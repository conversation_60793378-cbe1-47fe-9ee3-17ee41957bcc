<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>短信系统测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        .form-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .input-group {
            display: flex;
            gap: 10px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        .btn-danger {
            background: #dc3545;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-item {
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        .log-container {
            max-height: 300px;
            overflow-y: auto;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }
        .log-success {
            background: #d4edda;
            color: #155724;
        }
        .log-error {
            background: #f8d7da;
            color: #721c24;
        }
        .log-info {
            background: #d1ecf1;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>📱 短信系统完整测试</h1>
        <p>测试短信发送、验证、配置等功能</p>
    </div>

    <div class="test-section">
        <h3>1. 数据库初始化</h3>
        <p>创建短信相关数据表和配置</p>
        <button class="btn" onclick="initDatabase()">初始化数据库</button>
        <button class="btn btn-warning" onclick="checkTables()">检查表结构</button>
        <div id="db-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. 短信发送测试</h3>
        <div class="form-group">
            <label class="form-label">手机号码</label>
            <input type="tel" id="testPhone" class="form-input" placeholder="请输入手机号码" value="13800138000">
        </div>
        <div class="form-group">
            <label class="form-label">验证码类型</label>
            <select id="testType" class="form-input">
                <option value="login">登录验证</option>
                <option value="register">注册验证</option>
                <option value="reset_password">重置密码</option>
                <option value="bind_phone">绑定手机</option>
                <option value="security">安全验证</option>
            </select>
        </div>
        <button class="btn" onclick="sendTestSMS()">发送测试短信</button>
        <button class="btn btn-success" onclick="checkPhone()">检查手机号</button>
        <div id="sms-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. 验证码验证测试</h3>
        <div class="input-group">
            <input type="text" id="verifyCode" class="form-input" placeholder="请输入6位验证码" maxlength="6">
            <button class="btn btn-success" onclick="verifyTestCode()">验证验证码</button>
        </div>
        <div id="verify-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>4. 系统统计</h3>
        <button class="btn btn-warning" onclick="getStats()">获取统计信息</button>
        <div class="stats-grid" id="statsGrid" style="display: none;">
            <div class="stat-item">
                <div class="stat-value" id="totalSent">0</div>
                <div class="stat-label">总发送量</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="successRate">0%</div>
                <div class="stat-label">成功率</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="todaySent">0</div>
                <div class="stat-label">今日发送</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="uniquePhones">0</div>
                <div class="stat-label">唯一手机号</div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h3>5. 实时日志</h3>
        <button class="btn btn-danger" onclick="clearLog()">清空日志</button>
        <div id="logContainer" class="log-container"></div>
    </div>

    <script src="js/sms.js"></script>
    <script>
    let testPhone = '';
    let testType = 'login';

    function showResult(elementId, message, isSuccess = true) {
        const resultDiv = document.getElementById(elementId);
        resultDiv.className = 'result ' + (isSuccess ? 'success' : 'error');
        resultDiv.innerHTML = message;
        resultDiv.style.display = 'block';
        
        addLog(message, isSuccess ? 'success' : 'error');
    }

    function addLog(message, type = 'info') {
        const logContainer = document.getElementById('logContainer');
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry log-${type}`;
        logEntry.textContent = `[${timestamp}] ${message}`;
        
        logContainer.appendChild(logEntry);
        logContainer.scrollTop = logContainer.scrollHeight;
    }

    function clearLog() {
        document.getElementById('logContainer').innerHTML = '';
    }

    async function initDatabase() {
        try {
            addLog('正在初始化数据库...', 'info');
            
            const response = await fetch('create-sms-tables.php');
            const data = await response.json();
            
            if (data.success) {
                let message = '<h4>数据库初始化成功</h4>';
                message += '<ul>';
                data.created_tables.forEach(table => {
                    message += `<li>创建表: ${table}</li>`;
                });
                message += '</ul>';
                showResult('db-result', message, true);
            } else {
                showResult('db-result', `初始化失败: ${data.error}`, false);
            }
        } catch (error) {
            showResult('db-result', `网络错误: ${error.message}`, false);
        }
    }

    async function checkTables() {
        try {
            addLog('检查数据表结构...', 'info');
            
            const response = await fetch('debug-purchase.php');
            const data = await response.json();
            
            if (data.success) {
                let message = '<h4>数据表检查结果</h4>';
                message += `<p>所有表: ${data.all_tables.join(', ')}</p>`;
                showResult('db-result', message, true);
            } else {
                showResult('db-result', `检查失败: ${data.error}`, false);
            }
        } catch (error) {
            showResult('db-result', `检查失败: ${error.message}`, false);
        }
    }

    async function sendTestSMS() {
        testPhone = document.getElementById('testPhone').value.trim();
        testType = document.getElementById('testType').value;
        
        if (!testPhone) {
            showResult('sms-result', '请输入手机号码', false);
            return;
        }
        
        if (!/^1[3-9]\d{9}$/.test(testPhone)) {
            showResult('sms-result', '请输入正确的手机号码', false);
            return;
        }
        
        try {
            addLog(`向 ${testPhone} 发送 ${testType} 类型验证码...`, 'info');
            
            const formData = new FormData();
            formData.append('action', 'send');
            formData.append('phone', testPhone);
            formData.append('type', testType);
            
            const response = await fetch('api/sms.php', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (data.success) {
                showResult('sms-result', `短信发送成功！${data.message}`, true);
            } else {
                showResult('sms-result', `发送失败: ${data.message}`, false);
            }
        } catch (error) {
            showResult('sms-result', `网络错误: ${error.message}`, false);
        }
    }

    async function checkPhone() {
        const phone = document.getElementById('testPhone').value.trim();
        
        if (!phone) {
            showResult('sms-result', '请输入手机号码', false);
            return;
        }
        
        try {
            addLog(`检查手机号 ${phone} 是否已注册...`, 'info');
            
            const formData = new FormData();
            formData.append('action', 'check_phone');
            formData.append('phone', phone);
            
            const response = await fetch('api/sms.php', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (data.success) {
                const status = data.exists ? '已注册' : '未注册';
                showResult('sms-result', `手机号 ${phone} ${status}`, true);
            } else {
                showResult('sms-result', `检查失败: ${data.message}`, false);
            }
        } catch (error) {
            showResult('sms-result', `检查失败: ${error.message}`, false);
        }
    }

    async function verifyTestCode() {
        const code = document.getElementById('verifyCode').value.trim();
        
        if (!testPhone) {
            showResult('verify-result', '请先发送验证码', false);
            return;
        }
        
        if (!code) {
            showResult('verify-result', '请输入验证码', false);
            return;
        }
        
        if (!/^\d{6}$/.test(code)) {
            showResult('verify-result', '验证码格式错误', false);
            return;
        }
        
        try {
            addLog(`验证手机号 ${testPhone} 的验证码 ${code}...`, 'info');
            
            const formData = new FormData();
            formData.append('action', 'verify');
            formData.append('phone', testPhone);
            formData.append('code', code);
            formData.append('type', testType);
            
            const response = await fetch('api/sms.php', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (data.success) {
                showResult('verify-result', '验证码验证成功！', true);
            } else {
                showResult('verify-result', `验证失败: ${data.message}`, false);
            }
        } catch (error) {
            showResult('verify-result', `验证失败: ${error.message}`, false);
        }
    }

    async function getStats() {
        try {
            addLog('获取短信统计信息...', 'info');
            
            const formData = new FormData();
            formData.append('action', 'get_stats');
            formData.append('phone', '');
            
            const response = await fetch('api/sms.php', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (data.success) {
                const stats = data.stats;
                document.getElementById('totalSent').textContent = stats.total || 0;
                document.getElementById('successRate').textContent = 
                    stats.total > 0 ? Math.round((stats.success / stats.total) * 100) + '%' : '0%';
                document.getElementById('todaySent').textContent = stats.recent_orders || 0;
                document.getElementById('uniquePhones').textContent = stats.unique_phones || 0;
                
                document.getElementById('statsGrid').style.display = 'grid';
                addLog('统计信息获取成功', 'success');
            } else {
                addLog(`获取统计失败: ${data.message}`, 'error');
            }
        } catch (error) {
            addLog(`获取统计失败: ${error.message}`, 'error');
        }
    }

    // 页面加载时的初始化
    document.addEventListener('DOMContentLoaded', function() {
        addLog('短信系统测试页面已加载', 'info');
        
        // 自动检查数据库状态
        setTimeout(checkTables, 1000);
    });
    </script>
</body>
</html>
