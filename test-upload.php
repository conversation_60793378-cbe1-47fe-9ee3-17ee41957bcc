<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片上传测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 32px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .upload-form {
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-label {
            display: block;
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
        }
        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        .btn {
            background: #ff6f06;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }
        .btn:hover {
            background: #e55a00;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 13px;
            white-space: pre-wrap;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .preview {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        .preview img {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 6px;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>图片上传功能测试</h1>
        
        <?php
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            echo '<div class="result">';
            
            // 检查是否有文件上传
            if (isset($_FILES['images']) && !empty($_FILES['images']['tmp_name'][0])) {
                echo "收到文件上传请求\n";
                echo "文件数量: " . count($_FILES['images']['tmp_name']) . "\n\n";
                
                // 确保uploads目录存在
                $uploadDir = 'uploads/';
                if (!is_dir($uploadDir)) {
                    mkdir($uploadDir, 0755, true);
                    echo "创建uploads目录\n";
                }
                
                $uploadedFiles = [];
                $errors = [];
                
                foreach ($_FILES['images']['tmp_name'] as $key => $tmpName) {
                    if (!empty($tmpName) && $_FILES['images']['error'][$key] === UPLOAD_ERR_OK) {
                        $fileName = $_FILES['images']['name'][$key];
                        $fileSize = $_FILES['images']['size'][$key];
                        $fileType = $_FILES['images']['type'][$key];
                        
                        echo "处理文件 $key: $fileName\n";
                        echo "  大小: " . round($fileSize / 1024, 2) . " KB\n";
                        echo "  类型: $fileType\n";
                        
                        // 检查文件类型
                        $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                        if (!in_array($fileType, $allowedTypes)) {
                            $errors[] = "$fileName: 不支持的文件类型";
                            continue;
                        }
                        
                        // 检查文件大小 (5MB)
                        if ($fileSize > 5 * 1024 * 1024) {
                            $errors[] = "$fileName: 文件过大";
                            continue;
                        }
                        
                        // 生成新文件名
                        $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
                        $newFileName = uniqid() . '.' . $fileExtension;
                        $uploadPath = $uploadDir . $newFileName;
                        
                        // 移动文件
                        if (move_uploaded_file($tmpName, $uploadPath)) {
                            $uploadedFiles[] = $newFileName;
                            echo "  ✅ 上传成功: $newFileName\n";
                        } else {
                            $errors[] = "$fileName: 移动文件失败";
                            echo "  ❌ 上传失败\n";
                        }
                    } else {
                        echo "文件 $key: 跳过 (错误代码: " . $_FILES['images']['error'][$key] . ")\n";
                    }
                    echo "\n";
                }
                
                echo "=== 上传结果 ===\n";
                echo "成功上传: " . count($uploadedFiles) . " 个文件\n";
                echo "失败: " . count($errors) . " 个文件\n";
                
                if (!empty($errors)) {
                    echo "\n错误信息:\n";
                    foreach ($errors as $error) {
                        echo "- $error\n";
                    }
                }
                
                if (!empty($uploadedFiles)) {
                    echo "\n上传的文件:\n";
                    foreach ($uploadedFiles as $file) {
                        echo "- $file\n";
                    }
                    
                    echo '</div>';
                    echo '<div class="preview">';
                    foreach ($uploadedFiles as $file) {
                        echo '<img src="uploads/' . $file . '" alt="上传的图片">';
                    }
                    echo '</div>';
                } else {
                    echo '</div>';
                }
                
            } else {
                echo '<div class="result error">没有收到文件上传</div>';
            }
        }
        ?>
        
        <form class="upload-form" method="POST" enctype="multipart/form-data">
            <div class="form-group">
                <label class="form-label">选择图片文件</label>
                <input type="file" class="form-input" name="images[]" multiple accept="image/*" required>
            </div>
            
            <div class="form-group">
                <label class="form-label">商品标题（测试）</label>
                <input type="text" class="form-input" name="title" placeholder="输入商品标题" value="测试商品">
            </div>
            
            <button type="submit" class="btn">测试上传</button>
        </form>
        
        <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
            <h3>测试说明</h3>
            <ul>
                <li>选择一个或多个图片文件</li>
                <li>支持 JPG、PNG、GIF 格式</li>
                <li>单个文件不超过 5MB</li>
                <li>上传成功后会显示预览图</li>
            </ul>
        </div>
    </div>
</body>
</html>
