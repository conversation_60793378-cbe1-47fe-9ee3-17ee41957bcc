<?php
// 先处理所有逻辑，再包含header
require_once 'includes/functions.php';

// 检查登录状态
if (!isLoggedIn()) {
    $_SESSION['error_message'] = '请先登录后再发布商品';
    redirect('login.php');
}

$categories = getCategories();
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $title = sanitizeInput($_POST['productTitle']);
    $description = sanitizeInput($_POST['productDescription']);
    $price = floatval($_POST['productPrice']);
    $originalPrice = !empty($_POST['originalPrice']) ? floatval($_POST['originalPrice']) : null;
    $categoryId = intval($_POST['virtualCategory']);
    $stock = intval($_POST['inventory']);
    $isVirtual = ($_POST['productType'] ?? 'virtual') === 'virtual';
    
    // 验证必填字段
    if (empty($title) || empty($description) || $price <= 0 || $categoryId <= 0) {
        $error = '请填写完整的商品信息';
    } else {
        try {
            // 处理图片上传
            $images = [];
            if (isset($_FILES['images'])) {
                // 确保uploads目录存在
                if (!is_dir(UPLOAD_PATH)) {
                    mkdir(UPLOAD_PATH, 0755, true);
                }

                foreach ($_FILES['images']['tmp_name'] as $key => $tmpName) {
                    if (!empty($tmpName) && $_FILES['images']['error'][$key] === UPLOAD_ERR_OK) {
                        $file = [
                            'name' => $_FILES['images']['name'][$key],
                            'tmp_name' => $tmpName,
                            'size' => $_FILES['images']['size'][$key],
                            'error' => $_FILES['images']['error'][$key]
                        ];

                        $uploadedFile = uploadFile($file);
                        if ($uploadedFile) {
                            $images[] = $uploadedFile;
                        } else {
                            $error = '图片上传失败：' . $_FILES['images']['name'][$key];
                        }
                    }
                }
            }
            
            // 插入商品数据
            $stmt = $pdo->prepare("
                INSERT INTO products (user_id, category_id, title, description, price, original_price, stock, is_virtual, images) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $_SESSION['user_id'],
                $categoryId,
                $title,
                $description,
                $price,
                $originalPrice,
                $stock,
                $isVirtual,
                json_encode($images)
            ]);
            
            $productId = $pdo->lastInsertId();
            
            // 如果是虚拟商品，插入虚拟商品属性
            if ($isVirtual) {
                $usagePeriod = $_POST['usagePeriod'] ?? 'unlimited';
                $periodValue = !empty($_POST['periodValue']) ? intval($_POST['periodValue']) : null;
                $periodUnit = $_POST['periodUnit'] ?? null;
                $deliveryMethod = $_POST['deliveryMethod'] ?? 'manual';
                $platform = sanitizeInput($_POST['platform'] ?? '');
                $textContent = sanitizeInput($_POST['textContent'] ?? '');
                $instructions = sanitizeInput($_POST['instructions'] ?? '');
                
                // 处理服务选项
                $services = [];
                if (isset($_POST['serviceRefund'])) $services[] = 'refund';
                if (isset($_POST['serviceReplace'])) $services[] = 'replace';
                if (isset($_POST['serviceConsult'])) $services[] = 'consult';
                if (isset($_POST['serviceInvoice'])) $services[] = 'invoice';
                
                $stmt = $pdo->prepare("
                    INSERT INTO virtual_attributes 
                    (product_id, usage_period, period_value, period_unit, delivery_method, platform, content_text, instructions, services) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                
                $stmt->execute([
                    $productId,
                    $usagePeriod,
                    $periodValue,
                    $periodUnit,
                    $deliveryMethod,
                    $platform,
                    $textContent,
                    $instructions,
                    json_encode($services)
                ]);
            }
            
            $_SESSION['success_message'] = '商品发布成功！';
            redirect('detail.php?id=' . $productId);

        } catch (Exception $e) {
            $error = '发布失败：' . $e->getMessage();
        }
    }
}

// 现在包含header，因为所有逻辑处理已完成
$pageTitle = '发布商品';
$additionalCSS = ['css/publish.css', 'css/publish-enhanced.css'];
$additionalJS = ['js/publish.js'];
require_once 'includes/header.php';
?>

<!-- 主要内容区 -->
<main class="main publish-page">
    <div class="container">
        <div class="publish-container">
            <h1 class="page-title">发布商品</h1>
            
            <?php if ($error): ?>
                <div class="alert alert-error"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <form class="publish-form" id="publishForm" method="POST" enctype="multipart/form-data">
                <!-- 发布提示 -->
                <div class="publish-notice">
                    <div class="notice-icon">
                        <i class="bi bi-info-circle"></i>
                    </div>
                    <div class="notice-content">
                        <h4>发布虚拟商品</h4>
                        <p>本平台专注于虚拟商品交易，包括游戏道具、软件激活码、数字内容等</p>
                    </div>
                </div>

                <!-- 隐藏的商品类型字段，默认为虚拟商品 -->
                <input type="hidden" name="productType" value="virtual">

                <!-- 虚拟商品分类 -->
                <div class="form-group" id="virtualCategoryGroup">
                    <label class="form-label">商品分类</label>
                    <div class="virtual-category-selector">
                        <select class="form-select" id="virtualCategory" name="virtualCategory" required>
                            <option value="">请选择商品分类</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                
                <!-- 商品标题 -->
                <div class="form-group">
                    <label class="form-label">商品标题</label>
                    <input type="text" class="form-input" id="productTitle" name="productTitle" placeholder="请输入商品标题，最多30字" maxlength="30" required>
                    <div class="input-tips">标题越详细，越容易被买家搜索到</div>
                </div>
                
                <!-- 商品图片 -->
                <div class="form-group">
                    <label class="form-label">商品图片</label>
                    <div class="image-uploader">
                        <div class="upload-area" id="uploadArea">
                            <div class="upload-preview" id="uploadPreview">
                                <div class="upload-item add-image" id="addImageBtn">
                                    <div class="upload-icon">
                                        <i class="bi bi-cloud-upload"></i>
                                    </div>
                                    <div class="upload-text">
                                        <span class="upload-title">点击上传图片</span>
                                        <span class="upload-subtitle">支持 JPG、PNG、GIF 格式</span>
                                    </div>
                                    <input type="file" class="file-input" id="imageUpload" name="images[]" multiple accept="image/*">
                                </div>
                            </div>
                        </div>
                        <div class="upload-tips">
                            <div class="tip-item">
                                <i class="bi bi-check-circle"></i>
                                <span>最多上传9张图片，单张不超过5MB</span>
                            </div>
                            <div class="tip-item">
                                <i class="bi bi-lightbulb"></i>
                                <span>建议上传商品截图、使用效果图等，提高成交率</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 商品描述 -->
                <div class="form-group">
                    <label class="form-label">商品描述</label>
                    <textarea class="form-textarea" id="productDescription" name="productDescription" placeholder="请详细描述商品的内容、用途、使用方法等信息，让买家更好地了解商品" rows="6" required></textarea>
                </div>

                <!-- 虚拟商品属性 -->
                <div class="form-group" id="virtualAttributesGroup">
                    <label class="form-label">商品属性</label>
                    <div class="virtual-attributes">
                        <!-- 使用期限 -->
                        <div class="attribute-item">
                            <label class="attribute-label">使用期限</label>
                            <div class="attribute-content">
                                <select class="form-select" id="usagePeriod" name="usagePeriod">
                                    <option value="unlimited">永久有效</option>
                                    <option value="limited">限时有效</option>
                                </select>
                                <div class="period-input" id="periodInput" style="display: none;">
                                    <input type="number" class="form-input" id="periodValue" name="periodValue" min="1" placeholder="数量">
                                    <select class="form-select" id="periodUnit" name="periodUnit">
                                        <option value="day">天</option>
                                        <option value="month">月</option>
                                        <option value="year">年</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 发货方式 -->
                        <div class="attribute-item">
                            <label class="attribute-label">发货方式</label>
                            <div class="attribute-content">
                                <select class="form-select" id="deliveryMethod" name="deliveryMethod">
                                    <option value="automatic">自动发货</option>
                                    <option value="manual">手动发货</option>
                                </select>
                            </div>
                        </div>

                        <!-- 使用平台 -->
                        <div class="attribute-item">
                            <label class="attribute-label">使用平台</label>
                            <div class="attribute-content">
                                <input type="text" class="form-input" id="platform" name="platform" placeholder="如：iOS/安卓/Windows/Mac等">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 商品内容 -->
                <div class="form-group" id="contentGroup">
                    <label class="form-label">商品内容</label>
                    <div class="content-tabs">
                        <div class="content-tab active" data-tab="contentText">
                            <i class="bi bi-file-text"></i>
                            <span>文本内容</span>
                        </div>
                        <div class="content-tab" data-tab="contentFile">
                            <i class="bi bi-file-earmark-arrow-up"></i>
                            <span>附件内容</span>
                        </div>
                    </div>

                    <div class="content-panels">
                        <!-- 文本内容面板 -->
                        <div class="content-panel active" id="contentText">
                            <textarea class="form-textarea" id="textContent" name="textContent" placeholder="请输入激活码、账号密码等文本内容，买家付款后可见" rows="6"></textarea>
                            <div class="input-tips">
                                <i class="bi bi-shield-lock"></i>
                                该内容仅在买家付款后可见，请勿在商品描述中透露
                            </div>
                        </div>

                        <!-- 附件内容面板 -->
                        <div class="content-panel" id="contentFile">
                            <div class="file-uploader">
                                <div class="upload-btn" id="fileUploadBtn">
                                    <i class="bi bi-file-earmark-arrow-up"></i>
                                    <span>选择文件上传</span>
                                    <input type="file" class="file-input" id="fileUpload" name="contentFile">
                                </div>
                                <div class="upload-tips">
                                    <div class="tip-item">
                                        <i class="bi bi-check-circle"></i>
                                        <span>支持PDF、ZIP、RAR等格式，单个文件不超过50MB</span>
                                    </div>
                                    <div class="tip-item">
                                        <i class="bi bi-shield-lock"></i>
                                        <span>附件内容仅在买家付款后可下载</span>
                                    </div>
                                </div>
                            </div>
                            <div class="file-list" id="fileList">
                                <!-- 上传的文件将在这里显示 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 使用说明 -->
                <div class="form-group" id="instructionsGroup">
                    <label class="form-label">使用说明</label>
                    <textarea class="form-textarea" id="instructions" name="instructions" placeholder="请输入商品的使用方法、注意事项等信息，帮助买家更好地使用商品" rows="4"></textarea>
                </div>

                <!-- 售后服务 -->
                <div class="form-group" id="serviceGroup">
                    <label class="form-label">售后服务</label>
                    <div class="service-options">
                        <label class="checkbox-label">
                            <input type="checkbox" id="serviceRefund" name="serviceRefund">
                            <span class="checkmark"></span>
                            <span class="service-text">
                                <strong>支持退款</strong>
                                <small>商品有问题时支持退款</small>
                            </span>
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="serviceReplace" name="serviceReplace">
                            <span class="checkmark"></span>
                            <span class="service-text">
                                <strong>支持更换</strong>
                                <small>商品失效时支持更换</small>
                            </span>
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="serviceConsult" name="serviceConsult">
                            <span class="checkmark"></span>
                            <span class="service-text">
                                <strong>技术支持</strong>
                                <small>提供使用指导和技术咨询</small>
                            </span>
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="serviceInvoice" name="serviceInvoice">
                            <span class="checkmark"></span>
                            <span class="service-text">
                                <strong>提供发票</strong>
                                <small>可开具正规发票</small>
                            </span>
                        </label>
                    </div>
                    <textarea class="form-textarea" id="serviceDescription" name="serviceDescription" placeholder="请详细描述您提供的售后服务内容和政策，让买家更放心购买" rows="3"></textarea>
                </div>

                <!-- 商品价格 -->
                <div class="form-group">
                    <label class="form-label">商品价格</label>
                    <div class="price-input">
                        <span class="price-symbol">¥</span>
                        <input type="number" class="form-input" id="productPrice" name="productPrice" placeholder="0.00" min="0" step="0.01" required>
                    </div>
                </div>
                
                <!-- 原价展示 -->
                <div class="form-group">
                    <label class="form-label">原价展示（选填）</label>
                    <div class="price-input">
                        <span class="price-symbol">¥</span>
                        <input type="number" class="form-input" id="originalPrice" name="originalPrice" placeholder="0.00" min="0" step="0.01">
                    </div>
                </div>
                
                <!-- 库存数量 -->
                <div class="form-group">
                    <label class="form-label">库存数量</label>
                    <input type="number" class="form-input" id="inventory" name="inventory" placeholder="请输入库存数量" min="1" value="1" required>
                </div>
                
                <!-- 提交按钮 -->
                <div class="form-actions">
                    <button type="button" class="btn btn-outline" id="saveDraft">
                        <i class="bi bi-bookmark"></i>
                        <span>保存草稿</span>
                    </button>
                    <button type="submit" class="btn btn-primary" id="publishBtn">
                        <i class="bi bi-rocket-takeoff"></i>
                        <span>立即发布</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</main>

<script>
// 发布页面交互脚本
document.addEventListener('DOMContentLoaded', function() {
    // 初始化各个功能模块
    initImageUpload();
    initUsagePeriodToggle();
    initContentTabs();
    initFormValidation();
    initDragAndDrop();

    // 显示所有虚拟商品相关字段（因为现在只支持虚拟商品）
    const virtualCategoryGroup = document.getElementById('virtualCategoryGroup');
    const virtualAttributesGroup = document.getElementById('virtualAttributesGroup');
    const contentGroup = document.getElementById('contentGroup');
    const instructionsGroup = document.getElementById('instructionsGroup');
    const serviceGroup = document.getElementById('serviceGroup');

    if (virtualCategoryGroup) virtualCategoryGroup.style.display = 'block';
    if (virtualAttributesGroup) virtualAttributesGroup.style.display = 'block';
    if (contentGroup) contentGroup.style.display = 'block';
    if (instructionsGroup) instructionsGroup.style.display = 'block';
    if (serviceGroup) serviceGroup.style.display = 'block';

    // 初始化使用期限切换
    function initUsagePeriodToggle() {
        const usagePeriodSelect = document.getElementById('usagePeriod');
        const periodInput = document.getElementById('periodInput');

        if (usagePeriodSelect && periodInput) {
            usagePeriodSelect.addEventListener('change', function() {
                if (this.value === 'limited') {
                    periodInput.style.display = 'flex';
                    periodInput.classList.add('animate-fade-in');
                } else {
                    periodInput.style.display = 'none';
                }
            });
        }
    }

    // 初始化内容标签切换
    function initContentTabs() {
        const contentTabs = document.querySelectorAll('.content-tab');
        const contentPanels = document.querySelectorAll('.content-panel');

        contentTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                const tabType = this.dataset.tab;

                // 更新标签状态
                contentTabs.forEach(t => t.classList.remove('active'));
                this.classList.add('active');

                // 更新面板显示
                contentPanels.forEach(panel => {
                    panel.classList.remove('active');
                    if (panel.id === tabType) {
                        panel.classList.add('active');
                    }
                });
            });
        });
    }

    // 初始化图片上传
    function initImageUpload() {
        const imageUpload = document.getElementById('imageUpload');
        const uploadPreview = document.getElementById('uploadPreview');
        const addImageBtn = document.getElementById('addImageBtn');
        let uploadedImages = [];
        const maxImages = 9;

        if (imageUpload && uploadPreview) {
            imageUpload.addEventListener('change', function(e) {
                handleImageFiles(e.target.files);
                // 不重置input，保持文件选择
            });
        }

        function handleImageFiles(files) {
            // 清除之前的预览
            const existingPreviews = uploadPreview.querySelectorAll('.image-preview');
            existingPreviews.forEach(preview => preview.remove());
            uploadedImages = [];

            Array.from(files).forEach((file, index) => {
                if (index >= maxImages) {
                    showMessage('最多只能上传9张图片', 'warning');
                    return;
                }

                if (!file.type.startsWith('image/')) {
                    showMessage(`${file.name} 不是有效的图片文件`, 'error');
                    return;
                }

                if (file.size > 5 * 1024 * 1024) {
                    showMessage(`${file.name} 超过5MB限制`, 'error');
                    return;
                }

                createImagePreview(file, index);
                uploadedImages.push(file);
            });

            if (uploadedImages.length >= maxImages) {
                addImageBtn.style.display = 'none';
            } else {
                addImageBtn.style.display = 'flex';
            }
        }

        function createImagePreview(file, index) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const previewItem = document.createElement('div');
                previewItem.className = 'upload-item image-preview';
                previewItem.dataset.index = index;

                previewItem.innerHTML = `
                    <img src="${e.target.result}" alt="预览图片">
                    <div class="preview-overlay">
                        <button type="button" class="remove-btn" onclick="removeImage(this, ${index})">
                            <i class="bi bi-x-lg"></i>
                        </button>
                    </div>
                    <div class="image-info">
                        <span class="image-name">${file.name}</span>
                        <span class="image-size">${(file.size / 1024 / 1024).toFixed(2)}MB</span>
                    </div>
                `;

                uploadPreview.insertBefore(previewItem, addImageBtn);

                // 添加动画效果
                setTimeout(() => {
                    previewItem.classList.add('animate-scale-in');
                }, 10);
            };
            reader.readAsDataURL(file);
        }

        // 全局函数，供HTML调用
        window.removeImage = function(button, index) {
            const previewItem = button.closest('.upload-item');

            previewItem.classList.add('animate-fade-out');

            setTimeout(() => {
                previewItem.remove();

                // 从数组中移除对应的文件
                if (index >= 0 && index < uploadedImages.length) {
                    uploadedImages.splice(index, 1);
                }

                // 重新创建文件输入框的文件列表
                updateFileInput();

                if (uploadedImages.length < maxImages) {
                    addImageBtn.style.display = 'flex';
                }
            }, 300);
        };

        function updateFileInput() {
            // 创建新的DataTransfer对象
            const dt = new DataTransfer();
            uploadedImages.forEach(file => {
                dt.items.add(file);
            });
            imageUpload.files = dt.files;
        }
    }

    // 初始化拖拽上传
    function initDragAndDrop() {
        const uploadArea = document.getElementById('uploadArea');

        if (uploadArea) {
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                uploadArea.addEventListener(eventName, preventDefaults, false);
            });

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            ['dragenter', 'dragover'].forEach(eventName => {
                uploadArea.addEventListener(eventName, highlight, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                uploadArea.addEventListener(eventName, unhighlight, false);
            });

            function highlight(e) {
                uploadArea.classList.add('drag-over');
            }

            function unhighlight(e) {
                uploadArea.classList.remove('drag-over');
            }

            uploadArea.addEventListener('drop', handleDrop, false);

            function handleDrop(e) {
                const dt = e.dataTransfer;
                const files = dt.files;

                // 手动触发图片处理
                if (files.length > 0) {
                    const imageUpload = document.getElementById('imageUpload');
                    const changeEvent = new Event('change');
                    Object.defineProperty(changeEvent, 'target', {
                        value: { files: files },
                        enumerable: true
                    });
                    imageUpload.dispatchEvent(changeEvent);
                }
            }
        }
    }

    // 初始化表单验证
    function initFormValidation() {
        const form = document.getElementById('publishForm');
        const requiredFields = [
            { id: 'virtualCategory', message: '请选择商品分类' },
            { id: 'productTitle', message: '请输入商品标题' },
            { id: 'productDescription', message: '请输入商品描述' },
            { id: 'productPrice', message: '请输入商品价格' }
        ];

        // 添加实时验证
        requiredFields.forEach(field => {
            const element = document.getElementById(field.id);
            if (element) {
                element.addEventListener('blur', function() {
                    validateField(element, field.message);
                });

                element.addEventListener('input', function() {
                    if (element.classList.contains('input-error')) {
                        validateField(element, field.message);
                    }
                });
            }
        });

        function validateField(element, message) {
            const parent = element.closest('.form-group');
            const existingError = parent.querySelector('.error-message');

            if (existingError) {
                existingError.remove();
            }

            element.classList.remove('input-error');

            if (!element.value.trim()) {
                element.classList.add('input-error');

                const errorMessage = document.createElement('div');
                errorMessage.className = 'error-message';
                errorMessage.innerHTML = `<i class="bi bi-exclamation-circle"></i> ${message}`;
                parent.appendChild(errorMessage);

                return false;
            }

            return true;
        }

        // 表单提交验证
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            let isValid = true;
            requiredFields.forEach(field => {
                const element = document.getElementById(field.id);
                if (!validateField(element, field.message)) {
                    isValid = false;
                }
            });

            if (isValid) {
                // 显示提交状态
                const publishBtn = document.getElementById('publishBtn');
                const originalContent = publishBtn.innerHTML;
                publishBtn.innerHTML = '<i class="bi bi-arrow-repeat spin"></i> <span>发布中...</span>';
                publishBtn.disabled = true;

                // 提交表单到服务器
                form.submit();
            } else {
                showMessage('请完善必填信息', 'error');
            }
        });
    }

    // 显示消息提示
    function showMessage(message, type = 'info') {
        const messageElement = document.createElement('div');
        messageElement.className = `message-toast ${type}`;
        messageElement.innerHTML = `
            <div class="message-icon">
                <i class="bi ${getIconByType(type)}"></i>
            </div>
            <div class="message-content">${message}</div>
        `;

        document.body.appendChild(messageElement);

        setTimeout(() => {
            messageElement.classList.add('show');
        }, 10);

        setTimeout(() => {
            messageElement.classList.remove('show');
            setTimeout(() => {
                if (messageElement.parentNode) {
                    messageElement.parentNode.removeChild(messageElement);
                }
            }, 300);
        }, 3000);

        function getIconByType(type) {
            switch (type) {
                case 'success': return 'bi-check-circle-fill';
                case 'error': return 'bi-x-circle-fill';
                case 'warning': return 'bi-exclamation-triangle-fill';
                default: return 'bi-info-circle-fill';
            }
        }
    }
});
</script>

<?php require_once 'includes/footer.php'; ?>
