<?php
// 先处理所有逻辑，再包含header
require_once 'includes/functions.php';

// 检查登录状态
if (!isLoggedIn()) {
    $_SESSION['error_message'] = '请先登录';
    redirect('login.php');
}

$currentUser = getCurrentUser();

// 处理购物车操作
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    $productId = intval($_POST['product_id'] ?? 0);
    $quantity = intval($_POST['quantity'] ?? 1);

    try {
        switch ($action) {
            case 'add':
                // 添加到购物车
                $stmt = $pdo->prepare("
                    INSERT INTO cart (user_id, product_id, quantity) 
                    VALUES (?, ?, ?) 
                    ON DUPLICATE KEY UPDATE quantity = quantity + VALUES(quantity)
                ");
                $stmt->execute([$_SESSION['user_id'], $productId, $quantity]);
                $_SESSION['success_message'] = '商品已添加到购物车';
                break;

            case 'update':
                // 更新数量
                if ($quantity > 0) {
                    $stmt = $pdo->prepare("UPDATE cart SET quantity = ? WHERE user_id = ? AND product_id = ?");
                    $stmt->execute([$quantity, $_SESSION['user_id'], $productId]);
                } else {
                    $stmt = $pdo->prepare("DELETE FROM cart WHERE user_id = ? AND product_id = ?");
                    $stmt->execute([$_SESSION['user_id'], $productId]);
                }
                break;

            case 'remove':
                // 移除商品
                $stmt = $pdo->prepare("DELETE FROM cart WHERE user_id = ? AND product_id = ?");
                $stmt->execute([$_SESSION['user_id'], $productId]);
                $_SESSION['success_message'] = '商品已从购物车移除';
                break;

            case 'clear':
                // 清空购物车
                $stmt = $pdo->prepare("DELETE FROM cart WHERE user_id = ?");
                $stmt->execute([$_SESSION['user_id']]);
                $_SESSION['success_message'] = '购物车已清空';
                break;
        }
    } catch (Exception $e) {
        $_SESSION['error_message'] = '操作失败：' . $e->getMessage();
    }

    if ($action !== 'update') {
        redirect('cart.php');
    }
}

// 获取购物车商品
try {
    $stmt = $pdo->prepare("
        SELECT c.*, p.title, p.price, p.stock, p.images, p.status,
               u.nickname as seller_name, u.avatar as seller_avatar
        FROM cart c
        JOIN products p ON c.product_id = p.id
        JOIN users u ON p.user_id = u.id
        WHERE c.user_id = ? AND p.status = 'active'
        ORDER BY c.created_at DESC
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $cartItems = $stmt->fetchAll();
} catch (Exception $e) {
    $cartItems = [];
    $error = '获取购物车数据失败：' . $e->getMessage();
}

// 计算总价
$totalPrice = 0;
$totalItems = 0;
foreach ($cartItems as $item) {
    $totalPrice += $item['price'] * $item['quantity'];
    $totalItems += $item['quantity'];
}

// 现在包含header，因为所有逻辑处理已完成
$pageTitle = '购物车';
$additionalCSS = ['css/member.css', 'css/cart.css'];
require_once 'includes/header.php';
?>

<!-- 主要内容区 -->
<main class="main member-page">
    <div class="container">
        <div class="member-container">
            <!-- 侧边栏 -->
            <?php include 'includes/member-sidebar.php'; ?>

            <!-- 主内容区 -->
            <div class="member-content">
                <!-- 页面标题 -->
                <div class="page-header">
                    <div class="page-title-section">
                        <h1 class="page-title">购物车</h1>
                        <p class="page-subtitle">
                            共 <?php echo count($cartItems); ?> 件商品
                            <?php if ($totalItems > 0): ?>
                                ，总计 <?php echo $totalItems; ?> 个
                            <?php endif; ?>
                        </p>
                    </div>
                    <?php if (!empty($cartItems)): ?>
                        <div class="page-actions">
                            <button class="btn btn-outline" onclick="clearCart()">
                                <i class="bi bi-trash"></i>
                                <span>清空购物车</span>
                            </button>
                        </div>
                    <?php endif; ?>
                </div>

                <?php if (isset($error)): ?>
                    <div class="alert alert-error"><?php echo $error; ?></div>
                <?php endif; ?>

                <?php if (empty($cartItems)): ?>
                    <!-- 空购物车状态 -->
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="bi bi-cart-x"></i>
                        </div>
                        <h3 class="empty-title">购物车是空的</h3>
                        <p class="empty-description">快去挑选您喜欢的虚拟商品吧！</p>
                        <a href="index.php" class="btn btn-primary">
                            <i class="bi bi-shop"></i>
                            <span>去购物</span>
                        </a>
                    </div>
                <?php else: ?>
                    <!-- 购物车商品列表 -->
                    <div class="cart-container">
                        <div class="cart-items">
                            <?php foreach ($cartItems as $item): ?>
                                <div class="cart-item" data-product-id="<?php echo $item['product_id']; ?>">
                                    <div class="item-checkbox">
                                        <input type="checkbox" class="item-select" checked>
                                    </div>
                                    
                                    <div class="item-image">
                                        <?php 
                                        $images = json_decode($item['images'], true);
                                        $firstImage = !empty($images) ? $images[0] : 'images/placeholder.jpg';
                                        ?>
                                        <img src="<?php echo UPLOAD_PATH . $firstImage; ?>" alt="<?php echo htmlspecialchars($item['title']); ?>">
                                    </div>
                                    
                                    <div class="item-info">
                                        <h3 class="item-title">
                                            <a href="product.php?id=<?php echo $item['product_id']; ?>">
                                                <?php echo htmlspecialchars($item['title']); ?>
                                            </a>
                                        </h3>
                                        <div class="item-seller">
                                            <img src="<?php echo $item['seller_avatar'] ?: 'images/avatar-default.svg'; ?>" alt="卖家头像">
                                            <span><?php echo htmlspecialchars($item['seller_name']); ?></span>
                                        </div>
                                    </div>
                                    
                                    <div class="item-price">
                                        <span class="price">¥<?php echo number_format($item['price'], 2); ?></span>
                                    </div>
                                    
                                    <div class="item-quantity">
                                        <div class="quantity-control">
                                            <button class="quantity-btn minus" onclick="updateQuantity(<?php echo $item['product_id']; ?>, -1)">-</button>
                                            <input type="number" class="quantity-input" value="<?php echo $item['quantity']; ?>" min="1" max="<?php echo $item['stock']; ?>" onchange="updateQuantity(<?php echo $item['product_id']; ?>, this.value, true)">
                                            <button class="quantity-btn plus" onclick="updateQuantity(<?php echo $item['product_id']; ?>, 1)">+</button>
                                        </div>
                                    </div>
                                    
                                    <div class="item-total">
                                        <span class="total-price">¥<?php echo number_format($item['price'] * $item['quantity'], 2); ?></span>
                                    </div>
                                    
                                    <div class="item-actions">
                                        <button class="btn-icon" onclick="removeFromCart(<?php echo $item['product_id']; ?>)" title="移除">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <!-- 购物车底部操作栏 -->
                        <div class="cart-footer">
                            <div class="cart-summary">
                                <div class="select-all">
                                    <input type="checkbox" id="selectAll" checked>
                                    <label for="selectAll">全选</label>
                                </div>
                                
                                <div class="summary-info">
                                    <span class="selected-count">已选择 <span id="selectedCount"><?php echo count($cartItems); ?></span> 件商品</span>
                                    <span class="total-amount">合计：¥<span id="totalAmount"><?php echo number_format($totalPrice, 2); ?></span></span>
                                </div>
                                
                                <button class="btn btn-primary btn-large" onclick="checkout()">
                                    <i class="bi bi-credit-card"></i>
                                    <span>结算</span>
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</main>

<script>
// 购物车交互脚本
document.addEventListener('DOMContentLoaded', function() {
    initCartInteractions();
});

function initCartInteractions() {
    // 全选/取消全选
    const selectAllCheckbox = document.getElementById('selectAll');
    const itemCheckboxes = document.querySelectorAll('.item-select');
    
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            itemCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateCartSummary();
        });
    }
    
    // 单个商品选择
    itemCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectAllState();
            updateCartSummary();
        });
    });
    
    updateCartSummary();
}

function updateSelectAllState() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const itemCheckboxes = document.querySelectorAll('.item-select');
    const checkedCount = document.querySelectorAll('.item-select:checked').length;
    
    if (selectAllCheckbox) {
        selectAllCheckbox.checked = checkedCount === itemCheckboxes.length;
        selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < itemCheckboxes.length;
    }
}

function updateCartSummary() {
    const selectedItems = document.querySelectorAll('.item-select:checked');
    const selectedCountElement = document.getElementById('selectedCount');
    const totalAmountElement = document.getElementById('totalAmount');
    
    let totalAmount = 0;
    
    selectedItems.forEach(checkbox => {
        const cartItem = checkbox.closest('.cart-item');
        const priceElement = cartItem.querySelector('.item-price .price');
        const quantityElement = cartItem.querySelector('.quantity-input');
        
        if (priceElement && quantityElement) {
            const price = parseFloat(priceElement.textContent.replace('¥', '').replace(',', ''));
            const quantity = parseInt(quantityElement.value);
            totalAmount += price * quantity;
        }
    });
    
    if (selectedCountElement) {
        selectedCountElement.textContent = selectedItems.length;
    }
    
    if (totalAmountElement) {
        totalAmountElement.textContent = totalAmount.toFixed(2);
    }
}

function updateQuantity(productId, change, absolute = false) {
    const cartItem = document.querySelector(`[data-product-id="${productId}"]`);
    const quantityInput = cartItem.querySelector('.quantity-input');
    const currentQuantity = parseInt(quantityInput.value);
    const maxQuantity = parseInt(quantityInput.max);
    
    let newQuantity;
    if (absolute) {
        newQuantity = Math.max(1, Math.min(maxQuantity, parseInt(change)));
    } else {
        newQuantity = Math.max(1, Math.min(maxQuantity, currentQuantity + change));
    }
    
    if (newQuantity !== currentQuantity) {
        // 发送AJAX请求更新数量
        const formData = new FormData();
        formData.append('action', 'update');
        formData.append('product_id', productId);
        formData.append('quantity', newQuantity);
        
        fetch('cart.php', {
            method: 'POST',
            body: formData
        }).then(() => {
            quantityInput.value = newQuantity;
            
            // 更新单项总价
            const priceElement = cartItem.querySelector('.item-price .price');
            const totalElement = cartItem.querySelector('.total-price');
            if (priceElement && totalElement) {
                const price = parseFloat(priceElement.textContent.replace('¥', '').replace(',', ''));
                totalElement.textContent = '¥' + (price * newQuantity).toFixed(2);
            }
            
            updateCartSummary();
        });
    }
}

function removeFromCart(productId) {
    if (confirm('确定要移除这件商品吗？')) {
        const formData = new FormData();
        formData.append('action', 'remove');
        formData.append('product_id', productId);
        
        fetch('cart.php', {
            method: 'POST',
            body: formData
        }).then(() => {
            location.reload();
        });
    }
}

function clearCart() {
    if (confirm('确定要清空购物车吗？')) {
        const formData = new FormData();
        formData.append('action', 'clear');
        
        fetch('cart.php', {
            method: 'POST',
            body: formData
        }).then(() => {
            location.reload();
        });
    }
}

function checkout() {
    const selectedItems = document.querySelectorAll('.item-select:checked');
    if (selectedItems.length === 0) {
        alert('请选择要结算的商品');
        return;
    }
    
    // 这里可以跳转到结算页面
    alert('结算功能开发中...');
}
</script>

<?php require_once 'includes/footer.php'; ?>
