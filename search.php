<?php
$search = $_GET['q'] ?? '';
$pageTitle = $search ? "搜索结果 - $search" : '搜索';
require_once 'includes/header.php';

$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// 获取搜索结果
$products = [];
$totalCount = 0;

if ($search) {
    $products = getProducts($limit, $offset, null, $search);
    
    // 获取搜索结果总数（简化版本）
    global $pdo;
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM products p WHERE p.status = 'active' AND (p.title LIKE ? OR p.description LIKE ?)");
    $stmt->execute(["%$search%", "%$search%"]);
    $totalCount = $stmt->fetchColumn();
}

$totalPages = ceil($totalCount / $limit);
?>

<!-- 主要内容区 -->
<main class="main search-page">
    <div class="container">
        <!-- 搜索头部 -->
        <div class="search-header">
            <div class="search-box">
                <form action="search.php" method="GET">
                    <input type="text" name="q" placeholder="搜索你想要的虚拟商品" value="<?php echo htmlspecialchars($search); ?>">
                    <button type="submit" class="search-btn"><i class="bi bi-search"></i></button>
                </form>
            </div>
        </div>

        <?php if ($search): ?>
            <!-- 搜索结果信息 -->
            <div class="search-result-info">
                找到 <strong><?php echo $totalCount; ?></strong> 件与 <strong>"<?php echo htmlspecialchars($search); ?>"</strong> 相关的商品
            </div>

            <!-- 搜索结果分类 -->
            <div class="search-tabs">
                <div class="search-tab active">商品 <span class="search-tab-count">(<?php echo $totalCount; ?>)</span></div>
                <div class="search-tab">用户 <span class="search-tab-count">(0)</span></div>
            </div>

            <!-- 筛选条件 -->
            <div class="filter-section">
                <div class="filter-group">
                    <div class="filter-label">价格：</div>
                    <div class="filter-options">
                        <a href="#" class="active">全部</a>
                        <a href="#">0-50元</a>
                        <a href="#">50-100元</a>
                        <a href="#">100-500元</a>
                        <a href="#">500-1000元</a>
                        <a href="#">1000元以上</a>
                        <div class="price-input">
                            <input type="text" placeholder="¥">
                            <span>-</span>
                            <input type="text" placeholder="¥">
                            <button>确定</button>
                        </div>
                    </div>
                </div>
                <div class="filter-group">
                    <div class="filter-label">服务：</div>
                    <div class="filter-options">
                        <label class="checkbox-label">
                            <input type="checkbox"> 官方认证
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox"> 即时交付
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox"> 售后保障
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox"> 担保交易
                        </label>
                    </div>
                </div>
            </div>

            <!-- 排序选项 -->
            <div class="sort-section">
                <div class="sort-options">
                    <a href="#" class="active">综合</a>
                    <a href="#">最新发布</a>
                    <a href="#">价格 <i class="bi bi-arrow-down-up"></i></a>
                    <a href="#">销量 <i class="bi bi-arrow-down"></i></a>
                </div>
                <div class="view-switch">
                    <span class="view-count">共<?php echo $totalCount; ?>件商品</span>
                    <a href="#" class="grid-view active"><i class="bi bi-grid"></i></a>
                    <a href="#" class="list-view"><i class="bi bi-list"></i></a>
                </div>
            </div>

            <!-- 搜索结果列表 -->
            <div class="product-grid list-grid">
                <?php foreach ($products as $product): ?>
                    <div class="product-card">
                        <a href="detail.php?id=<?php echo $product['id']; ?>">
                            <div class="product-img">
                                <?php 
                                $images = json_decode($product['images'], true);
                                $firstImage = $images ? $images[0] : 'images/product-default.svg';
                                ?>
                                <img src="<?php echo $firstImage; ?>" alt="<?php echo htmlspecialchars($product['title']); ?>">
                                <?php if ($product['is_virtual']): ?>
                                    <span class="product-tag">虚拟商品</span>
                                <?php endif; ?>
                            </div>
                            <div class="product-info">
                                <h3 class="product-title">
                                    <?php 
                                    // 高亮搜索关键词
                                    $title = htmlspecialchars($product['title']);
                                    if ($search) {
                                        $title = str_ireplace($search, '<mark>' . htmlspecialchars($search) . '</mark>', $title);
                                    }
                                    echo $title;
                                    ?>
                                </h3>
                                <div class="product-meta">
                                    <span class="price"><?php echo formatPrice($product['price']); ?></span>
                                    <?php if ($product['original_price']): ?>
                                        <span class="original-price"><?php echo formatPrice($product['original_price']); ?></span>
                                    <?php endif; ?>
                                </div>
                                <div class="seller-info">
                                    <img src="<?php echo $product['seller_avatar']; ?>" alt="卖家头像" class="seller-avatar">
                                    <span class="seller-name"><?php echo htmlspecialchars($product['seller_name']); ?></span>
                                </div>
                            </div>
                        </a>
                        <div class="product-actions">
                            <span class="like-btn"><i class="bi bi-heart"></i> <?php echo $product['likes']; ?></span>
                            <span class="view-btn"><i class="bi bi-eye"></i> <?php echo $product['views']; ?></span>
                        </div>
                    </div>
                <?php endforeach; ?>
                
                <?php if (empty($products)): ?>
                    <div class="empty-state">
                        <i class="bi bi-search"></i>
                        <h3>没有找到相关商品</h3>
                        <p>试试其他关键词，或者浏览热门商品</p>
                        <a href="index.php" class="btn btn-primary">浏览热门商品</a>
                    </div>
                <?php endif; ?>
            </div>

            <!-- 分页 -->
            <?php if ($totalPages > 1): ?>
                <div class="pagination">
                    <?php if ($page > 1): ?>
                        <a href="?q=<?php echo urlencode($search); ?>&page=<?php echo $page - 1; ?>" class="prev-page">
                            <i class="bi bi-chevron-left"></i>
                        </a>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                        <a href="?q=<?php echo urlencode($search); ?>&page=<?php echo $i; ?>" 
                           <?php echo $i === $page ? 'class="active"' : ''; ?>>
                            <?php echo $i; ?>
                        </a>
                    <?php endfor; ?>
                    
                    <?php if ($page < $totalPages): ?>
                        <a href="?q=<?php echo urlencode($search); ?>&page=<?php echo $page + 1; ?>" class="next-page">
                            <i class="bi bi-chevron-right"></i>
                        </a>
                    <?php endif; ?>
                    
                    <div class="page-jump">
                        <span>到第</span>
                        <input type="text" value="<?php echo $page; ?>" id="jumpPage">
                        <span>页</span>
                        <button onclick="jumpToPage()">确定</button>
                    </div>
                </div>
            <?php endif; ?>
            
        <?php else: ?>
            <!-- 搜索建议 -->
            <div class="search-suggestions">
                <h3>热门搜索</h3>
                <div class="suggestion-tags">
                    <a href="search.php?q=腾讯视频会员" class="suggestion-tag">腾讯视频会员</a>
                    <a href="search.php?q=游戏点卡" class="suggestion-tag">游戏点卡</a>
                    <a href="search.php?q=软件授权" class="suggestion-tag">软件授权</a>
                    <a href="search.php?q=电子书" class="suggestion-tag">电子书</a>
                    <a href="search.php?q=设计素材" class="suggestion-tag">设计素材</a>
                    <a href="search.php?q=在线课程" class="suggestion-tag">在线课程</a>
                </div>
            </div>
            
            <div class="search-categories">
                <h3>热门分类</h3>
                <div class="category-grid">
                    <?php 
                    $categories = getCategories();
                    foreach ($categories as $category): 
                    ?>
                        <a href="list.php?category=<?php echo $category['slug']; ?>" class="category-item">
                            <div class="category-icon">
                                <i class="bi bi-tag"></i>
                            </div>
                            <div class="category-name"><?php echo htmlspecialchars($category['name']); ?></div>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</main>

<script>
function jumpToPage() {
    const page = document.getElementById('jumpPage').value;
    const search = '<?php echo urlencode($search); ?>';
    if (page && page > 0) {
        window.location.href = `?q=${search}&page=${page}`;
    }
}
</script>

<style>
.search-suggestions, .search-categories {
    margin: 2rem 0;
}

.suggestion-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
}

.suggestion-tag {
    padding: 0.5rem 1rem;
    background: #f5f5f5;
    border-radius: 20px;
    text-decoration: none;
    color: #666;
    transition: all 0.3s;
}

.suggestion-tag:hover {
    background: #007bff;
    color: white;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.category-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.category-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.category-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: #007bff;
}

.empty-state {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #ccc;
}

mark {
    background: #ffeb3b;
    padding: 0 2px;
}
</style>

<?php require_once 'includes/footer.php'; ?>
