<?php
// 演示数据插入脚本
require_once 'config/database.php';

// 检查是否已有数据
$stmt = $pdo->query("SELECT COUNT(*) FROM users");
$userCount = $stmt->fetchColumn();

if ($userCount > 0) {
    echo "数据库中已有数据，跳过演示数据插入。\n";
    exit;
}

try {
    // 插入演示用户
    $users = [
        [
            'username' => 'demo_user1',
            'email' => '<EMAIL>',
            'phone' => '13800138001',
            'password' => password_hash('123456', PASSWORD_DEFAULT),
            'nickname' => '数码达人',
            'avatar' => 'images/avatar1.svg',
            'is_verified' => 1,
            'rating' => 4.8
        ],
        [
            'username' => 'demo_user2',
            'email' => '<EMAIL>',
            'phone' => '13800138002',
            'password' => password_hash('123456', PASSWORD_DEFAULT),
            'nickname' => '游戏专营店',
            'avatar' => 'images/avatar2.svg',
            'is_verified' => 1,
            'rating' => 4.9
        ],
        [
            'username' => 'demo_user3',
            'email' => '<EMAIL>',
            'phone' => '13800138003',
            'password' => password_hash('123456', PASSWORD_DEFAULT),
            'nickname' => '软件授权中心',
            'avatar' => 'images/avatar3.svg',
            'is_verified' => 1,
            'rating' => 4.7
        ]
    ];

    foreach ($users as $user) {
        $stmt = $pdo->prepare("
            INSERT INTO users (username, email, phone, password, nickname, avatar, is_verified, rating) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $user['username'],
            $user['email'],
            $user['phone'],
            $user['password'],
            $user['nickname'],
            $user['avatar'],
            $user['is_verified'],
            $user['rating']
        ]);
    }

    // 插入演示商品
    $products = [
        [
            'user_id' => 1,
            'category_id' => 1, // 会员账号
            'title' => '腾讯视频VIP会员 12个月 年卡 自动充值',
            'description' => '腾讯视频VIP会员年卡，支持手机、电脑、电视多端观看，海量高清影视资源，无广告观看体验。购买后自动充值到账，支持官方验证。',
            'price' => 148.00,
            'original_price' => 253.00,
            'stock' => 100,
            'is_virtual' => 1,
            'images' => json_encode(['images/product1.svg']),
            'views' => 1024,
            'likes' => 125
        ],
        [
            'user_id' => 2,
            'category_id' => 2, // 游戏点卡
            'title' => '王者荣耀 648元点卡 6480点券 代充值',
            'description' => '王者荣耀官方点卡，648元面值，可获得6480点券。支持秒充，安全可靠，官方正版保障。',
            'price' => 588.00,
            'original_price' => 648.00,
            'stock' => 50,
            'is_virtual' => 1,
            'images' => json_encode(['images/product2.svg']),
            'views' => 856,
            'likes' => 98
        ],
        [
            'user_id' => 3,
            'category_id' => 3, // 软件授权
            'title' => 'Adobe Creative Cloud 2023 全家桶 一年授权',
            'description' => 'Adobe Creative Cloud 2023全套软件授权，包含Photoshop、Illustrator、Premiere Pro等20多款专业软件，支持2台设备同时使用。',
            'price' => 688.00,
            'original_price' => 888.00,
            'stock' => 20,
            'is_virtual' => 1,
            'images' => json_encode(['images/product3.svg']),
            'views' => 2048,
            'likes' => 132
        ],
        [
            'user_id' => 1,
            'category_id' => 4, // 电子书籍
            'title' => '《人工智能编程指南》电子书 PDF+EPUB+MOBI格式',
            'description' => '最新人工智能编程指南，涵盖机器学习、深度学习、自然语言处理等热门技术。提供PDF、EPUB、MOBI三种格式，支持多种阅读设备。',
            'price' => 29.90,
            'original_price' => 68.00,
            'stock' => 999,
            'is_virtual' => 1,
            'images' => json_encode(['images/product4.svg']),
            'views' => 512,
            'likes' => 75
        ],
        [
            'user_id' => 2,
            'category_id' => 5, // 设计资源
            'title' => '高端UI设计素材包 1000+精品图标',
            'description' => '精心设计的UI素材包，包含1000多个高质量图标，支持多种格式，适用于网页设计、APP设计、平面设计等多个领域。',
            'price' => 99.00,
            'original_price' => 199.00,
            'stock' => 200,
            'is_virtual' => 1,
            'images' => json_encode(['images/product5.svg']),
            'views' => 768,
            'likes' => 89
        ],
        [
            'user_id' => 3,
            'category_id' => 6, // 教程课程
            'title' => 'Python零基础入门到精通 完整视频教程',
            'description' => 'Python编程完整学习路径，从零基础到项目实战，包含100+小时高清视频教程，配套源码和练习题。',
            'price' => 199.00,
            'original_price' => 399.00,
            'stock' => 500,
            'is_virtual' => 1,
            'images' => json_encode(['images/product6.svg']),
            'views' => 1536,
            'likes' => 156
        ]
    ];

    foreach ($products as $product) {
        $stmt = $pdo->prepare("
            INSERT INTO products (user_id, category_id, title, description, price, original_price, stock, is_virtual, images, views, likes) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $product['user_id'],
            $product['category_id'],
            $product['title'],
            $product['description'],
            $product['price'],
            $product['original_price'],
            $product['stock'],
            $product['is_virtual'],
            $product['images'],
            $product['views'],
            $product['likes']
        ]);
        
        $productId = $pdo->lastInsertId();
        
        // 为虚拟商品添加属性
        if ($product['is_virtual']) {
            $virtualAttrs = [
                'product_id' => $productId,
                'usage_period' => $productId <= 3 ? 'limited' : 'unlimited',
                'period_value' => $productId <= 3 ? 12 : null,
                'period_unit' => $productId <= 3 ? 'month' : null,
                'delivery_method' => 'automatic',
                'platform' => $productId == 1 ? 'iOS/Android/Windows/Mac' : ($productId == 2 ? 'iOS/Android' : 'Windows/Mac'),
                'content_text' => '购买后将通过站内信发送激活码和使用说明',
                'instructions' => '1. 收到激活码后请及时使用\n2. 如有问题请联系客服\n3. 激活后不支持退款',
                'services' => json_encode(['consult', 'replace'])
            ];
            
            $stmt = $pdo->prepare("
                INSERT INTO virtual_attributes (product_id, usage_period, period_value, period_unit, delivery_method, platform, content_text, instructions, services) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $virtualAttrs['product_id'],
                $virtualAttrs['usage_period'],
                $virtualAttrs['period_value'],
                $virtualAttrs['period_unit'],
                $virtualAttrs['delivery_method'],
                $virtualAttrs['platform'],
                $virtualAttrs['content_text'],
                $virtualAttrs['instructions'],
                $virtualAttrs['services']
            ]);
        }
    }

    echo "演示数据插入成功！\n";
    echo "演示用户账号：\n";
    echo "- 用户名: demo_user1, 密码: 123456\n";
    echo "- 用户名: demo_user2, 密码: 123456\n";
    echo "- 用户名: demo_user3, 密码: 123456\n";
    echo "\n已插入 " . count($users) . " 个用户和 " . count($products) . " 个商品。\n";

} catch (Exception $e) {
    echo "插入演示数据失败：" . $e->getMessage() . "\n";
}
?>
