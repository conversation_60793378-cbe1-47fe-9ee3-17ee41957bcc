<svg xmlns="http://www.w3.org/2000/svg" width="300" height="300" viewBox="0 0 300 300">
  <style>
    .product-bg { fill: #F8F8F8; }
    .product-outline { fill: none; stroke: #DDDDDD; stroke-width: 2; }
    .product-phone { fill: #222222; }
    .product-screen { fill: #6A7FDB; }
    .product-button { fill: #AAAAAA; }
    .product-camera { fill: #555555; }
    .product-camera-ring { fill: none; stroke: #888888; stroke-width: 2; }
  </style>
  <g>
    <!-- 背景 -->
    <rect class="product-bg" width="300" height="300" rx="10" ry="10"/>
    
    <!-- 手机轮廓 -->
    <rect class="product-phone" x="85" y="45" width="130" height="210" rx="15" ry="15"/>
    
    <!-- 屏幕 -->
    <rect class="product-screen" x="90" y="55" width="120" height="190" rx="8" ry="8"/>
    
    <!-- 摄像头模块 -->
    <rect class="product-camera" x="100" y="65" width="40" height="40" rx="8" ry="8"/>
    
    <!-- 摄像头 -->
    <circle class="product-camera" cx="110" cy="75" r="5"/>
    <circle class="product-camera-ring" cx="110" cy="75" r="7"/>
    
    <circle class="product-camera" cx="110" cy="95" r="5"/>
    <circle class="product-camera-ring" cx="110" cy="95" r="7"/>
    
    <circle class="product-camera" cx="130" cy="75" r="5"/>
    <circle class="product-camera-ring" cx="130" cy="75" r="7"/>
    
    <!-- 产品名称 -->
    <text x="150" y="280" text-anchor="middle" font-family="Arial" font-size="16" fill="#333333">iPhone 14 Pro</text>
  </g>
</svg>