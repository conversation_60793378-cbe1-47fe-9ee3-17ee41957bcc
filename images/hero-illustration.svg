<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="400" height="300" fill="url(#gradient1)"/>
  
  <!-- Floating elements -->
  <circle cx="80" cy="60" r="20" fill="rgba(255,255,255,0.2)"/>
  <circle cx="320" cy="80" r="15" fill="rgba(255,255,255,0.15)"/>
  <circle cx="350" cy="200" r="25" fill="rgba(255,255,255,0.1)"/>
  
  <!-- Main illustration - Digital commerce concept -->
  <g transform="translate(100, 50)">
    <!-- Computer/Device -->
    <rect x="0" y="80" width="200" height="120" rx="8" fill="white" opacity="0.9"/>
    <rect x="10" y="90" width="180" height="100" rx="4" fill="#f8f9fa"/>
    
    <!-- Screen content -->
    <rect x="20" y="100" width="40" height="30" rx="4" fill="#ff6f06"/>
    <rect x="70" y="100" width="60" height="8" rx="4" fill="#e9ecef"/>
    <rect x="70" y="115" width="40" height="6" rx="3" fill="#dee2e6"/>
    
    <rect x="20" y="140" width="40" height="30" rx="4" fill="#28a745"/>
    <rect x="70" y="140" width="60" height="8" rx="4" fill="#e9ecef"/>
    <rect x="70" y="155" width="40" height="6" rx="3" fill="#dee2e6"/>
    
    <!-- Shopping cart icon -->
    <g transform="translate(150, 110)">
      <path d="M2 2h3l.4 2M7 13a1 1 0 1 0 0 2 1 1 0 0 0 0-2zM17 13a1 1 0 1 0 0 2 1 1 0 0 0 0-2zM5.4 4H19l-1 7H6.4L5.4 4z" 
            stroke="#ff6f06" stroke-width="2" fill="none"/>
    </g>
    
    <!-- Digital elements floating around -->
    <g opacity="0.6">
      <!-- Cloud -->
      <path d="M60 40c-4 0-7 3-7 7 0 1 0 2 1 3-2 1-3 3-3 5 0 3 3 6 6 6h10c3 0 5-2 5-5 0-2-1-4-3-5 0-6-5-11-9-11z" 
            fill="white"/>
      
      <!-- Lightning bolt -->
      <path d="M170 30l-8 15h6l-4 10 8-15h-6l4-10z" fill="#ffc107"/>
      
      <!-- Shield -->
      <path d="M40 20c0-2 2-4 4-4s4 2 4 4v8c0 4-4 8-4 8s-4-4-4-8v-8z" fill="#28a745"/>
    </g>
  </g>
  
  <!-- Decorative elements -->
  <path d="M50 250c20-10 40-5 60-15s40-5 60-15" stroke="rgba(255,255,255,0.3)" stroke-width="2" fill="none"/>
  <path d="M250 280c20-10 40-5 60-15s40-5 60-15" stroke="rgba(255,255,255,0.3)" stroke-width="2" fill="none"/>
  
  <!-- Gradients -->
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:rgba(255,255,255,0.1);stop-opacity:1" />
      <stop offset="100%" style="stop-color:rgba(255,255,255,0.05);stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
