<svg xmlns="http://www.w3.org/2000/svg" width="300" height="300" viewBox="0 0 300 300">
  <style>
    .product-bg { fill: #F8F8F8; }
    .product-outline { fill: none; stroke: #DDDDDD; stroke-width: 2; }
    .product-phone { fill: #333333; }
    .product-screen { fill: #4A90E2; }
    .product-button { fill: #CCCCCC; }
    .product-camera { fill: #666666; }
  </style>
  <g>
    <!-- 背景 -->
    <rect class="product-bg" width="300" height="300" rx="10" ry="10"/>
    
    <!-- 手机轮廓 -->
    <rect class="product-phone" x="90" y="50" width="120" height="200" rx="12" ry="12"/>
    
    <!-- 屏幕 -->
    <rect class="product-screen" x="95" y="60" width="110" height="180" rx="5" ry="5"/>
    
    <!-- 摄像头 -->
    <circle class="product-camera" cx="150" cy="75" r="5"/>
    
    <!-- 按钮 -->
    <rect class="product-button" x="85" y="120" width="5" height="20" rx="2" ry="2"/>
    
    <!-- 产品名称 -->
    <text x="150" y="260" text-anchor="middle" font-family="Arial" font-size="16" fill="#333333">iPhone 13</text>
  </g>
</svg>