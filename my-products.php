<?php
// 先处理所有逻辑，再包含header
require_once 'includes/functions.php';

// 检查登录状态
if (!isLoggedIn()) {
    $_SESSION['error_message'] = '请先登录';
    redirect('login.php');
}

$currentUser = getCurrentUser();

// 处理商品操作
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    $productId = intval($_POST['product_id'] ?? 0);

    if ($productId > 0) {
        try {
            switch ($action) {
                case 'delete':
                    $stmt = $pdo->prepare("DELETE FROM products WHERE id = ? AND user_id = ?");
                    $stmt->execute([$productId, $_SESSION['user_id']]);
                    $_SESSION['success_message'] = '商品删除成功';
                    break;

                case 'toggle_status':
                    $stmt = $pdo->prepare("UPDATE products SET status = CASE WHEN status = 'active' THEN 'inactive' ELSE 'active' END WHERE id = ? AND user_id = ?");
                    $stmt->execute([$productId, $_SESSION['user_id']]);
                    $_SESSION['success_message'] = '商品状态更新成功';
                    break;
            }
        } catch (Exception $e) {
            $_SESSION['error_message'] = '操作失败：' . $e->getMessage();
        }
    }

    redirect('my-products.php');
}

// 获取筛选参数
$status = $_GET['status'] ?? 'all';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 12;
$offset = ($page - 1) * $limit;

// 构建查询条件
$whereClause = "WHERE p.user_id = ?";
$params = [$_SESSION['user_id']];

if ($status !== 'all') {
    $whereClause .= " AND p.status = ?";
    $params[] = $status;
}

// 获取用户发布的商品
try {
    $stmt = $pdo->prepare("
        SELECT p.*, c.name as category_name,
               (SELECT COUNT(*) FROM orders o WHERE o.product_id = p.id AND o.status = 'completed') as sold_count
        FROM products p 
        JOIN categories c ON p.category_id = c.id 
        $whereClause
        ORDER BY p.created_at DESC 
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([...$params, $limit, $offset]);
    $products = $stmt->fetchAll();
    
    // 获取总数
    $countStmt = $pdo->prepare("SELECT COUNT(*) FROM products p $whereClause");
    $countStmt->execute($params);
    $totalProducts = $countStmt->fetchColumn();
    $totalPages = ceil($totalProducts / $limit);
    
} catch (Exception $e) {
    $products = [];
    $totalProducts = 0;
    $totalPages = 0;
}

// 获取统计数据
try {
    $statsStmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active,
            SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive,
            SUM(CASE WHEN status = 'draft' THEN 1 ELSE 0 END) as draft,
            SUM(views) as total_views,
            SUM(likes) as total_likes
        FROM products 
        WHERE user_id = ?
    ");
    $statsStmt->execute([$_SESSION['user_id']]);
    $stats = $statsStmt->fetch();
} catch (Exception $e) {
    $stats = [
        'total' => 0,
        'active' => 0,
        'inactive' => 0,
        'draft' => 0,
        'total_views' => 0,
        'total_likes' => 0
    ];
}

// 现在包含header，因为所有逻辑处理已完成
$pageTitle = '我的发布';
$additionalCSS = ['css/member.css'];
require_once 'includes/header.php';
?>

<!-- 主要内容区 -->
<main class="main member-page">
    <div class="container">
        <div class="member-container">
            <!-- 侧边栏 -->
            <?php include 'includes/member-sidebar.php'; ?>

            <!-- 主内容区 -->
            <div class="member-content">
                <!-- 页面标题 -->
                <div class="page-header">
                    <div class="page-title-section">
                        <h1 class="page-title">我的发布</h1>
                        <p class="page-subtitle">管理您发布的所有商品</p>
                    </div>
                    <div class="page-actions">
                        <a href="publish.php" class="btn btn-primary">
                            <i class="bi bi-plus-lg"></i>
                            <span>发布新商品</span>
                        </a>
                    </div>
                </div>

            <!-- 统计卡片 -->
            <div class="stats-cards">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="bi bi-box-seam"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value"><?php echo $stats['total']; ?></div>
                        <div class="stat-label">总商品数</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon active">
                        <i class="bi bi-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value"><?php echo $stats['active']; ?></div>
                        <div class="stat-label">在售中</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="bi bi-eye"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value"><?php echo number_format($stats['total_views']); ?></div>
                        <div class="stat-label">总浏览量</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="bi bi-heart"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value"><?php echo number_format($stats['total_likes']); ?></div>
                        <div class="stat-label">总收藏数</div>
                    </div>
                </div>
            </div>

            <!-- 筛选标签 -->
            <div class="filter-tabs">
                <a href="?status=all" class="filter-tab <?php echo $status === 'all' ? 'active' : ''; ?>">
                    全部 (<?php echo $stats['total']; ?>)
                </a>
                <a href="?status=active" class="filter-tab <?php echo $status === 'active' ? 'active' : ''; ?>">
                    在售中 (<?php echo $stats['active']; ?>)
                </a>
                <a href="?status=inactive" class="filter-tab <?php echo $status === 'inactive' ? 'active' : ''; ?>">
                    已下架 (<?php echo $stats['inactive']; ?>)
                </a>
                <a href="?status=draft" class="filter-tab <?php echo $status === 'draft' ? 'active' : ''; ?>">
                    草稿箱 (<?php echo $stats['draft']; ?>)
                </a>
            </div>

            <!-- 商品列表 -->
            <div class="products-section">
                <?php if (empty($products)): ?>
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="bi bi-box-seam"></i>
                        </div>
                        <h3>还没有发布任何商品</h3>
                        <p>发布您的第一个虚拟商品，开始赚钱吧！</p>
                        <a href="publish.php" class="btn btn-primary">立即发布</a>
                    </div>
                <?php else: ?>
                    <div class="products-grid">
                        <?php foreach ($products as $product): ?>
                            <div class="product-card">
                                <div class="product-image">
                                    <?php
                                    $images = json_decode($product['images'], true);
                                    $firstImage = $images ? (UPLOAD_PATH . $images[0]) : 'images/product-default.svg';
                                    ?>
                                    <img src="<?php echo $firstImage; ?>" alt="<?php echo htmlspecialchars($product['title']); ?>">
                                    <div class="product-status status-<?php echo $product['status']; ?>">
                                        <?php
                                        switch ($product['status']) {
                                            case 'active': echo '在售中'; break;
                                            case 'inactive': echo '已下架'; break;
                                            case 'draft': echo '草稿'; break;
                                            default: echo $product['status'];
                                        }
                                        ?>
                                    </div>
                                </div>
                                <div class="product-info">
                                    <h3 class="product-title">
                                        <a href="detail.php?id=<?php echo $product['id']; ?>">
                                            <?php echo htmlspecialchars($product['title']); ?>
                                        </a>
                                    </h3>
                                    <div class="product-meta">
                                        <span class="product-category"><?php echo htmlspecialchars($product['category_name']); ?></span>
                                        <span class="product-price"><?php echo formatPrice($product['price']); ?></span>
                                    </div>
                                    <div class="product-stats">
                                        <span class="stat-item">
                                            <i class="bi bi-eye"></i>
                                            <?php echo $product['views']; ?>
                                        </span>
                                        <span class="stat-item">
                                            <i class="bi bi-heart"></i>
                                            <?php echo $product['likes']; ?>
                                        </span>
                                        <span class="stat-item">
                                            <i class="bi bi-bag"></i>
                                            <?php echo $product['sold_count']; ?>
                                        </span>
                                    </div>
                                    <div class="product-time">
                                        发布于 <?php echo timeAgo($product['created_at']); ?>
                                    </div>
                                </div>
                                <div class="product-actions">
                                    <a href="edit-product.php?id=<?php echo $product['id']; ?>" class="action-btn edit-btn">
                                        <i class="bi bi-pencil"></i>
                                        编辑
                                    </a>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="toggle_status">
                                        <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                                        <button type="submit" class="action-btn toggle-btn">
                                            <i class="bi bi-<?php echo $product['status'] === 'active' ? 'pause' : 'play'; ?>"></i>
                                            <?php echo $product['status'] === 'active' ? '下架' : '上架'; ?>
                                        </button>
                                    </form>
                                    <button class="action-btn delete-btn" onclick="deleteProduct(<?php echo $product['id']; ?>)">
                                        <i class="bi bi-trash"></i>
                                        删除
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- 分页 -->
                    <?php if ($totalPages > 1): ?>
                        <div class="pagination">
                            <?php if ($page > 1): ?>
                                <a href="?status=<?php echo $status; ?>&page=<?php echo $page - 1; ?>" class="page-btn">
                                    <i class="bi bi-chevron-left"></i>
                                </a>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <a href="?status=<?php echo $status; ?>&page=<?php echo $i; ?>" 
                                   class="page-btn <?php echo $i === $page ? 'active' : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>
                            
                            <?php if ($page < $totalPages): ?>
                                <a href="?status=<?php echo $status; ?>&page=<?php echo $page + 1; ?>" class="page-btn">
                                    <i class="bi bi-chevron-right"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
function deleteProduct(productId) {
    if (confirm('确定要删除这个商品吗？此操作不可恢复。')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="product_id" value="${productId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php require_once 'includes/footer.php'; ?>
