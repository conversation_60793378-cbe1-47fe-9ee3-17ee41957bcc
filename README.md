# 数字鱼 - 虚拟商品交易平台

基于PHP开发的虚拟商品交易平台，类似闲鱼但专注于虚拟商品交易。

## 功能特性

### 用户功能
- 用户注册/登录系统
- 个人中心管理
- 商品浏览和搜索
- 商品收藏和浏览历史
- 用户评价系统

### 商品功能
- 商品发布和管理
- 虚拟商品特殊属性支持
- 商品分类管理
- 图片上传功能
- 商品详情展示

### 虚拟商品特性
- 使用期限设置（永久/限时）
- 发货方式（自动/手动）
- 平台兼容性说明
- 商品内容管理（文本/文件）
- 使用说明和售后服务

## 技术栈

- **后端**: PHP 7.4+
- **数据库**: MySQL 5.7+
- **前端**: HTML5, CSS3, JavaScript
- **框架**: 原生PHP (无框架依赖)

## 安装说明

### 环境要求
- PHP 7.4 或更高版本
- MySQL 5.7 或更高版本
- Apache/Nginx Web服务器
- PDO MySQL 扩展

### 安装步骤

1. **下载代码**
   ```bash
   git clone [repository-url]
   cd xianyu
   ```

2. **配置Web服务器**
   - 将项目文件放置在Web服务器根目录
   - 确保 `uploads/` 目录有写入权限

3. **运行安装程序**
   - 访问 `http://your-domain/install.php`
   - 按照向导完成数据库配置
   - 系统会自动创建数据库表和初始数据

4. **完成安装**
   - 删除 `install.php` 文件（安全考虑）
   - 访问首页开始使用

### 手动安装（可选）

如果自动安装失败，可以手动配置：

1. **创建数据库**
   ```sql
   CREATE DATABASE xianyu_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

2. **导入数据库结构**
   ```bash
   mysql -u username -p xianyu_db < config/init_database.sql
   ```

3. **配置数据库连接**
   - 编辑 `config/database.php`
   - 修改数据库连接参数

## 目录结构

```
xianyu/
├── api/                    # API接口文件
│   └── favorite.php       # 收藏功能API
├── config/                # 配置文件
│   ├── database.php       # 数据库配置
│   └── init_database.sql  # 数据库初始化脚本
├── css/                   # 样式文件
├── images/                # 图片资源
├── includes/              # 公共文件
│   ├── functions.php      # 公共函数
│   ├── header.php         # 页面头部
│   └── footer.php         # 页面底部
├── js/                    # JavaScript文件
├── uploads/               # 上传文件目录
├── index.php              # 首页
├── login.php              # 登录页面
├── register.php           # 注册页面
├── publish.php            # 发布商品页面
├── list.php               # 商品列表页面
├── detail.php             # 商品详情页面
├── search.php             # 搜索页面
├── member.php             # 会员中心
├── logout.php             # 退出登录
├── install.php            # 安装程序
└── README.md              # 说明文档
```

## 数据库设计

### 主要数据表

- `users` - 用户表
- `products` - 商品表
- `categories` - 商品分类表
- `virtual_attributes` - 虚拟商品属性表
- `orders` - 订单表
- `favorites` - 收藏表
- `browse_history` - 浏览历史表

## 使用说明

### 用户注册和登录
1. 访问注册页面创建账号
2. 使用用户名/邮箱/手机号登录
3. 完善个人信息

### 发布商品
1. 登录后点击"发布商品"
2. 选择商品类型（实物/虚拟）
3. 填写商品信息
4. 上传商品图片
5. 设置价格和库存
6. 发布商品

### 虚拟商品特殊设置
- 使用期限：永久有效或限时有效
- 发货方式：自动发货或手动发货
- 平台支持：说明适用平台
- 商品内容：文本内容或文件附件
- 售后服务：退款、更换、技术支持等

### 商品管理
- 在会员中心查看已发布商品
- 编辑商品信息
- 查看商品统计数据
- 管理商品状态

## 开发说明

### 添加新功能
1. 在相应目录创建PHP文件
2. 使用 `includes/functions.php` 中的公共函数
3. 包含 `includes/header.php` 和 `includes/footer.php`
4. 遵循现有的代码风格

### 数据库操作
- 使用PDO进行数据库操作
- 所有SQL查询都使用预处理语句
- 在 `includes/functions.php` 中添加新的数据库函数

### 安全考虑
- 所有用户输入都经过验证和过滤
- 使用password_hash()加密密码
- 实现了CSRF保护
- 文件上传有类型和大小限制

## 常见问题

### 安装问题
- **数据库连接失败**: 检查数据库配置信息
- **权限错误**: 确保uploads目录有写入权限
- **页面空白**: 检查PHP错误日志

### 使用问题
- **图片上传失败**: 检查文件大小和格式
- **登录失败**: 确认用户名和密码正确
- **商品不显示**: 检查商品状态和分类

## 更新日志

### v1.0.0 (2023-12-XX)
- 初始版本发布
- 基础用户系统
- 商品发布和管理
- 虚拟商品支持
- 搜索和筛选功能

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请联系开发团队。

---

**注意**: 这是一个演示项目，用于学习和开发目的。在生产环境中使用前，请进行充分的安全测试和性能优化。