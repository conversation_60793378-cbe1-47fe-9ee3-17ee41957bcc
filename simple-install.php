<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字鱼 - 简易安装</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 32px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 24px;
            text-align: center;
        }
        .step {
            margin-bottom: 24px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #ff6f06;
        }
        .step h3 {
            margin: 0 0 12px 0;
            color: #333;
        }
        .step p {
            margin: 0 0 16px 0;
            color: #6c757d;
        }
        .btn {
            background: #ff6f06;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #e55a00;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.danger {
            background: #dc3545;
        }
        .result {
            margin-top: 16px;
            padding: 12px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 13px;
            line-height: 1.4;
            white-space: pre-wrap;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .result.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .sql-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 16px;
            margin: 16px 0;
            font-family: monospace;
            font-size: 13px;
            overflow-x: auto;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 16px;
            margin: 16px 0;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>数字鱼功能更新 - 简易安装</h1>
        
        <div class="warning">
            <strong>注意：</strong>请确保您已经备份了数据库，然后按照以下步骤进行安装。
        </div>
        
        <div class="step">
            <h3>方法一：自动安装（推荐）</h3>
            <p>点击下面的按钮执行自动安装脚本</p>
            <button class="btn" onclick="runAutoInstall()">开始自动安装</button>
            <div id="auto-result"></div>
        </div>
        
        <div class="step">
            <h3>方法二：手动执行SQL</h3>
            <p>如果自动安装失败，您可以手动执行以下SQL语句：</p>
            <div class="sql-box">-- 购物车表
CREATE TABLE IF NOT EXISTS cart (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_product_id (product_id),
    UNIQUE KEY unique_cart_item (user_id, product_id)
);

-- 钱包表
CREATE TABLE IF NOT EXISTS wallet (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    balance DECIMAL(10,2) DEFAULT 0.00,
    frozen_amount DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    UNIQUE KEY unique_user_wallet (user_id)
);

-- 钱包交易记录表
CREATE TABLE IF NOT EXISTS wallet_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    type ENUM('deposit', 'withdraw', 'payment', 'refund', 'commission') NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    balance_after DECIMAL(10,2) NOT NULL,
    description TEXT,
    order_id INT NULL,
    status ENUM('pending', 'completed', 'failed') DEFAULT 'completed',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_status (status)
);

-- 实名认证表
CREATE TABLE IF NOT EXISTS verification (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    real_name VARCHAR(50) NOT NULL,
    id_number VARCHAR(20) NOT NULL,
    id_front_image VARCHAR(255) NOT NULL,
    id_back_image VARCHAR(255) NOT NULL,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    reject_reason TEXT NULL,
    verified_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    UNIQUE KEY unique_user_verification (user_id)
);

-- 添加用户认证状态字段
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_verified BOOLEAN DEFAULT FALSE;</div>
            <button class="btn" onclick="copySQL()">复制SQL语句</button>
        </div>
        
        <div class="step">
            <h3>方法三：检查安装状态</h3>
            <p>检查数据库表是否已经正确创建</p>
            <button class="btn" onclick="checkInstallation()">检查安装状态</button>
            <div id="check-result"></div>
        </div>
        
        <div class="step">
            <h3>完成安装</h3>
            <p>安装完成后，您可以访问以下新功能：</p>
            <ul>
                <li><a href="cart.php" class="btn">购物车功能</a></li>
                <li><a href="wallet.php" class="btn">钱包功能</a></li>
                <li><a href="verification.php" class="btn">实名认证</a></li>
                <li><a href="test-features.php" class="btn">功能测试</a></li>
            </ul>
        </div>
    </div>

    <script>
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${type}">${message}</div>`;
        }
        
        function runAutoInstall() {
            showResult('auto-result', '正在执行自动安装...', 'info');
            
            // 先检查数据库连接
            fetch('api/safe-db-test.php')
            .then(response => response.text())
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    if (data.success || data.basic_tables.length > 0) {
                        showResult('auto-result', '数据库连接正常，开始创建表...', 'info');
                        
                        // 创建表
                        return fetch('api/create-tables.php', { method: 'POST' });
                    } else {
                        throw new Error('数据库连接失败: ' + data.message);
                    }
                } catch (e) {
                    throw new Error('数据库检查失败: ' + e.message + '\n原始响应: ' + text);
                }
            })
            .then(response => response.text())
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        showResult('auto-result', `安装成功！\n已创建: ${data.created_tables.join(', ')}\n\n您现在可以使用新功能了！`, 'success');
                    } else {
                        showResult('auto-result', '安装失败: ' + data.message, 'error');
                    }
                } catch (e) {
                    showResult('auto-result', '表创建失败: ' + e.message + '\n原始响应: ' + text, 'error');
                }
            })
            .catch(error => {
                showResult('auto-result', '安装失败: ' + error.message, 'error');
            });
        }
        
        function checkInstallation() {
            showResult('check-result', '正在检查安装状态...', 'info');
            
            fetch('api/safe-db-test.php')
            .then(response => response.text())
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    let message = `数据库连接: ${data.success ? '正常' : '失败'}\n`;
                    message += `基础表: ${data.basic_tables.join(', ')}\n`;
                    message += `新增表: ${data.existing_tables.join(', ')}\n`;
                    message += `缺少表: ${data.missing_tables.join(', ')}\n`;
                    message += `uploads目录: ${data.uploads_writable ? '可写' : '不可写'}`;
                    
                    const allGood = data.basic_tables.length >= 2 && data.existing_tables.length >= 3 && data.uploads_writable;
                    showResult('check-result', message, allGood ? 'success' : 'error');
                } catch (e) {
                    showResult('check-result', '检查失败: ' + e.message + '\n原始响应: ' + text, 'error');
                }
            })
            .catch(error => {
                showResult('check-result', '检查失败: ' + error.message, 'error');
            });
        }
        
        function copySQL() {
            const sqlText = document.querySelector('.sql-box').textContent;
            navigator.clipboard.writeText(sqlText).then(() => {
                alert('SQL语句已复制到剪贴板');
            }).catch(() => {
                alert('复制失败，请手动选择并复制');
            });
        }
    </script>
</body>
</html>
