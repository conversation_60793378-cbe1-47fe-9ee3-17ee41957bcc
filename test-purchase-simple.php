<?php
require_once 'includes/functions.php';

// 模拟登录（仅用于测试）
if (!isLoggedIn()) {
    // 获取第一个用户进行测试
    $stmt = $pdo->query("SELECT id FROM users LIMIT 1");
    $user = $stmt->fetch();
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = 'test_user';
    }
}

// 获取一个测试商品
$stmt = $pdo->query("SELECT * FROM products WHERE status = 'active' LIMIT 1");
$testProduct = $stmt->fetch();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>购买功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .product-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .btn {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .btn:hover {
            background: #e55a2b;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>购买功能测试</h1>
        
        <?php if ($testProduct): ?>
        <div class="product-info">
            <h3>测试商品信息</h3>
            <p><strong>商品ID:</strong> <?php echo $testProduct['id']; ?></p>
            <p><strong>商品名称:</strong> <?php echo htmlspecialchars($testProduct['title']); ?></p>
            <p><strong>价格:</strong> ¥<?php echo number_format($testProduct['price'], 2); ?></p>
            <p><strong>库存:</strong> <?php echo $testProduct['stock']; ?></p>
            <p><strong>状态:</strong> <?php echo $testProduct['status']; ?></p>
        </div>
        
        <div>
            <button class="btn" onclick="testPurchase(<?php echo $testProduct['id']; ?>)">测试购买</button>
            <button class="btn" onclick="testAddToCart(<?php echo $testProduct['id']; ?>)">测试加入购物车</button>
            <button class="btn" onclick="checkDatabase()">检查数据库状态</button>
        </div>
        
        <?php else: ?>
        <div class="error" style="display: block;">
            <p>没有找到可测试的商品。请先添加一些商品。</p>
        </div>
        <?php endif; ?>
        
        <div id="result" class="result"></div>
    </div>

    <script>
    function showResult(message, isSuccess = true) {
        const resultDiv = document.getElementById('result');
        resultDiv.className = 'result ' + (isSuccess ? 'success' : 'error');
        resultDiv.innerHTML = message;
        resultDiv.style.display = 'block';
    }

    function testPurchase(productId) {
        const formData = new FormData();
        formData.append('product_id', productId);
        formData.append('quantity', 1);
        formData.append('action', 'buy_now');

        fetch('api/purchase.php', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);
            return response.text();
        })
        .then(text => {
            console.log('Raw response:', text);
            try {
                const data = JSON.parse(text);
                if (data.success) {
                    showResult(`购买成功！订单ID: ${data.order_id}`, true);
                } else {
                    showResult(`购买失败: ${data.message}`, false);
                }
            } catch (e) {
                showResult(`JSON解析错误: ${e.message}<br>原始响应: ${text}`, false);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showResult(`网络错误: ${error.message}`, false);
        });
    }

    function testAddToCart(productId) {
        const formData = new FormData();
        formData.append('action', 'add');
        formData.append('product_id', productId);
        formData.append('quantity', 1);

        fetch('api/cart.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showResult('成功加入购物车！', true);
            } else {
                showResult(`加入购物车失败: ${data.message}`, false);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showResult(`网络错误: ${error.message}`, false);
        });
    }

    function checkDatabase() {
        fetch('debug-purchase.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let message = '<h4>数据库状态:</h4>';
                message += `<p>商品数量: ${data.product_count}</p>`;
                message += `<p>用户ID: ${data.session_user_id}</p>`;
                message += `<p>Virtual表存在: ${data.virtual_table_exists ? '是' : '否'}</p>`;
                message += '<p>所有表: ' + data.all_tables.join(', ') + '</p>';
                showResult(message, true);
            } else {
                showResult(`数据库检查失败: ${data.error}`, false);
            }
        })
        .catch(error => {
            showResult(`检查失败: ${error.message}`, false);
        });
    }
    </script>
</body>
</html>
