<?php
// 先处理所有逻辑，再包含header
require_once 'includes/functions.php';

// 检查登录状态
if (!isLoggedIn()) {
    $_SESSION['error_message'] = '请先登录';
    redirect('login.php');
}

$currentUser = getCurrentUser();

// 获取用户认证状态
try {
    $stmt = $pdo->prepare("SELECT * FROM verification WHERE user_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $verification = $stmt->fetch();
} catch (Exception $e) {
    $verification = null;
    $error = '获取认证信息失败：' . $e->getMessage();
}

// 处理认证申请
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] === 'submit_verification') {
    $realName = trim($_POST['real_name'] ?? '');
    $idNumber = trim($_POST['id_number'] ?? '');
    
    // 验证必填字段
    if (empty($realName) || empty($idNumber)) {
        $error = '请填写完整的认证信息';
    } else if (!preg_match('/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/', $idNumber)) {
        $error = '身份证号码格式不正确';
    } else {
        try {
            // 处理身份证照片上传
            $idFrontImage = '';
            $idBackImage = '';
            
            if (isset($_FILES['id_front']) && $_FILES['id_front']['error'] === UPLOAD_ERR_OK) {
                $idFrontImage = uploadFile($_FILES['id_front'], ['jpg', 'jpeg', 'png']);
                if (!$idFrontImage) {
                    throw new Exception('身份证正面照片上传失败');
                }
            } else {
                throw new Exception('请上传身份证正面照片');
            }
            
            if (isset($_FILES['id_back']) && $_FILES['id_back']['error'] === UPLOAD_ERR_OK) {
                $idBackImage = uploadFile($_FILES['id_back'], ['jpg', 'jpeg', 'png']);
                if (!$idBackImage) {
                    throw new Exception('身份证背面照片上传失败');
                }
            } else {
                throw new Exception('请上传身份证背面照片');
            }
            
            // 检查是否已有认证记录
            if ($verification) {
                // 更新认证信息
                $stmt = $pdo->prepare("
                    UPDATE verification 
                    SET real_name = ?, id_number = ?, id_front_image = ?, id_back_image = ?, 
                        status = 'pending', reject_reason = NULL, updated_at = CURRENT_TIMESTAMP
                    WHERE user_id = ?
                ");
                $stmt->execute([$realName, $idNumber, $idFrontImage, $idBackImage, $_SESSION['user_id']]);
            } else {
                // 插入新认证记录
                $stmt = $pdo->prepare("
                    INSERT INTO verification (user_id, real_name, id_number, id_front_image, id_back_image, status) 
                    VALUES (?, ?, ?, ?, ?, 'pending')
                ");
                $stmt->execute([$_SESSION['user_id'], $realName, $idNumber, $idFrontImage, $idBackImage]);
            }
            
            $_SESSION['success_message'] = '实名认证申请已提交，请等待审核';
            redirect('verification.php');
            
        } catch (Exception $e) {
            $error = '认证申请失败：' . $e->getMessage();
        }
    }
}

// 重新获取认证信息
if (!isset($error)) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM verification WHERE user_id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $verification = $stmt->fetch();
    } catch (Exception $e) {
        $verification = null;
    }
}

// 现在包含header，因为所有逻辑处理已完成
$pageTitle = '实名认证';
$additionalCSS = ['css/member.css', 'css/verification.css'];
require_once 'includes/header.php';
?>

<!-- 主要内容区 -->
<main class="main member-page">
    <div class="container">
        <div class="member-container">
            <!-- 侧边栏 -->
            <?php include 'includes/member-sidebar.php'; ?>

            <!-- 主内容区 -->
            <div class="member-content">
                <!-- 页面标题 -->
                <div class="page-header">
                    <div class="page-title-section">
                        <h1 class="page-title">实名认证</h1>
                        <p class="page-subtitle">完成实名认证，享受更多平台服务</p>
                    </div>
                </div>

                <?php if (isset($error)): ?>
                    <div class="alert alert-error"><?php echo $error; ?></div>
                <?php endif; ?>

                <?php if ($verification): ?>
                    <!-- 认证状态显示 -->
                    <div class="verification-status">
                        <div class="status-card">
                            <div class="status-header">
                                <div class="status-icon">
                                    <?php if ($verification['status'] === 'approved'): ?>
                                        <i class="bi bi-check-circle-fill text-success"></i>
                                    <?php elseif ($verification['status'] === 'rejected'): ?>
                                        <i class="bi bi-x-circle-fill text-danger"></i>
                                    <?php else: ?>
                                        <i class="bi bi-clock-fill text-warning"></i>
                                    <?php endif; ?>
                                </div>
                                <div class="status-info">
                                    <h3 class="status-title">
                                        <?php
                                        switch ($verification['status']) {
                                            case 'approved':
                                                echo '认证已通过';
                                                break;
                                            case 'rejected':
                                                echo '认证被拒绝';
                                                break;
                                            default:
                                                echo '认证审核中';
                                        }
                                        ?>
                                    </h3>
                                    <p class="status-desc">
                                        <?php
                                        switch ($verification['status']) {
                                            case 'approved':
                                                echo '恭喜您，实名认证已通过！您现在可以享受平台的所有服务。';
                                                break;
                                            case 'rejected':
                                                echo '很抱歉，您的认证申请被拒绝。请查看拒绝原因并重新提交。';
                                                break;
                                            default:
                                                echo '您的认证申请正在审核中，预计1-3个工作日内完成审核。';
                                        }
                                        ?>
                                    </p>
                                </div>
                            </div>

                            <div class="verification-details">
                                <div class="detail-item">
                                    <label>真实姓名：</label>
                                    <span><?php echo htmlspecialchars($verification['real_name']); ?></span>
                                </div>
                                <div class="detail-item">
                                    <label>身份证号：</label>
                                    <span><?php echo substr($verification['id_number'], 0, 6) . '********' . substr($verification['id_number'], -4); ?></span>
                                </div>
                                <div class="detail-item">
                                    <label>申请时间：</label>
                                    <span><?php echo date('Y-m-d H:i:s', strtotime($verification['created_at'])); ?></span>
                                </div>
                                <?php if ($verification['verified_at']): ?>
                                    <div class="detail-item">
                                        <label>审核时间：</label>
                                        <span><?php echo date('Y-m-d H:i:s', strtotime($verification['verified_at'])); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <?php if ($verification['status'] === 'rejected'): ?>
                                <div class="reject-reason">
                                    <h4>拒绝原因：</h4>
                                    <p><?php echo htmlspecialchars($verification['reject_reason'] ?? '未提供拒绝原因'); ?></p>
                                </div>
                            <?php endif; ?>

                            <?php if ($verification['status'] === 'rejected'): ?>
                                <div class="status-actions">
                                    <button class="btn btn-primary" onclick="showVerificationForm()">
                                        <i class="bi bi-arrow-clockwise"></i>
                                        <span>重新申请</span>
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- 认证表单 -->
                <div class="verification-form-container" <?php echo $verification && $verification['status'] !== 'rejected' ? 'style="display: none;"' : ''; ?> id="verificationForm">
                    <div class="form-card">
                        <div class="form-header">
                            <h3>实名认证申请</h3>
                            <p>请填写真实信息并上传身份证照片，我们将严格保护您的隐私</p>
                        </div>

                        <form method="POST" enctype="multipart/form-data" class="verification-form">
                            <input type="hidden" name="action" value="submit_verification">

                            <div class="form-group">
                                <label class="form-label required">真实姓名</label>
                                <input type="text" name="real_name" class="form-input" placeholder="请输入身份证上的真实姓名" 
                                       value="<?php echo $verification ? htmlspecialchars($verification['real_name']) : ''; ?>" required>
                            </div>

                            <div class="form-group">
                                <label class="form-label required">身份证号码</label>
                                <input type="text" name="id_number" class="form-input" placeholder="请输入18位身份证号码" 
                                       value="<?php echo $verification ? htmlspecialchars($verification['id_number']) : ''; ?>" 
                                       pattern="[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]" required>
                            </div>

                            <div class="form-group">
                                <label class="form-label required">身份证正面照片</label>
                                <div class="upload-area" id="idFrontUpload">
                                    <div class="upload-placeholder">
                                        <i class="bi bi-cloud-upload"></i>
                                        <p>点击上传身份证正面照片</p>
                                        <small>支持 JPG、PNG 格式，文件大小不超过 5MB</small>
                                    </div>
                                    <input type="file" name="id_front" accept="image/*" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label required">身份证背面照片</label>
                                <div class="upload-area" id="idBackUpload">
                                    <div class="upload-placeholder">
                                        <i class="bi bi-cloud-upload"></i>
                                        <p>点击上传身份证背面照片</p>
                                        <small>支持 JPG、PNG 格式，文件大小不超过 5MB</small>
                                    </div>
                                    <input type="file" name="id_back" accept="image/*" required>
                                </div>
                            </div>

                            <div class="verification-tips">
                                <h4>认证须知：</h4>
                                <ul>
                                    <li>请确保身份证照片清晰完整，四角完整，信息清楚可见</li>
                                    <li>照片中不得出现遮挡、反光、模糊等情况</li>
                                    <li>请使用本人真实身份证，一个身份证只能认证一个账号</li>
                                    <li>认证信息仅用于身份验证，我们将严格保护您的隐私</li>
                                    <li>提交后预计1-3个工作日内完成审核</li>
                                </ul>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary btn-large">
                                    <i class="bi bi-shield-check"></i>
                                    <span>提交认证申请</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 认证优势 -->
                <div class="verification-benefits">
                    <h3>实名认证的优势</h3>
                    <div class="benefits-grid">
                        <div class="benefit-item">
                            <div class="benefit-icon">
                                <i class="bi bi-shield-check"></i>
                            </div>
                            <h4>提升信任度</h4>
                            <p>实名认证用户更容易获得其他用户的信任，提高交易成功率</p>
                        </div>
                        <div class="benefit-item">
                            <div class="benefit-icon">
                                <i class="bi bi-star"></i>
                            </div>
                            <h4>优先推荐</h4>
                            <p>认证用户的商品将获得更多曝光机会和优先推荐</p>
                        </div>
                        <div class="benefit-item">
                            <div class="benefit-icon">
                                <i class="bi bi-wallet2"></i>
                            </div>
                            <h4>提现便利</h4>
                            <p>实名认证后可以正常使用提现功能，资金安全有保障</p>
                        </div>
                        <div class="benefit-item">
                            <div class="benefit-icon">
                                <i class="bi bi-headset"></i>
                            </div>
                            <h4>专属服务</h4>
                            <p>享受专属客服服务，问题处理更快速高效</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
// 实名认证交互脚本
document.addEventListener('DOMContentLoaded', function() {
    initFileUploads();
});

function initFileUploads() {
    const uploadAreas = document.querySelectorAll('.upload-area');
    
    uploadAreas.forEach(area => {
        const input = area.querySelector('input[type="file"]');
        const placeholder = area.querySelector('.upload-placeholder');
        
        area.addEventListener('click', () => input.click());
        
        area.addEventListener('dragover', (e) => {
            e.preventDefault();
            area.classList.add('dragover');
        });
        
        area.addEventListener('dragleave', () => {
            area.classList.remove('dragover');
        });
        
        area.addEventListener('drop', (e) => {
            e.preventDefault();
            area.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                input.files = files;
                handleFileSelect(input, area);
            }
        });
        
        input.addEventListener('change', () => {
            handleFileSelect(input, area);
        });
    });
}

function handleFileSelect(input, area) {
    const file = input.files[0];
    if (!file) return;
    
    // 检查文件类型
    if (!file.type.startsWith('image/')) {
        alert('请选择图片文件');
        input.value = '';
        return;
    }
    
    // 检查文件大小
    if (file.size > 5 * 1024 * 1024) {
        alert('文件大小不能超过5MB');
        input.value = '';
        return;
    }
    
    // 显示预览
    const reader = new FileReader();
    reader.onload = function(e) {
        area.innerHTML = `
            <div class="upload-preview">
                <img src="${e.target.result}" alt="预览图">
                <div class="preview-overlay">
                    <button type="button" class="btn-remove" onclick="removeFile(this)">
                        <i class="bi bi-x-lg"></i>
                    </button>
                </div>
            </div>
        `;
        
        // 重新添加input
        area.appendChild(input);
    };
    reader.readAsDataURL(file);
}

function removeFile(button) {
    const area = button.closest('.upload-area');
    const input = area.querySelector('input[type="file"]');
    input.value = '';
    
    area.innerHTML = `
        <div class="upload-placeholder">
            <i class="bi bi-cloud-upload"></i>
            <p>点击上传身份证照片</p>
            <small>支持 JPG、PNG 格式，文件大小不超过 5MB</small>
        </div>
    `;
    
    // 重新添加input
    area.appendChild(input);
    
    // 重新初始化事件
    initFileUploads();
}

function showVerificationForm() {
    document.getElementById('verificationForm').style.display = 'block';
    document.querySelector('.verification-form-container').scrollIntoView({ behavior: 'smooth' });
}
</script>

<?php require_once 'includes/footer.php'; ?>
