<?php
// 先处理所有逻辑，再包含header
require_once 'includes/functions.php';

// 检查登录状态
if (!isLoggedIn()) {
    $_SESSION['error_message'] = '请先登录';
    redirect('login.php');
}

$currentUser = getCurrentUser();

// 获取用户发布的商品
$userProducts = [];
try {
    $stmt = $pdo->prepare("
        SELECT p.*, c.name as category_name 
        FROM products p 
        JOIN categories c ON p.category_id = c.id 
        WHERE p.user_id = ? 
        ORDER BY p.created_at DESC 
        LIMIT 6
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $userProducts = $stmt->fetchAll();
} catch (Exception $e) {
    // 处理错误
}

// 获取用户收藏的商品
$favoriteProducts = [];
try {
    $stmt = $pdo->prepare("
        SELECT p.*, u.nickname as seller_name, u.avatar as seller_avatar 
        FROM favorites f 
        JOIN products p ON f.product_id = p.id 
        JOIN users u ON p.user_id = u.id 
        WHERE f.user_id = ? 
        ORDER BY f.created_at DESC 
        LIMIT 6
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $favoriteProducts = $stmt->fetchAll();
} catch (Exception $e) {
    // 处理错误
}

// 获取浏览历史
$browseHistory = [];
try {
    $stmt = $pdo->prepare("
        SELECT p.*, u.nickname as seller_name, u.avatar as seller_avatar 
        FROM browse_history bh 
        JOIN products p ON bh.product_id = p.id 
        JOIN users u ON p.user_id = u.id 
        WHERE bh.user_id = ? 
        ORDER BY bh.created_at DESC 
        LIMIT 6
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $browseHistory = $stmt->fetchAll();
} catch (Exception $e) {
    // 处理错误
}

// 现在包含header，因为所有逻辑处理已完成
$pageTitle = '会员中心';
$additionalCSS = ['css/member.css'];
require_once 'includes/header.php';
?>

<!-- 主要内容区 -->
<main class="main member-page">
    <div class="container">
        <div class="member-container">
            <!-- 侧边栏 -->
            <?php include 'includes/member-sidebar.php'; ?>
            
            <!-- 主内容区 -->
            <div class="member-content">
                <!-- 订单概览 -->
                <div class="order-overview">
                    <div class="section-header">
                        <h2>我的订单</h2>
                        <a href="#" class="view-all">查看全部 <i class="bi bi-chevron-right"></i></a>
                    </div>
                    
                    <div class="order-status-tabs">
                        <div class="status-tab">
                            <div class="status-icon"><i class="bi bi-credit-card"></i></div>
                            <div class="status-text">待付款</div>
                            <div class="status-count">0</div>
                        </div>
                        <div class="status-tab">
                            <div class="status-icon"><i class="bi bi-box-seam"></i></div>
                            <div class="status-text">待发货</div>
                            <div class="status-count">0</div>
                        </div>
                        <div class="status-tab">
                            <div class="status-icon"><i class="bi bi-truck"></i></div>
                            <div class="status-text">待收货</div>
                            <div class="status-count">0</div>
                        </div>
                        <div class="status-tab">
                            <div class="status-icon"><i class="bi bi-chat-dots"></i></div>
                            <div class="status-text">待评价</div>
                            <div class="status-count">0</div>
                        </div>
                        <div class="status-tab">
                            <div class="status-icon"><i class="bi bi-arrow-repeat"></i></div>
                            <div class="status-text">退款/售后</div>
                            <div class="status-count">0</div>
                        </div>
                    </div>
                </div>
                
                <!-- 我的发布 -->
                <div class="my-listings">
                    <div class="section-header">
                        <h2>我的发布</h2>
                        <a href="publish.php" class="view-all">发布新商品 <i class="bi bi-plus"></i></a>
                    </div>
                    
                    <div class="listing-tabs">
                        <div class="listing-tab active">出售中</div>
                        <div class="listing-tab">已卖出</div>
                        <div class="listing-tab">已下架</div>
                        <div class="listing-tab">草稿箱</div>
                    </div>
                    
                    <div class="product-grid listing-grid">
                        <?php foreach ($userProducts as $product): ?>
                            <div class="product-card">
                                <a href="detail.php?id=<?php echo $product['id']; ?>">
                                    <div class="product-img">
                                        <?php
                                        $images = json_decode($product['images'], true);
                                        $firstImage = $images ? (UPLOAD_PATH . $images[0]) : 'images/product-default.svg';
                                        ?>
                                        <img src="<?php echo $firstImage; ?>" alt="<?php echo htmlspecialchars($product['title']); ?>">
                                        <?php if ($product['is_virtual']): ?>
                                            <span class="product-tag">虚拟商品</span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="product-info">
                                        <h3 class="product-title"><?php echo htmlspecialchars($product['title']); ?></h3>
                                        <div class="product-meta">
                                            <span class="price"><?php echo formatPrice($product['price']); ?></span>
                                            <?php if ($product['original_price']): ?>
                                                <span class="original-price"><?php echo formatPrice($product['original_price']); ?></span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="listing-stats">
                                            <span class="view-count"><i class="bi bi-eye"></i> <?php echo $product['views']; ?></span>
                                            <span class="like-count"><i class="bi bi-heart"></i> <?php echo $product['likes']; ?></span>
                                        </div>
                                    </div>
                                </a>
                                <div class="listing-actions">
                                    <button class="action-btn edit-btn">编辑</button>
                                    <button class="action-btn promote-btn">推广</button>
                                    <button class="action-btn more-btn"><i class="bi bi-three-dots"></i></button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        
                        <div class="add-listing-card">
                            <a href="publish.php">
                                <div class="add-icon"><i class="bi bi-plus-lg"></i></div>
                                <div class="add-text">发布商品</div>
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- 我的收藏 -->
                <div class="my-favorites">
                    <div class="section-header">
                        <h2>我的收藏</h2>
                        <a href="#" class="view-all">查看全部 <i class="bi bi-chevron-right"></i></a>
                    </div>
                    
                    <div class="product-grid favorites-grid">
                        <?php foreach ($favoriteProducts as $product): ?>
                            <div class="product-card">
                                <a href="detail.php?id=<?php echo $product['id']; ?>">
                                    <div class="product-img">
                                        <?php
                                        $images = json_decode($product['images'], true);
                                        $firstImage = $images ? (UPLOAD_PATH . $images[0]) : 'images/product-default.svg';
                                        ?>
                                        <img src="<?php echo $firstImage; ?>" alt="<?php echo htmlspecialchars($product['title']); ?>">
                                        <?php if ($product['is_virtual']): ?>
                                            <span class="product-tag">虚拟商品</span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="product-info">
                                        <h3 class="product-title"><?php echo htmlspecialchars($product['title']); ?></h3>
                                        <div class="product-meta">
                                            <span class="price"><?php echo formatPrice($product['price']); ?></span>
                                            <?php if ($product['original_price']): ?>
                                                <span class="original-price"><?php echo formatPrice($product['original_price']); ?></span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="seller-info">
                                            <img src="<?php echo $product['seller_avatar']; ?>" alt="卖家头像" class="seller-avatar">
                                            <span class="seller-name"><?php echo htmlspecialchars($product['seller_name']); ?></span>
                                        </div>
                                    </div>
                                </a>
                                <div class="product-actions">
                                    <span class="like-btn liked"><i class="bi bi-heart-fill"></i> <?php echo $product['likes']; ?></span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        
                        <?php if (empty($favoriteProducts)): ?>
                            <div class="empty-state">
                                <i class="bi bi-heart"></i>
                                <p>还没有收藏任何商品</p>
                                <a href="index.php" class="btn btn-primary">去逛逛</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- 浏览历史 -->
                <div class="browse-history">
                    <div class="section-header">
                        <h2>浏览历史</h2>
                        <a href="#" class="view-all">查看全部 <i class="bi bi-chevron-right"></i></a>
                    </div>
                    
                    <div class="product-grid history-grid">
                        <?php foreach ($browseHistory as $product): ?>
                            <div class="product-card">
                                <a href="detail.php?id=<?php echo $product['id']; ?>">
                                    <div class="product-img">
                                        <?php
                                        $images = json_decode($product['images'], true);
                                        $firstImage = $images ? (UPLOAD_PATH . $images[0]) : 'images/product-default.svg';
                                        ?>
                                        <img src="<?php echo $firstImage; ?>" alt="<?php echo htmlspecialchars($product['title']); ?>">
                                    </div>
                                    <div class="product-info">
                                        <h3 class="product-title"><?php echo htmlspecialchars($product['title']); ?></h3>
                                        <div class="product-meta">
                                            <span class="price"><?php echo formatPrice($product['price']); ?></span>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        <?php endforeach; ?>
                        
                        <?php if (empty($browseHistory)): ?>
                            <div class="empty-state">
                                <i class="bi bi-clock-history"></i>
                                <p>还没有浏览记录</p>
                                <a href="index.php" class="btn btn-primary">去逛逛</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                </div>
            </div>
        </div>
    </div>
</main>

<style>
.empty-state {
    text-align: center;
    padding: 2rem;
    color: #666;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #ccc;
}

.listing-actions {
    display: flex;
    gap: 0.5rem;
    padding: 0.5rem;
    border-top: 1px solid #eee;
}

.action-btn {
    flex: 1;
    padding: 0.25rem 0.5rem;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s;
}

.action-btn:hover {
    background: #f5f5f5;
}

.add-listing-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    border: 2px dashed #ddd;
    border-radius: 8px;
    text-decoration: none;
    color: #666;
    transition: all 0.3s;
}

.add-listing-card:hover {
    border-color: #007bff;
    color: #007bff;
}

.add-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}
</style>

<?php require_once 'includes/footer.php'; ?>
