<?php
// 先处理所有逻辑，再包含header
require_once 'includes/functions.php';

// 检查登录状态
if (!isLoggedIn()) {
    $_SESSION['error_message'] = '请先登录';
    redirect('login.php');
}

$currentUser = getCurrentUser();

// 处理取消收藏
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] === 'unfavorite') {
    $productId = intval($_POST['product_id'] ?? 0);

    if ($productId > 0) {
        try {
            $stmt = $pdo->prepare("DELETE FROM favorites WHERE user_id = ? AND product_id = ?");
            $stmt->execute([$_SESSION['user_id'], $productId]);
            $_SESSION['success_message'] = '已取消收藏';
        } catch (Exception $e) {
            $_SESSION['error_message'] = '操作失败：' . $e->getMessage();
        }
    }

    redirect('my-favorites.php');
}

// 获取分页参数
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 12;
$offset = ($page - 1) * $limit;

// 获取用户收藏的商品
try {
    $stmt = $pdo->prepare("
        SELECT p.*, u.nickname as seller_name, u.avatar as seller_avatar, 
               c.name as category_name, f.created_at as favorited_at
        FROM favorites f 
        JOIN products p ON f.product_id = p.id 
        JOIN users u ON p.user_id = u.id 
        JOIN categories c ON p.category_id = c.id 
        WHERE f.user_id = ? AND p.status = 'active'
        ORDER BY f.created_at DESC 
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([$_SESSION['user_id'], $limit, $offset]);
    $favorites = $stmt->fetchAll();
    
    // 获取总数
    $countStmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM favorites f 
        JOIN products p ON f.product_id = p.id 
        WHERE f.user_id = ? AND p.status = 'active'
    ");
    $countStmt->execute([$_SESSION['user_id']]);
    $totalFavorites = $countStmt->fetchColumn();
    $totalPages = ceil($totalFavorites / $limit);
    
} catch (Exception $e) {
    $favorites = [];
    $totalFavorites = 0;
    $totalPages = 0;
}

// 现在包含header，因为所有逻辑处理已完成
$pageTitle = '我的收藏';
$additionalCSS = ['css/member.css'];
require_once 'includes/header.php';
?>

<!-- 主要内容区 -->
<main class="main member-page">
    <div class="container">
        <div class="member-container">
            <!-- 侧边栏 -->
            <?php include 'includes/member-sidebar.php'; ?>

            <!-- 主内容区 -->
            <div class="member-content">
                <!-- 页面标题 -->
                <div class="page-header">
                    <div class="page-title-section">
                        <h1 class="page-title">我的收藏</h1>
                        <p class="page-subtitle">共收藏了 <?php echo $totalFavorites; ?> 个商品</p>
                    </div>
                </div>

            <!-- 收藏商品列表 -->
            <div class="favorites-section">
                <?php if (empty($favorites)): ?>
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="bi bi-heart"></i>
                        </div>
                        <h3>还没有收藏任何商品</h3>
                        <p>去逛逛，发现更多心仪的商品吧！</p>
                        <a href="index.php" class="btn btn-primary">去逛逛</a>
                    </div>
                <?php else: ?>
                    <div class="products-grid">
                        <?php foreach ($favorites as $product): ?>
                            <div class="product-card favorite-card">
                                <div class="product-image">
                                    <?php
                                    $images = json_decode($product['images'], true);
                                    $firstImage = $images ? (UPLOAD_PATH . $images[0]) : 'images/product-default.svg';
                                    ?>
                                    <img src="<?php echo $firstImage; ?>" alt="<?php echo htmlspecialchars($product['title']); ?>">
                                    <?php if ($product['is_virtual']): ?>
                                        <span class="product-tag">虚拟商品</span>
                                    <?php endif; ?>
                                    <div class="favorite-overlay">
                                        <button class="unfavorite-btn" onclick="unfavoriteProduct(<?php echo $product['id']; ?>)" title="取消收藏">
                                            <i class="bi bi-heart-fill"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="product-info">
                                    <h3 class="product-title">
                                        <a href="detail.php?id=<?php echo $product['id']; ?>">
                                            <?php echo htmlspecialchars($product['title']); ?>
                                        </a>
                                    </h3>
                                    <div class="product-meta">
                                        <span class="product-category"><?php echo htmlspecialchars($product['category_name']); ?></span>
                                        <span class="product-price"><?php echo formatPrice($product['price']); ?></span>
                                    </div>
                                    <div class="seller-info">
                                        <img src="<?php echo $product['seller_avatar'] ?: 'images/avatar-default.svg'; ?>" alt="卖家头像" class="seller-avatar">
                                        <span class="seller-name"><?php echo htmlspecialchars($product['seller_name']); ?></span>
                                    </div>
                                    <div class="product-stats">
                                        <span class="stat-item">
                                            <i class="bi bi-eye"></i>
                                            <?php echo $product['views']; ?>
                                        </span>
                                        <span class="stat-item">
                                            <i class="bi bi-heart"></i>
                                            <?php echo $product['likes']; ?>
                                        </span>
                                    </div>
                                    <div class="favorite-time">
                                        收藏于 <?php echo timeAgo($product['favorited_at']); ?>
                                    </div>
                                </div>
                                <div class="product-actions">
                                    <a href="detail.php?id=<?php echo $product['id']; ?>" class="action-btn view-btn">
                                        <i class="bi bi-eye"></i>
                                        查看详情
                                    </a>
                                    <button class="action-btn unfavorite-btn-text" onclick="unfavoriteProduct(<?php echo $product['id']; ?>)">
                                        <i class="bi bi-heart-fill"></i>
                                        取消收藏
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- 分页 -->
                    <?php if ($totalPages > 1): ?>
                        <div class="pagination">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?php echo $page - 1; ?>" class="page-btn">
                                    <i class="bi bi-chevron-left"></i>
                                </a>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <a href="?page=<?php echo $i; ?>" 
                                   class="page-btn <?php echo $i === $page ? 'active' : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>
                            
                            <?php if ($page < $totalPages): ?>
                                <a href="?page=<?php echo $page + 1; ?>" class="page-btn">
                                    <i class="bi bi-chevron-right"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</main>

<style>
/* 收藏页面特有样式 */
.favorite-card {
    position: relative;
}

.favorite-overlay {
    position: absolute;
    top: 12px;
    right: 12px;
    opacity: 0;
    transition: all 0.3s ease;
}

.favorite-card:hover .favorite-overlay {
    opacity: 1;
}

.unfavorite-btn {
    width: 36px;
    height: 36px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    color: #dc3545;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.unfavorite-btn:hover {
    background: #dc3545;
    color: white;
    transform: scale(1.1);
}

.seller-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.seller-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    object-fit: cover;
}

.seller-name {
    font-size: 13px;
    color: #6c757d;
}

.favorite-time {
    font-size: 12px;
    color: #adb5bd;
    margin-top: 8px;
}

.unfavorite-btn-text {
    color: #dc3545;
}

.unfavorite-btn-text:hover {
    background-color: #f8d7da;
    border-color: #dc3545;
    color: #dc3545;
}
</style>

<script>
function unfavoriteProduct(productId) {
    if (confirm('确定要取消收藏这个商品吗？')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="unfavorite">
            <input type="hidden" name="product_id" value="${productId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php require_once 'includes/footer.php'; ?>
