<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字鱼 - 最终安装</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 32px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 24px;
            text-align: center;
        }
        .status-card {
            margin-bottom: 24px;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #ff6f06;
        }
        .status-success {
            background: #d4edda;
            border-left-color: #28a745;
        }
        .status-error {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        .status-warning {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .btn {
            background: #ff6f06;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .btn:hover {
            background: #e55a00;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.danger {
            background: #dc3545;
        }
        .result {
            margin-top: 16px;
            padding: 12px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 13px;
            line-height: 1.4;
            white-space: pre-wrap;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #ff6f06, #ff8533);
            width: 0%;
            transition: width 0.5s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 500;
            font-size: 12px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-top: 24px;
        }
        .feature-card {
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .feature-card h4 {
            margin: 0 0 8px 0;
            color: #333;
        }
        .feature-card p {
            margin: 0 0 12px 0;
            color: #6c757d;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐟 数字鱼功能更新安装</h1>
        
        <div class="progress">
            <div class="progress-bar" id="progressBar">0%</div>
        </div>
        
        <div id="statusContainer">
            <!-- 状态信息将在这里显示 -->
        </div>
        
        <div style="text-align: center; margin: 24px 0;">
            <button class="btn" onclick="startInstallation()">🚀 开始安装</button>
            <button class="btn" onclick="checkStatus()">🔍 检查状态</button>
            <button class="btn" onclick="testFeatures()">🧪 测试功能</button>
        </div>
        
        <div id="resultContainer"></div>
        
        <div class="feature-grid">
            <div class="feature-card">
                <h4>🛒 购物车功能</h4>
                <p>商品添加、删除、数量管理</p>
                <a href="cart.php" class="btn">访问</a>
            </div>
            <div class="feature-card">
                <h4>💰 钱包功能</h4>
                <p>充值、提现、交易记录</p>
                <a href="wallet.php" class="btn">访问</a>
            </div>
            <div class="feature-card">
                <h4>🆔 实名认证</h4>
                <p>身份验证、状态管理</p>
                <a href="verification.php" class="btn">访问</a>
            </div>
            <div class="feature-card">
                <h4>📤 图片上传</h4>
                <p>商品图片上传修复</p>
                <a href="publish.php" class="btn">测试</a>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 0;
        const totalSteps = 3;
        
        function updateProgress(step, message) {
            currentStep = step;
            const percentage = Math.round((step / totalSteps) * 100);
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = percentage + '%';
            progressBar.textContent = percentage + '% - ' + message;
        }
        
        function showStatus(message, type = 'info') {
            const container = document.getElementById('statusContainer');
            const statusClass = type === 'success' ? 'status-success' : 
                               type === 'error' ? 'status-error' : 
                               type === 'warning' ? 'status-warning' : '';
            
            container.innerHTML = `<div class="status-card ${statusClass}">${message}</div>`;
        }
        
        function showResult(message) {
            const container = document.getElementById('resultContainer');
            container.innerHTML = `<div class="result">${message}</div>`;
        }
        
        function startInstallation() {
            updateProgress(0, '开始安装');
            showStatus('正在检查数据库连接...', 'info');
            
            // 步骤1：检查数据库
            fetch('api/safe-db-test.php')
            .then(response => response.text())
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    updateProgress(1, '数据库检查完成');
                    
                    if (data.success || data.basic_tables.length > 0) {
                        showStatus(`✅ 数据库连接正常<br>基础表: ${data.basic_tables.join(', ')}<br>现有新表: ${data.existing_tables.join(', ') || '无'}`, 'success');
                        
                        // 步骤2：创建表
                        createTables();
                    } else {
                        showStatus(`❌ 数据库连接失败: ${data.message}`, 'error');
                        showResult(JSON.stringify(data, null, 2));
                    }
                } catch (e) {
                    showStatus(`❌ 响应解析失败: ${e.message}`, 'error');
                    showResult('原始响应:\n' + text);
                }
            })
            .catch(error => {
                showStatus(`❌ 网络请求失败: ${error.message}`, 'error');
            });
        }
        
        function createTables() {
            updateProgress(2, '创建数据表');
            showStatus('正在创建数据表...', 'info');
            
            fetch('api/create-tables.php', { method: 'POST' })
            .then(response => response.text())
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    updateProgress(3, '安装完成');
                    
                    if (data.success) {
                        showStatus(`🎉 安装成功！<br>已创建: ${data.created_tables.join(', ')}`, 'success');
                        showResult('安装完成！您现在可以使用所有新功能了。');
                    } else {
                        showStatus(`⚠️ 部分安装失败: ${data.message}`, 'warning');
                        showResult(JSON.stringify(data, null, 2));
                    }
                } catch (e) {
                    showStatus(`❌ 表创建失败: ${e.message}`, 'error');
                    showResult('原始响应:\n' + text);
                }
            })
            .catch(error => {
                showStatus(`❌ 创建表失败: ${error.message}`, 'error');
            });
        }
        
        function checkStatus() {
            showStatus('正在检查安装状态...', 'info');
            
            fetch('api/safe-db-test.php')
            .then(response => response.text())
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    
                    let statusMessage = `
                        📊 <strong>安装状态检查</strong><br>
                        数据库连接: ${data.success ? '✅ 正常' : '❌ 失败'}<br>
                        基础表 (${data.basic_tables.length}/3): ${data.basic_tables.join(', ')}<br>
                        新增表 (${data.existing_tables.length}/4): ${data.existing_tables.join(', ')}<br>
                        缺少表: ${data.missing_tables.join(', ') || '无'}<br>
                        uploads目录: ${data.uploads_exists ? '✅ 存在' : '❌ 不存在'}<br>
                        uploads权限: ${data.uploads_writable ? '✅ 可写' : '❌ 不可写'}<br>
                        is_verified字段: ${data.has_verified_field ? '✅ 存在' : '❌ 不存在'}
                    `;
                    
                    const allGood = data.basic_tables.length >= 2 && 
                                   data.existing_tables.length >= 3 && 
                                   data.uploads_writable;
                    
                    showStatus(statusMessage, allGood ? 'success' : 'warning');
                    showResult(JSON.stringify(data, null, 2));
                    
                    if (allGood) {
                        updateProgress(3, '所有功能正常');
                    }
                } catch (e) {
                    showStatus(`❌ 状态检查失败: ${e.message}`, 'error');
                    showResult('原始响应:\n' + text);
                }
            })
            .catch(error => {
                showStatus(`❌ 检查失败: ${error.message}`, 'error');
            });
        }
        
        function testFeatures() {
            window.open('test-features.php', '_blank');
        }
        
        // 页面加载时自动检查状态
        window.addEventListener('load', function() {
            setTimeout(checkStatus, 1000);
        });
    </script>
</body>
</html>
