<?php
require_once 'includes/functions.php';

header('Content-Type: application/json; charset=utf-8');

try {
    // 检查orders表是否有order_number字段
    $stmt = $pdo->query("SHOW COLUMNS FROM orders LIKE 'order_number'");
    $hasOrderNumber = $stmt->fetch() !== false;
    
    $updates = [];
    
    if (!$hasOrderNumber) {
        // 添加order_number字段
        $pdo->exec("ALTER TABLE orders ADD COLUMN order_number VARCHAR(50) UNIQUE AFTER id");
        $updates[] = "添加 order_number 字段";
        
        // 为现有订单生成订单号
        $stmt = $pdo->query("SELECT id, created_at FROM orders WHERE order_number IS NULL");
        $orders = $stmt->fetchAll();
        
        foreach ($orders as $order) {
            $orderNumber = 'XY' . date('YmdHis', strtotime($order['created_at'])) . str_pad($order['id'], 4, '0', STR_PAD_LEFT);
            $updateStmt = $pdo->prepare("UPDATE orders SET order_number = ? WHERE id = ?");
            $updateStmt->execute([$orderNumber, $order['id']]);
        }
        $updates[] = "为现有订单生成订单号";
        
        // 添加索引
        $pdo->exec("CREATE INDEX idx_order_number ON orders(order_number)");
        $updates[] = "添加 order_number 索引";
    } else {
        $updates[] = "order_number 字段已存在";
    }
    
    // 检查virtual_attributes表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'virtual_attributes'");
    $virtualTableExists = $stmt->fetch() !== false;
    
    if (!$virtualTableExists) {
        // 创建virtual_attributes表
        $sql = "CREATE TABLE virtual_attributes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            product_id INT NOT NULL,
            delivery_method ENUM('manual', 'automatic') DEFAULT 'manual',
            content TEXT,
            download_link VARCHAR(255),
            activation_code VARCHAR(100),
            instructions TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
            UNIQUE KEY unique_product_virtual (product_id)
        )";
        $pdo->exec($sql);
        $updates[] = "创建 virtual_attributes 表";
    } else {
        $updates[] = "virtual_attributes 表已存在";
    }
    
    echo json_encode([
        'success' => true,
        'message' => '数据库修复完成',
        'updates' => $updates
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}
?>
