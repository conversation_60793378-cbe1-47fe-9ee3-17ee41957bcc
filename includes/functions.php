<?php
require_once 'config/database.php';

// 用户认证函数
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    return $stmt->fetch();
}

function login($username, $password) {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ? OR email = ? OR phone = ?");
    $stmt->execute([$username, $username, $username]);
    $user = $stmt->fetch();
    
    if ($user && password_verify($password, $user['password'])) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        return true;
    }
    
    return false;
}

function logout() {
    session_destroy();
    header('Location: index.php');
    exit;
}

function register($data) {
    global $pdo;
    
    // 检查用户名、邮箱、手机号是否已存在
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ? OR email = ? OR phone = ?");
    $stmt->execute([$data['username'], $data['email'], $data['phone']]);
    
    if ($stmt->fetchColumn() > 0) {
        return false;
    }
    
    // 创建新用户
    $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT INTO users (username, email, phone, password, nickname) VALUES (?, ?, ?, ?, ?)");
    
    return $stmt->execute([
        $data['username'],
        $data['email'], 
        $data['phone'],
        $hashedPassword,
        $data['nickname']
    ]);
}

// 商品相关函数
function getProducts($limit = 20, $offset = 0, $category = null, $search = null, $sort = 'default', $priceMin = null, $priceMax = null) {
    global $pdo;

    $sql = "SELECT p.*, u.nickname as seller_name, u.avatar as seller_avatar, u.rating as seller_rating, c.name as category_name
            FROM products p
            JOIN users u ON p.user_id = u.id
            JOIN categories c ON p.category_id = c.id
            WHERE p.status = 'active'";

    $params = [];

    if ($category) {
        $sql .= " AND c.slug = ?";
        $params[] = $category;
    }

    if ($search) {
        $sql .= " AND (p.title LIKE ? OR p.description LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }

    if ($priceMin !== null && $priceMin !== '') {
        $sql .= " AND p.price >= ?";
        $params[] = floatval($priceMin);
    }

    if ($priceMax !== null && $priceMax !== '') {
        $sql .= " AND p.price <= ?";
        $params[] = floatval($priceMax);
    }

    // 排序
    switch ($sort) {
        case 'newest':
            $sql .= " ORDER BY p.created_at DESC";
            break;
        case 'price_asc':
            $sql .= " ORDER BY p.price ASC";
            break;
        case 'price_desc':
            $sql .= " ORDER BY p.price DESC";
            break;
        case 'popular':
            $sql .= " ORDER BY p.views DESC, p.likes DESC";
            break;
        default:
            $sql .= " ORDER BY p.created_at DESC";
            break;
    }

    $sql .= " LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);

    return $stmt->fetchAll();
}

function getTotalProductCount($category = null, $search = null, $priceMin = null, $priceMax = null) {
    global $pdo;

    $sql = "SELECT COUNT(*) FROM products p
            JOIN categories c ON p.category_id = c.id
            WHERE p.status = 'active'";

    $params = [];

    if ($category) {
        $sql .= " AND c.slug = ?";
        $params[] = $category;
    }

    if ($search) {
        $sql .= " AND (p.title LIKE ? OR p.description LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }

    if ($priceMin !== null && $priceMin !== '') {
        $sql .= " AND p.price >= ?";
        $params[] = floatval($priceMin);
    }

    if ($priceMax !== null && $priceMax !== '') {
        $sql .= " AND p.price <= ?";
        $params[] = floatval($priceMax);
    }

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);

    return $stmt->fetchColumn();
}

function getProduct($id) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT p.*, u.nickname as seller_name, u.avatar as seller_avatar, u.rating as seller_rating,
               c.name as category_name, va.*
        FROM products p 
        JOIN users u ON p.user_id = u.id 
        JOIN categories c ON p.category_id = c.id 
        LEFT JOIN virtual_attributes va ON p.id = va.product_id
        WHERE p.id = ?
    ");
    $stmt->execute([$id]);
    
    return $stmt->fetch();
}

function incrementViews($productId) {
    global $pdo;

    $stmt = $pdo->prepare("UPDATE products SET views = views + 1 WHERE id = ?");
    $stmt->execute([$productId]);
}

function addBrowseHistory($productId) {
    global $pdo;

    // 只有登录用户才记录浏览历史
    if (!isLoggedIn()) {
        return;
    }

    try {
        // 检查是否已经存在该商品的浏览记录
        $stmt = $pdo->prepare("SELECT id FROM browse_history WHERE user_id = ? AND product_id = ?");
        $stmt->execute([$_SESSION['user_id'], $productId]);

        if ($stmt->fetch()) {
            // 如果存在，更新浏览时间
            $stmt = $pdo->prepare("UPDATE browse_history SET created_at = NOW() WHERE user_id = ? AND product_id = ?");
            $stmt->execute([$_SESSION['user_id'], $productId]);
        } else {
            // 如果不存在，插入新记录
            $stmt = $pdo->prepare("INSERT INTO browse_history (user_id, product_id, created_at) VALUES (?, ?, NOW())");
            $stmt->execute([$_SESSION['user_id'], $productId]);
        }
    } catch (Exception $e) {
        // 静默处理错误，不影响主要功能
        error_log("添加浏览历史失败: " . $e->getMessage());
    }
}

function getCategories() {
    global $pdo;
    
    $stmt = $pdo->query("SELECT * FROM categories ORDER BY sort_order");
    return $stmt->fetchAll();
}

// 文件上传函数
function uploadFile($file, $allowedTypes = ['jpg', 'jpeg', 'png', 'gif']) {
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        error_log("Upload error: File not uploaded properly");
        return false;
    }

    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

    if (!in_array($fileExtension, $allowedTypes)) {
        error_log("Upload error: File type not allowed - " . $fileExtension);
        return false;
    }

    if ($file['size'] > MAX_FILE_SIZE) {
        error_log("Upload error: File size too large - " . $file['size']);
        return false;
    }

    $fileName = uniqid() . '.' . $fileExtension;
    $uploadPath = UPLOAD_PATH . $fileName;

    if (!is_dir(UPLOAD_PATH)) {
        if (!mkdir(UPLOAD_PATH, 0755, true)) {
            error_log("Upload error: Cannot create upload directory");
            return false;
        }
    }

    if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
        error_log("Upload success: " . $fileName);
        return $fileName;
    }

    error_log("Upload error: Cannot move uploaded file");
    return false;
}

// 工具函数
function formatPrice($price) {
    return '¥' . number_format($price, 2);
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return '刚刚';
    if ($time < 3600) return floor($time/60) . '分钟前';
    if ($time < 86400) return floor($time/3600) . '小时前';
    if ($time < 2592000) return floor($time/86400) . '天前';
    if ($time < 31536000) return floor($time/2592000) . '个月前';
    
    return floor($time/31536000) . '年前';
}

function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function redirect($url) {
    header("Location: $url");
    exit;
}

// 格式化数字显示
function formatNumber($number) {
    if ($number >= 1000000) {
        return round($number / 1000000, 1) . 'M';
    } elseif ($number >= 1000) {
        return round($number / 1000, 1) . 'K';
    }
    return $number;
}

// 获取分类图标
function getCategoryIcon($categorySlug) {
    $icons = [
        'membership' => 'person-badge',
        'gamecard' => 'controller',
        'software' => 'laptop',
        'ebooks' => 'book',
        'design' => 'palette',
        'courses' => 'mortarboard',
        'media' => 'music-note-beamed',
        'services' => 'gear',
        'others' => 'box'
    ];

    return $icons[$categorySlug] ?? 'box';
}

// 获取分类描述
function getCategoryDescription($categorySlug) {
    $descriptions = [
        'membership' => '各类会员账号，优质服务体验',
        'gamecard' => '游戏点卡充值，畅玩无忧',
        'software' => '正版软件授权，安全可靠',
        'ebooks' => '电子书籍资源，知识宝库',
        'design' => '设计素材资源，创意无限',
        'courses' => '在线教程课程，技能提升',
        'media' => '音乐影视资源，娱乐首选',
        'services' => '虚拟服务定制，专业高效',
        'others' => '其他虚拟商品，应有尽有'
    ];

    return $descriptions[$categorySlug] ?? '优质虚拟商品';
}

// 获取分类商品数量
function getCategoryProductCount($categoryId) {
    global $pdo;

    $stmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE category_id = ? AND status = 'active'");
    $stmt->execute([$categoryId]);

    return $stmt->fetchColumn();
}
