<?php
/**
 * 短信服务类
 * 支持多种短信服务商：阿里云、腾讯云、模拟发送
 */

class SMSService {
    private $provider;
    private $accessKey;
    private $accessSecret;
    private $signName;
    private $templateCode;
    
    public function __construct() {
        $this->provider = SMS_PROVIDER;
        $this->accessKey = SMS_ACCESS_KEY;
        $this->accessSecret = SMS_ACCESS_SECRET;
        $this->signName = SMS_SIGN_NAME;
        $this->templateCode = SMS_TEMPLATE_CODE;
    }
    
    /**
     * 发送验证码短信
     */
    public function sendVerificationCode($phone, $code, $type = 'login') {
        global $pdo;
        
        try {
            // 检查发送频率限制
            if (!$this->checkRateLimit($phone)) {
                return [
                    'success' => false,
                    'message' => '发送过于频繁，请' . SMS_RATE_LIMIT . '秒后再试'
                ];
            }
            
            // 检查每日发送限制
            if (!$this->checkDailyLimit($phone)) {
                return [
                    'success' => false,
                    'message' => '今日发送次数已达上限'
                ];
            }
            
            // 根据不同服务商发送短信
            $result = $this->sendSMS($phone, $code, $type);
            
            if ($result['success']) {
                // 保存验证码到数据库
                $this->saveVerificationCode($phone, $code, $type);
                
                // 记录发送日志
                $this->logSMSSend($phone, $type, 'success');
            } else {
                // 记录发送失败日志
                $this->logSMSSend($phone, $type, 'failed', $result['message']);
            }
            
            return $result;
            
        } catch (Exception $e) {
            error_log("SMS send error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => '短信发送失败，请稍后重试'
            ];
        }
    }
    
    /**
     * 验证验证码
     */
    public function verifyCode($phone, $code, $type = 'login') {
        global $pdo;
        
        try {
            $stmt = $pdo->prepare("
                SELECT * FROM sms_codes 
                WHERE phone = ? AND code = ? AND type = ? AND used = 0 
                AND created_at > DATE_SUB(NOW(), INTERVAL 10 MINUTE)
                ORDER BY created_at DESC LIMIT 1
            ");
            $stmt->execute([$phone, $code, $type]);
            $record = $stmt->fetch();
            
            if ($record) {
                // 标记验证码为已使用
                $updateStmt = $pdo->prepare("UPDATE sms_codes SET used = 1 WHERE id = ?");
                $updateStmt->execute([$record['id']]);
                
                return ['success' => true, 'message' => '验证成功'];
            } else {
                return ['success' => false, 'message' => '验证码错误或已过期'];
            }
            
        } catch (Exception $e) {
            error_log("SMS verify error: " . $e->getMessage());
            return ['success' => false, 'message' => '验证失败，请重试'];
        }
    }
    
    /**
     * 检查发送频率限制
     */
    private function checkRateLimit($phone) {
        global $pdo;
        
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM sms_logs 
            WHERE phone = ? AND created_at > DATE_SUB(NOW(), INTERVAL ? SECOND)
        ");
        $stmt->execute([$phone, SMS_RATE_LIMIT]);
        
        return $stmt->fetchColumn() == 0;
    }
    
    /**
     * 检查每日发送限制
     */
    private function checkDailyLimit($phone) {
        global $pdo;
        
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM sms_logs 
            WHERE phone = ? AND DATE(created_at) = CURDATE()
        ");
        $stmt->execute([$phone]);
        
        return $stmt->fetchColumn() < SMS_DAILY_LIMIT;
    }
    
    /**
     * 根据服务商发送短信
     */
    private function sendSMS($phone, $code, $type) {
        switch ($this->provider) {
            case 'aliyun':
                return $this->sendAliyunSMS($phone, $code, $type);
            case 'tencent':
                return $this->sendTencentSMS($phone, $code, $type);
            case 'mock':
            default:
                return $this->sendMockSMS($phone, $code, $type);
        }
    }
    
    /**
     * 阿里云短信发送
     */
    private function sendAliyunSMS($phone, $code, $type) {
        // 这里实现阿里云短信发送逻辑
        // 需要安装阿里云SDK或使用HTTP API
        
        // 模拟实现
        if ($this->accessKey === 'your_access_key') {
            return $this->sendMockSMS($phone, $code, $type);
        }
        
        // 实际的阿里云API调用代码
        // ...
        
        return ['success' => true, 'message' => '发送成功'];
    }
    
    /**
     * 腾讯云短信发送
     */
    private function sendTencentSMS($phone, $code, $type) {
        // 这里实现腾讯云短信发送逻辑
        return $this->sendMockSMS($phone, $code, $type);
    }
    
    /**
     * 模拟短信发送（开发测试用）
     */
    private function sendMockSMS($phone, $code, $type) {
        // 开发环境下的模拟发送
        error_log("Mock SMS: 向 {$phone} 发送验证码 {$code}，类型：{$type}");
        
        return [
            'success' => true,
            'message' => '验证码发送成功（模拟）',
            'provider' => 'mock'
        ];
    }
    
    /**
     * 保存验证码到数据库
     */
    private function saveVerificationCode($phone, $code, $type) {
        global $pdo;
        
        // 先使之前的验证码失效
        $stmt = $pdo->prepare("UPDATE sms_codes SET used = 1 WHERE phone = ? AND type = ? AND used = 0");
        $stmt->execute([$phone, $type]);
        
        // 插入新验证码
        $stmt = $pdo->prepare("
            INSERT INTO sms_codes (phone, code, type, created_at) 
            VALUES (?, ?, ?, NOW())
        ");
        $stmt->execute([$phone, $code, $type]);
    }
    
    /**
     * 记录短信发送日志
     */
    private function logSMSSend($phone, $type, $status, $error = null) {
        global $pdo;
        
        $stmt = $pdo->prepare("
            INSERT INTO sms_logs (phone, type, status, error_message, created_at) 
            VALUES (?, ?, ?, ?, NOW())
        ");
        $stmt->execute([$phone, $type, $status, $error]);
    }
    
    /**
     * 生成验证码
     */
    public static function generateCode($length = 6) {
        return str_pad(mt_rand(0, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);
    }
    
    /**
     * 获取短信发送统计
     */
    public function getStatistics($phone = null, $days = 7) {
        global $pdo;
        
        $whereClause = "WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)";
        $params = [$days];
        
        if ($phone) {
            $whereClause .= " AND phone = ?";
            $params[] = $phone;
        }
        
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
                COUNT(DISTINCT phone) as unique_phones
            FROM sms_logs 
            {$whereClause}
        ");
        $stmt->execute($params);
        
        return $stmt->fetch();
    }
}

// 全局函数封装
function sendSMSCode($phone, $type = 'login') {
    $sms = new SMSService();
    $code = SMSService::generateCode();
    return $sms->sendVerificationCode($phone, $code, $type);
}

function verifySMSCode($phone, $code, $type = 'login') {
    $sms = new SMSService();
    return $sms->verifyCode($phone, $code, $type);
}
?>
