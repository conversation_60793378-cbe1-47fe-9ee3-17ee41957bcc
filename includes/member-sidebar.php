<?php
// 获取当前页面名称用于高亮导航
$currentPage = basename($_SERVER['PHP_SELF']);

// 获取用户统计数据
try {
    // 获取用户发布商品统计
    $productStatsStmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_products,
            SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_products,
            SUM(views) as total_views,
            SUM(likes) as total_likes
        FROM products 
        WHERE user_id = ?
    ");
    $productStatsStmt->execute([$_SESSION['user_id']]);
    $productStats = $productStatsStmt->fetch();
    
    // 获取订单统计
    $orderStatsStmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_orders,
            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_orders,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_orders
        FROM orders 
        WHERE user_id = ?
    ");
    $orderStatsStmt->execute([$_SESSION['user_id']]);
    $orderStats = $orderStatsStmt->fetch();
    
    // 获取收藏统计
    $favoriteStatsStmt = $pdo->prepare("
        SELECT COUNT(*) as total_favorites
        FROM favorites
        WHERE user_id = ?
    ");
    $favoriteStatsStmt->execute([$_SESSION['user_id']]);
    $favoriteStats = $favoriteStatsStmt->fetch();

    // 获取购物车统计
    $cartStatsStmt = $pdo->prepare("
        SELECT COUNT(*) as cart_count
        FROM cart
        WHERE user_id = ?
    ");
    $cartStatsStmt->execute([$_SESSION['user_id']]);
    $cartStats = $cartStatsStmt->fetch();

} catch (Exception $e) {
    $productStats = ['total_products' => 0, 'active_products' => 0, 'total_views' => 0, 'total_likes' => 0];
    $orderStats = ['total_orders' => 0, 'pending_orders' => 0, 'completed_orders' => 0];
    $favoriteStats = ['total_favorites' => 0];
    $cartStats = ['cart_count' => 0];
}

// 导航菜单配置
$menuItems = [
    'trade' => [
        'title' => '我的交易',
        'items' => [
            ['url' => 'my-orders.php', 'icon' => 'bi-bag', 'text' => '我的订单', 'badge' => $orderStats['pending_orders']],
            ['url' => 'cart.php', 'icon' => 'bi-cart', 'text' => '购物车', 'badge' => $cartStats['cart_count']],
            ['url' => 'my-favorites.php', 'icon' => 'bi-heart', 'text' => '我的收藏', 'badge' => $favoriteStats['total_favorites']],
            ['url' => 'browse-history.php', 'icon' => 'bi-clock-history', 'text' => '浏览历史'],
            ['url' => 'wallet.php', 'icon' => 'bi-wallet2', 'text' => '我的钱包'],
            ['url' => 'address.php', 'icon' => 'bi-geo-alt', 'text' => '收货地址']
        ]
    ],
    'business' => [
        'title' => '我的数字鱼',
        'items' => [
            ['url' => 'my-products.php', 'icon' => 'bi-box-seam', 'text' => '我的发布', 'badge' => $productStats['total_products']],
            ['url' => 'my-reviews.php', 'icon' => 'bi-star', 'text' => '我的评价'],
            ['url' => 'verification.php', 'icon' => 'bi-shield-check', 'text' => '实名认证'],
            ['url' => 'analytics.php', 'icon' => 'bi-graph-up', 'text' => '数据分析']
        ]
    ],
    'settings' => [
        'title' => '设置',
        'items' => [
            ['url' => 'settings.php', 'icon' => 'bi-person-gear', 'text' => '账号设置'],
            ['url' => 'security.php', 'icon' => 'bi-shield-lock', 'text' => '安全设置'],
            ['url' => 'notifications.php', 'icon' => 'bi-bell', 'text' => '消息设置'],
            ['url' => 'help.php', 'icon' => 'bi-question-circle', 'text' => '帮助中心'],
            ['url' => 'logout.php', 'icon' => 'bi-box-arrow-right', 'text' => '退出登录', 'class' => 'logout-item']
        ]
    ]
];
?>

<!-- 侧边栏导航 -->
<div class="member-sidebar">
    <!-- 用户信息卡片 -->
    <div class="user-profile-card">
        <div class="user-avatar-section">
            <div class="user-avatar">
                <img src="<?php echo $currentUser['avatar'] ?: 'images/avatar-default.svg'; ?>" alt="用户头像" id="avatarImg">
                <div class="avatar-edit" onclick="triggerAvatarUpload()">
                    <i class="bi bi-camera"></i>
                </div>
                <input type="file" id="avatarUpload" accept="image/*" style="display: none;">
            </div>
            <div class="user-info">
                <div class="user-name"><?php echo htmlspecialchars($currentUser['nickname']); ?></div>
                <div class="user-level">
                    <div class="level-badge">
                        <i class="bi bi-star-fill"></i>
                        <span><?php echo number_format($currentUser['rating'] ?? 5.0, 1); ?></span>
                    </div>
                    <div class="level-text">信誉等级</div>
                </div>
            </div>
        </div>
        
        <!-- 用户统计 -->
        <div class="user-stats">
            <div class="stat-item">
                <div class="stat-value"><?php echo $productStats['total_views']; ?></div>
                <div class="stat-label">总浏览</div>
            </div>
            <div class="stat-item">
                <div class="stat-value"><?php echo $productStats['total_likes']; ?></div>
                <div class="stat-label">获赞数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value"><?php echo $orderStats['completed_orders']; ?></div>
                <div class="stat-label">成交数</div>
            </div>
        </div>
    </div>
    
    <!-- 导航菜单 -->
    <div class="sidebar-menu">
        <?php foreach ($menuItems as $groupKey => $group): ?>
            <div class="menu-group">
                <div class="menu-title"><?php echo $group['title']; ?></div>
                <ul class="menu-list">
                    <?php foreach ($group['items'] as $item): ?>
                        <?php 
                        $isActive = ($currentPage === $item['url']) || 
                                   ($currentPage === 'member.php' && $item['url'] === 'member.php');
                        $itemClass = $isActive ? 'active' : '';
                        if (isset($item['class'])) {
                            $itemClass .= ' ' . $item['class'];
                        }
                        ?>
                        <li class="<?php echo $itemClass; ?>">
                            <a href="<?php echo $item['url']; ?>">
                                <i class="<?php echo $item['icon']; ?>"></i>
                                <span><?php echo $item['text']; ?></span>
                                <?php if (isset($item['badge']) && $item['badge'] > 0): ?>
                                    <span class="menu-badge"><?php echo $item['badge']; ?></span>
                                <?php endif; ?>
                            </a>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endforeach; ?>
    </div>
</div>

<script>
function triggerAvatarUpload() {
    document.getElementById('avatarUpload').click();
}

document.getElementById('avatarUpload').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        // 这里可以添加头像上传逻辑
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('avatarImg').src = e.target.result;
        };
        reader.readAsDataURL(file);
        
        // 实际项目中应该上传到服务器
        console.log('头像上传功能待实现');
    }
});
</script>
