<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>购买流程测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #ff6b35;
            padding-bottom: 10px;
        }
        .btn {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #e55a2b;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-success {
            background: #28a745;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .status-item {
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            text-align: center;
        }
        .status-item h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .status-value {
            font-size: 24px;
            font-weight: bold;
            color: #ff6b35;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🛒 购买功能完整测试</h1>
        <p>测试虚拟商品交易平台的购买流程和相关功能</p>
    </div>

    <div class="test-section">
        <h3>1. 数据库状态检查</h3>
        <p>检查数据库表结构和基础数据</p>
        <button class="btn" onclick="checkDatabase()">检查数据库</button>
        <button class="btn btn-secondary" onclick="fixDatabase()">修复数据库</button>
        <div id="db-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. 购买API测试</h3>
        <p>测试购买接口的各种情况</p>
        <button class="btn" onclick="testPurchaseAPI()">测试购买API</button>
        <button class="btn btn-secondary" onclick="testCartAPI()">测试购物车API</button>
        <div id="api-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. 页面功能测试</h3>
        <p>测试相关页面的功能</p>
        <a href="test-purchase-simple.php" class="btn" target="_blank">简单购买测试</a>
        <a href="my-orders.php" class="btn btn-secondary" target="_blank">查看订单页面</a>
        <a href="cart.php" class="btn btn-secondary" target="_blank">购物车页面</a>
        <div id="page-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>4. 系统状态监控</h3>
        <div class="status-grid">
            <div class="status-item">
                <h4>数据库连接</h4>
                <div class="status-value" id="db-status">检查中...</div>
            </div>
            <div class="status-item">
                <h4>商品数量</h4>
                <div class="status-value" id="product-count">-</div>
            </div>
            <div class="status-item">
                <h4>订单数量</h4>
                <div class="status-value" id="order-count">-</div>
            </div>
            <div class="status-item">
                <h4>用户数量</h4>
                <div class="status-value" id="user-count">-</div>
            </div>
        </div>
        <button class="btn btn-success" onclick="refreshStatus()">刷新状态</button>
    </div>

    <script>
    function showResult(elementId, message, isSuccess = true) {
        const resultDiv = document.getElementById(elementId);
        resultDiv.className = 'result ' + (isSuccess ? 'success' : 'error');
        resultDiv.innerHTML = message;
        resultDiv.style.display = 'block';
    }

    function checkDatabase() {
        fetch('debug-purchase.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let message = '<h4>数据库状态正常</h4>';
                message += `<p><strong>商品数量:</strong> ${data.product_count}</p>`;
                message += `<p><strong>用户ID:</strong> ${data.session_user_id}</p>`;
                message += `<p><strong>Virtual表存在:</strong> ${data.virtual_table_exists ? '是' : '否'}</p>`;
                message += '<p><strong>所有表:</strong> ' + data.all_tables.join(', ') + '</p>';
                message += '<h5>Orders表结构:</h5><pre>' + data.orders_structure.join('\\n') + '</pre>';
                showResult('db-result', message, true);
            } else {
                showResult('db-result', `数据库检查失败: ${data.error}`, false);
            }
        })
        .catch(error => {
            showResult('db-result', `检查失败: ${error.message}`, false);
        });
    }

    function fixDatabase() {
        fetch('fix-orders-table.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let message = '<h4>数据库修复完成</h4>';
                message += '<ul>';
                data.updates.forEach(update => {
                    message += `<li>${update}</li>`;
                });
                message += '</ul>';
                showResult('db-result', message, true);
            } else {
                showResult('db-result', `修复失败: ${data.error}`, false);
            }
        })
        .catch(error => {
            showResult('db-result', `修复失败: ${error.message}`, false);
        });
    }

    function testPurchaseAPI() {
        // 模拟购买请求
        const formData = new FormData();
        formData.append('product_id', 1);
        formData.append('quantity', 1);
        formData.append('action', 'buy_now');

        fetch('api/purchase.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(text => {
            try {
                const data = JSON.parse(text);
                if (data.success) {
                    showResult('api-result', `购买API测试成功！订单ID: ${data.order_id}`, true);
                } else {
                    showResult('api-result', `购买API返回错误: ${data.message}`, false);
                }
            } catch (e) {
                showResult('api-result', `JSON解析错误: ${e.message}<br><strong>原始响应:</strong><pre>${text}</pre>`, false);
            }
        })
        .catch(error => {
            showResult('api-result', `API请求失败: ${error.message}`, false);
        });
    }

    function testCartAPI() {
        const formData = new FormData();
        formData.append('action', 'add');
        formData.append('product_id', 1);
        formData.append('quantity', 1);

        fetch('api/cart.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showResult('api-result', '购物车API测试成功！', true);
            } else {
                showResult('api-result', `购物车API错误: ${data.message}`, false);
            }
        })
        .catch(error => {
            showResult('api-result', `购物车API请求失败: ${error.message}`, false);
        });
    }

    function refreshStatus() {
        // 更新各种状态
        document.getElementById('db-status').textContent = '检查中...';
        document.getElementById('product-count').textContent = '-';
        document.getElementById('order-count').textContent = '-';
        document.getElementById('user-count').textContent = '-';

        fetch('debug-purchase.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('db-status').textContent = '正常';
                document.getElementById('product-count').textContent = data.product_count;
                
                // 获取订单和用户数量
                return fetch('api/get-stats.php');
            } else {
                document.getElementById('db-status').textContent = '错误';
                throw new Error(data.error);
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('order-count').textContent = data.order_count || 0;
                document.getElementById('user-count').textContent = data.user_count || 0;
            }
        })
        .catch(error => {
            console.error('Status refresh error:', error);
            document.getElementById('db-status').textContent = '错误';
        });
    }

    // 页面加载时自动刷新状态
    document.addEventListener('DOMContentLoaded', function() {
        refreshStatus();
    });
    </script>
</body>
</html>
