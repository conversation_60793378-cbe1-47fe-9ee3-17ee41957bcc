<?php
// 测试页面 - 验证新功能
require_once 'includes/functions.php';

// 检查登录状态
if (!isLoggedIn()) {
    $_SESSION['error_message'] = '请先登录';
    redirect('login.php');
}

$currentUser = getCurrentUser();

// 现在包含header
$pageTitle = '功能测试';
$additionalCSS = ['css/member.css'];
require_once 'includes/header.php';
?>

<!-- 主要内容区 -->
<main class="main member-page">
    <div class="container">
        <div class="member-container">
            <!-- 侧边栏 -->
            <?php include 'includes/member-sidebar.php'; ?>

            <!-- 主内容区 -->
            <div class="member-content">
                <!-- 页面标题 -->
                <div class="page-header">
                    <div class="page-title-section">
                        <h1 class="page-title">功能测试</h1>
                        <p class="page-subtitle">测试新增功能是否正常工作</p>
                    </div>
                </div>

                <!-- 功能测试卡片 -->
                <div class="test-grid">
                    <!-- 图片上传测试 -->
                    <div class="test-card">
                        <h3>图片上传功能</h3>
                        <p>测试商品图片上传是否正常工作</p>
                        <div class="test-actions">
                            <a href="publish.php" class="btn btn-primary">
                                <i class="bi bi-cloud-upload"></i>
                                <span>测试发布商品</span>
                            </a>
                        </div>
                        <div class="test-status">
                            <span class="status-badge status-info">待测试</span>
                        </div>
                    </div>

                    <!-- 购物车功能测试 -->
                    <div class="test-card">
                        <h3>购物车功能</h3>
                        <p>测试购物车添加、删除、更新功能</p>
                        <div class="test-actions">
                            <a href="cart.php" class="btn btn-primary">
                                <i class="bi bi-cart"></i>
                                <span>查看购物车</span>
                            </a>
                            <button class="btn btn-outline" onclick="testCartAPI()">
                                <i class="bi bi-gear"></i>
                                <span>测试API</span>
                            </button>
                        </div>
                        <div class="test-status">
                            <span class="status-badge status-info">待测试</span>
                        </div>
                    </div>

                    <!-- 钱包功能测试 -->
                    <div class="test-card">
                        <h3>钱包功能</h3>
                        <p>测试钱包充值、提现、交易记录功能</p>
                        <div class="test-actions">
                            <a href="wallet.php" class="btn btn-primary">
                                <i class="bi bi-wallet2"></i>
                                <span>查看钱包</span>
                            </a>
                        </div>
                        <div class="test-status">
                            <span class="status-badge status-info">待测试</span>
                        </div>
                    </div>

                    <!-- 实名认证测试 -->
                    <div class="test-card">
                        <h3>实名认证功能</h3>
                        <p>测试实名认证申请和状态显示</p>
                        <div class="test-actions">
                            <a href="verification.php" class="btn btn-primary">
                                <i class="bi bi-shield-check"></i>
                                <span>实名认证</span>
                            </a>
                        </div>
                        <div class="test-status">
                            <span class="status-badge status-info">待测试</span>
                        </div>
                    </div>

                    <!-- 用户菜单测试 -->
                    <div class="test-card">
                        <h3>用户菜单优化</h3>
                        <p>测试右上角用户菜单在移动端的显示效果</p>
                        <div class="test-actions">
                            <button class="btn btn-primary" onclick="testUserMenu()">
                                <i class="bi bi-phone"></i>
                                <span>模拟移动端</span>
                            </button>
                        </div>
                        <div class="test-status">
                            <span class="status-badge status-info">待测试</span>
                        </div>
                    </div>

                    <!-- 数据库测试 -->
                    <div class="test-card">
                        <h3>数据库表结构</h3>
                        <p>检查新增的数据库表是否创建成功</p>
                        <div class="test-actions">
                            <button class="btn btn-primary" onclick="testDatabase()">
                                <i class="bi bi-database"></i>
                                <span>检查表结构</span>
                            </button>
                        </div>
                        <div class="test-status">
                            <span class="status-badge status-info">待测试</span>
                        </div>
                    </div>
                </div>

                <!-- 测试结果显示区域 -->
                <div class="test-results" id="testResults" style="display: none;">
                    <h3>测试结果</h3>
                    <div class="results-content" id="resultsContent">
                        <!-- 测试结果将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<style>
.test-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.test-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #f1f3f4;
    position: relative;
}

.test-card h3 {
    margin: 0 0 12px 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.test-card p {
    margin: 0 0 20px 0;
    color: #6c757d;
    line-height: 1.5;
}

.test-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.test-status {
    position: absolute;
    top: 16px;
    right: 16px;
}

.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-info {
    background: #d1ecf1;
    color: #0c5460;
}

.status-success {
    background: #d4edda;
    color: #155724;
}

.status-danger {
    background: #f8d7da;
    color: #721c24;
}

.test-results {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #f1f3f4;
}

.test-results h3 {
    margin: 0 0 16px 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.results-content {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    font-family: monospace;
    font-size: 14px;
    line-height: 1.5;
    white-space: pre-wrap;
}
</style>

<script>
// 测试购物车API
function testCartAPI() {
    showTestResult('开始测试购物车API...\n');

    // 测试获取购物车数量
    fetch('api/cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=count'
    })
    .then(response => {
        console.log('Cart API Response status:', response.status);
        return response.text();
    })
    .then(text => {
        console.log('Cart API Raw response:', text);
        try {
            const data = JSON.parse(text);
            if (data.success) {
                showTestResult(`✅ 购物车数量API测试: 成功\n`);
                showTestResult(`当前购物车商品数量: ${data.count || 0}\n\n`);
            } else {
                showTestResult(`❌ 购物车API返回错误: ${data.message}\n\n`);
            }
        } catch (e) {
            showTestResult(`❌ 购物车API JSON解析失败: ${e.message}\n`);
            showTestResult(`原始响应: ${text}\n\n`);
        }
    })
    .catch(error => {
        showTestResult(`❌ 购物车API网络错误: ${error.message}\n\n`);
    });
}

// 测试用户菜单
function testUserMenu() {
    showTestResult('测试用户菜单移动端适配...\n');
    
    // 模拟移动端视口
    const viewport = document.querySelector('meta[name="viewport"]');
    if (viewport) {
        showTestResult('视口设置: ' + viewport.content + '\n');
    }
    
    // 检查CSS媒体查询
    const userInfo = document.querySelector('.user-info');
    if (userInfo) {
        const styles = window.getComputedStyle(userInfo);
        showTestResult(`用户信息区域样式: gap=${styles.gap}\n`);
    }
    
    showTestResult('用户菜单测试完成\n\n');
}

// 测试数据库
function testDatabase() {
    showTestResult('检查数据库表结构...\n');
    
    // 这里可以通过AJAX调用后端脚本来检查数据库
    fetch('api/test-db.php')
    .then(response => response.json())
    .then(data => {
        showTestResult(`数据库测试: ${data.success ? '成功' : '失败'}\n`);
        if (data.tables) {
            showTestResult('已创建的表:\n');
            data.tables.forEach(table => {
                showTestResult(`- ${table}\n`);
            });
        }
        showTestResult('\n');
    })
    .catch(error => {
        showTestResult(`数据库测试失败: ${error.message}\n\n`);
    });
}

// 显示测试结果
function showTestResult(message) {
    const resultsDiv = document.getElementById('testResults');
    const contentDiv = document.getElementById('resultsContent');
    
    resultsDiv.style.display = 'block';
    contentDiv.textContent += message;
    
    // 滚动到底部
    contentDiv.scrollTop = contentDiv.scrollHeight;
}
</script>

<?php require_once 'includes/footer.php'; ?>
