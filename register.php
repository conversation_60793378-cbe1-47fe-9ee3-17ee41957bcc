<?php
$pageTitle = '注册';
require_once 'includes/header.php';

// 如果已登录，重定向到首页
if (isLoggedIn()) {
    redirect('index.php');
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = sanitizeInput($_POST['username']);
    $email = sanitizeInput($_POST['email']);
    $phone = sanitizeInput($_POST['phone']);
    $password = $_POST['password'];
    $confirmPassword = $_POST['confirmPassword'];
    $nickname = sanitizeInput($_POST['nickname']);
    $agreement = isset($_POST['agreement']);
    
    // 验证输入
    if (empty($username) || empty($email) || empty($phone) || empty($password) || empty($nickname)) {
        $error = '请填写完整的注册信息';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = '请输入有效的邮箱地址';
    } elseif (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
        $error = '请输入有效的手机号码';
    } elseif (strlen($password) < 8) {
        $error = '密码长度至少8位';
    } elseif ($password !== $confirmPassword) {
        $error = '两次输入的密码不一致';
    } elseif (!$agreement) {
        $error = '请同意用户协议和隐私政策';
    } else {
        // 生成用户名（如果为空）
        if (empty($username)) {
            $username = 'user_' . substr($phone, -4) . rand(1000, 9999);
        }
        
        $userData = [
            'username' => $username,
            'email' => $email,
            'phone' => $phone,
            'password' => $password,
            'nickname' => $nickname
        ];
        
        if (register($userData)) {
            $_SESSION['success_message'] = '注册成功！请登录';
            redirect('login.php');
        } else {
            $error = '注册失败，用户名、邮箱或手机号可能已存在';
        }
    }
}
?>

<!-- 主要内容区 -->
<main class="main register-page">
    <div class="container">
        <div class="auth-container">
            <div class="auth-header">
                <h2>注册数字鱼账号</h2>
                <p>已有账号？<a href="login.php">立即登录</a></p>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-error"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>
            
            <form class="auth-form" id="registerForm" method="POST">
                <div class="form-group">
                    <label for="username">用户名（选填）</label>
                    <input type="text" id="username" name="username" placeholder="留空将自动生成" value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>">
                </div>
                
                <div class="form-group">
                    <label for="email">邮箱地址</label>
                    <input type="email" id="email" name="email" placeholder="请输入邮箱地址" required value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                </div>
                
                <div class="form-group">
                    <label for="phone">手机号码</label>
                    <div class="input-group">
                        <span class="input-prefix">+86</span>
                        <input type="tel" id="phone" name="phone" placeholder="请输入手机号码" required value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password">设置密码</label>
                    <div class="input-group">
                        <input type="password" id="password" name="password" placeholder="请设置登录密码" required>
                        <span class="toggle-password"><i class="bi bi-eye"></i></span>
                    </div>
                    <div class="password-strength">
                        <div class="strength-bar">
                            <div class="strength-level" data-level="0"></div>
                        </div>
                        <div class="strength-text">密码强度：弱</div>
                    </div>
                    <p class="form-tip">密码长度8-20位，必须包含字母、数字</p>
                </div>
                
                <div class="form-group">
                    <label for="confirmPassword">确认密码</label>
                    <div class="input-group">
                        <input type="password" id="confirmPassword" name="confirmPassword" placeholder="请再次输入密码" required>
                        <span class="toggle-password"><i class="bi bi-eye"></i></span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="nickname">昵称</label>
                    <input type="text" id="nickname" name="nickname" placeholder="请设置昵称" required value="<?php echo isset($_POST['nickname']) ? htmlspecialchars($_POST['nickname']) : ''; ?>">
                </div>
                
                <div class="form-group checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="agreement" required>
                        <span>我已阅读并同意</span>
                    </label>
                    <a href="#" class="agreement-link">《数字鱼用户协议》</a>
                    <a href="#" class="agreement-link">《隐私政策》</a>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="auth-submit-btn">注册</button>
                </div>
                
                <div class="other-login">
                    <p class="divider"><span>其他方式登录</span></p>
                    <div class="social-login">
                        <a href="#" class="social-icon"><i class="bi bi-wechat"></i></a>
                        <a href="#" class="social-icon"><i class="bi bi-qq"></i></a>
                        <a href="#" class="social-icon"><i class="bi bi-apple"></i></a>
                        <a href="#" class="social-icon"><i class="bi bi-envelope"></i></a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</main>

<script>
// 密码显示/隐藏
document.querySelectorAll('.toggle-password').forEach(toggle => {
    toggle.addEventListener('click', function() {
        const input = this.previousElementSibling;
        const icon = this.querySelector('i');
        
        if (input.type === 'password') {
            input.type = 'text';
            icon.className = 'bi bi-eye-slash';
        } else {
            input.type = 'password';
            icon.className = 'bi bi-eye';
        }
    });
});

// 密码强度检测
document.getElementById('password').addEventListener('input', function() {
    const password = this.value;
    const strengthBar = document.querySelector('.strength-level');
    const strengthText = document.querySelector('.strength-text');
    
    let strength = 0;
    let text = '弱';
    
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    
    if (strength >= 4) {
        text = '强';
        strengthBar.className = 'strength-level strong';
    } else if (strength >= 2) {
        text = '中';
        strengthBar.className = 'strength-level medium';
    } else {
        text = '弱';
        strengthBar.className = 'strength-level weak';
    }
    
    strengthText.textContent = '密码强度：' + text;
});
</script>

<?php require_once 'includes/footer.php'; ?>
