<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 闲鱼</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/bootstrap-icons.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <a href="index.html">
                    <img src="images/logo.svg" alt="闲鱼logo">
                </a>
            </div>
            <nav class="nav">
                <ul>
                    <li><a href="index.html">首页</a></li>
                    <li><a href="register.html">注册</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- 主要内容区 -->
    <main class="main login-page">
        <div class="container">
            <div class="auth-container">
                <div class="auth-header">
                    <h2>登录闲鱼</h2>
                    <p>还没有账号？<a href="register.html">立即注册</a></p>
                </div>
                
                <div class="login-tabs">
                    <div class="login-tab active" data-tab="phone">手机号登录</div>
                    <div class="login-tab" data-tab="password">账号密码登录</div>
                </div>
                
                <!-- 手机号登录表单 -->
                <form class="auth-form login-form active" id="phoneLoginForm" data-form="phone">
                    <div class="form-group">
                        <label for="loginPhone">手机号码</label>
                        <div class="input-group">
                            <span class="input-prefix">+86</span>
                            <input type="tel" id="loginPhone" name="phone" placeholder="请输入手机号码" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="loginVerifyCode">验证码</label>
                        <div class="input-group">
                            <input type="text" id="loginVerifyCode" name="verifyCode" placeholder="请输入验证码" required>
                            <button type="button" class="verify-code-btn">获取验证码</button>
                        </div>
                    </div>
                    
                    <div class="form-group checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="rememberPhone" checked>
                            <span>记住登录</span>
                        </label>
                        <a href="#" class="forgot-link">登录遇到问题？</a>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="auth-submit-btn">登录</button>
                    </div>
                </form>
                
                <!-- 账号密码登录表单 -->
                <form class="auth-form login-form" id="passwordLoginForm" data-form="password">
                    <div class="form-group">
                        <label for="loginUsername">账号</label>
                        <input type="text" id="loginUsername" name="username" placeholder="手机号/邮箱/用户名" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="loginPassword">密码</label>
                        <div class="input-group">
                            <input type="password" id="loginPassword" name="password" placeholder="请输入密码" required>
                            <span class="toggle-password"><i class="bi bi-eye"></i></span>
                        </div>
                    </div>
                    
                    <div class="form-group checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="rememberPassword" checked>
                            <span>记住登录</span>
                        </label>
                        <a href="#" class="forgot-link">忘记密码？</a>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="auth-submit-btn">登录</button>
                    </div>
                </form>
                
                <div class="other-login">
                    <p class="divider"><span>其他方式登录</span></p>
                    <div class="social-login">
                        <a href="#" class="social-icon"><i class="bi bi-wechat"></i></a>
                        <a href="#" class="social-icon"><i class="bi bi-qq"></i></a>
                        <a href="#" class="social-icon"><i class="bi bi-apple"></i></a>
                        <a href="#" class="social-icon"><i class="bi bi-envelope"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 底部信息 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-links">
                <div class="link-group">
                    <h4>关于我们</h4>
                    <ul>
                        <li><a href="#">平台介绍</a></li>
                        <li><a href="#">联系我们</a></li>
                        <li><a href="#">加入我们</a></li>
                    </ul>
                </div>
                <div class="link-group">
                    <h4>帮助中心</h4>
                    <ul>
                        <li><a href="#">新手指南</a></li>
                        <li><a href="#">常见问题</a></li>
                        <li><a href="#">意见反馈</a></li>
                    </ul>
                </div>
                <div class="link-group">
                    <h4>交易保障</h4>
                    <ul>
                        <li><a href="#">闲鱼规则</a></li>
                        <li><a href="#">安全保障</a></li>
                        <li><a href="#">隐私政策</a></li>
                    </ul>
                </div>
                <div class="link-group">
                    <h4>特色服务</h4>
                    <ul>
                        <li><a href="#">闲鱼寄卖</a></li>
                        <li><a href="#">闲鱼严选</a></li>
                        <li><a href="#">闲鱼优品</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="copyright">
                    <p>© 2023 闲鱼 版权所有</p>
                </div>
                <div class="footer-info">
                    <p>浙ICP备XXXXXXXX号-X | 浙公网安备XXXXXXXXXXXX号</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
</body>
</html>