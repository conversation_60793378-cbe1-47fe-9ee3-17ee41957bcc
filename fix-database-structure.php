<?php
// 修复数据库表结构
require_once 'includes/functions.php';

echo "开始修复数据库表结构...\n\n";

try {
    $fixes = [];
    
    // 1. 检查并添加orders表的order_number字段
    echo "1. 检查orders表结构...\n";
    $stmt = $pdo->query("SHOW COLUMNS FROM orders LIKE 'order_number'");
    $hasOrderNumber = $stmt->fetch() !== false;
    
    if (!$hasOrderNumber) {
        echo "   添加order_number字段...\n";
        $pdo->exec("ALTER TABLE orders ADD COLUMN order_number VARCHAR(50) UNIQUE AFTER id");
        $fixes[] = "添加 order_number 字段";
        
        // 为现有订单生成订单号
        echo "   为现有订单生成订单号...\n";
        $stmt = $pdo->query("SELECT id, created_at FROM orders WHERE order_number IS NULL OR order_number = ''");
        $orders = $stmt->fetchAll();
        
        foreach ($orders as $order) {
            $orderNumber = 'XY' . date('YmdHis', strtotime($order['created_at'])) . str_pad($order['id'], 4, '0', STR_PAD_LEFT);
            $updateStmt = $pdo->prepare("UPDATE orders SET order_number = ? WHERE id = ?");
            $updateStmt->execute([$orderNumber, $order['id']]);
        }
        $fixes[] = "为 " . count($orders) . " 个现有订单生成订单号";
    } else {
        echo "   order_number字段已存在\n";
        $fixes[] = "order_number 字段已存在";
    }
    
    // 2. 检查并添加products表的is_virtual字段
    echo "\n2. 检查products表结构...\n";
    $stmt = $pdo->query("SHOW COLUMNS FROM products LIKE 'is_virtual'");
    $hasIsVirtual = $stmt->fetch() !== false;
    
    if (!$hasIsVirtual) {
        echo "   添加is_virtual字段...\n";
        $pdo->exec("ALTER TABLE products ADD COLUMN is_virtual BOOLEAN DEFAULT TRUE AFTER stock");
        $fixes[] = "添加 is_virtual 字段";
        
        // 将所有现有商品设为虚拟商品
        echo "   设置现有商品为虚拟商品...\n";
        $pdo->exec("UPDATE products SET is_virtual = TRUE WHERE is_virtual IS NULL");
        $fixes[] = "设置现有商品为虚拟商品";
    } else {
        echo "   is_virtual字段已存在\n";
        $fixes[] = "is_virtual 字段已存在";
    }
    
    // 3. 检查virtual_attributes表
    echo "\n3. 检查virtual_attributes表...\n";
    $stmt = $pdo->query("SHOW TABLES LIKE 'virtual_attributes'");
    $virtualTableExists = $stmt->fetch() !== false;
    
    if (!$virtualTableExists) {
        echo "   创建virtual_attributes表...\n";
        $sql = "CREATE TABLE virtual_attributes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            product_id INT NOT NULL,
            delivery_method ENUM('manual', 'automatic') DEFAULT 'manual',
            content TEXT,
            download_link VARCHAR(255),
            activation_code VARCHAR(100),
            instructions TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
            UNIQUE KEY unique_product_virtual (product_id)
        )";
        $pdo->exec($sql);
        $fixes[] = "创建 virtual_attributes 表";
    } else {
        echo "   virtual_attributes表已存在\n";
        $fixes[] = "virtual_attributes 表已存在";
    }
    
    // 4. 添加必要的索引
    echo "\n4. 添加数据库索引...\n";
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_order_number ON orders(order_number)",
        "CREATE INDEX IF NOT EXISTS idx_buyer_id ON orders(buyer_id)",
        "CREATE INDEX IF NOT EXISTS idx_seller_id ON orders(seller_id)",
        "CREATE INDEX IF NOT EXISTS idx_status ON orders(status)",
        "CREATE INDEX IF NOT EXISTS idx_product_status ON products(status)",
        "CREATE INDEX IF NOT EXISTS idx_product_user ON products(user_id)"
    ];
    
    foreach ($indexes as $indexSql) {
        try {
            $pdo->exec($indexSql);
            echo "   添加索引成功\n";
        } catch (Exception $e) {
            echo "   索引可能已存在: " . $e->getMessage() . "\n";
        }
    }
    $fixes[] = "添加数据库索引";
    
    // 5. 检查数据完整性
    echo "\n5. 检查数据完整性...\n";
    
    // 检查用户数量
    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
    $userCount = $stmt->fetchColumn();
    echo "   用户数量: $userCount\n";
    
    // 检查商品数量
    $stmt = $pdo->query("SELECT COUNT(*) FROM products WHERE status = 'active'");
    $productCount = $stmt->fetchColumn();
    echo "   活跃商品数量: $productCount\n";
    
    // 检查订单数量
    $stmt = $pdo->query("SELECT COUNT(*) FROM orders");
    $orderCount = $stmt->fetchColumn();
    echo "   订单数量: $orderCount\n";
    
    echo "\n✅ 数据库表结构修复完成！\n";
    echo "修复内容:\n";
    foreach ($fixes as $fix) {
        echo "- $fix\n";
    }
    
    echo "\n数据库状态:\n";
    echo "- 用户数量: $userCount\n";
    echo "- 商品数量: $productCount\n";
    echo "- 订单数量: $orderCount\n";
    
} catch (Exception $e) {
    echo "\n❌ 修复失败: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
?>
