<?php
// 先处理所有逻辑，再包含header
require_once 'includes/functions.php';

// 检查登录状态
if (!isLoggedIn()) {
    $_SESSION['error_message'] = '请先登录';
    redirect('login.php');
}

$currentUser = getCurrentUser();

// 初始化用户钱包（如果不存在）
try {
    $stmt = $pdo->prepare("SELECT * FROM wallet WHERE user_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $wallet = $stmt->fetch();
    
    if (!$wallet) {
        $stmt = $pdo->prepare("INSERT INTO wallet (user_id, balance, frozen_amount) VALUES (?, 0.00, 0.00)");
        $stmt->execute([$_SESSION['user_id']]);
        
        $stmt = $pdo->prepare("SELECT * FROM wallet WHERE user_id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $wallet = $stmt->fetch();
    }
} catch (Exception $e) {
    $error = '获取钱包信息失败：' . $e->getMessage();
    $wallet = ['balance' => 0, 'frozen_amount' => 0];
}

// 处理钱包操作
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    $amount = floatval($_POST['amount'] ?? 0);
    
    if ($amount <= 0) {
        $_SESSION['error_message'] = '金额必须大于0';
        redirect('wallet.php');
    }
    
    try {
        $pdo->beginTransaction();
        
        switch ($action) {
            case 'deposit':
                // 充值（这里简化处理，实际应该对接支付接口）
                $stmt = $pdo->prepare("UPDATE wallet SET balance = balance + ? WHERE user_id = ?");
                $stmt->execute([$amount, $_SESSION['user_id']]);
                
                // 记录交易
                $stmt = $pdo->prepare("SELECT balance FROM wallet WHERE user_id = ?");
                $stmt->execute([$_SESSION['user_id']]);
                $newBalance = $stmt->fetchColumn();
                
                $stmt = $pdo->prepare("
                    INSERT INTO wallet_transactions (user_id, type, amount, balance_after, description) 
                    VALUES (?, 'deposit', ?, ?, ?)
                ");
                $stmt->execute([$_SESSION['user_id'], $amount, $newBalance, '账户充值']);
                
                $_SESSION['success_message'] = '充值成功';
                break;
                
            case 'withdraw':
                // 提现
                if ($amount > $wallet['balance']) {
                    throw new Exception('余额不足');
                }
                
                $stmt = $pdo->prepare("UPDATE wallet SET balance = balance - ? WHERE user_id = ?");
                $stmt->execute([$amount, $_SESSION['user_id']]);
                
                // 记录交易
                $stmt = $pdo->prepare("SELECT balance FROM wallet WHERE user_id = ?");
                $stmt->execute([$_SESSION['user_id']]);
                $newBalance = $stmt->fetchColumn();
                
                $stmt = $pdo->prepare("
                    INSERT INTO wallet_transactions (user_id, type, amount, balance_after, description, status) 
                    VALUES (?, 'withdraw', ?, ?, ?, 'pending')
                ");
                $stmt->execute([$_SESSION['user_id'], $amount, $newBalance, '申请提现']);
                
                $_SESSION['success_message'] = '提现申请已提交，预计1-3个工作日到账';
                break;
        }
        
        $pdo->commit();
    } catch (Exception $e) {
        $pdo->rollback();
        $_SESSION['error_message'] = '操作失败：' . $e->getMessage();
    }
    
    redirect('wallet.php');
}

// 获取交易记录
try {
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = 10;
    $offset = ($page - 1) * $limit;
    
    $stmt = $pdo->prepare("
        SELECT * FROM wallet_transactions 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([$_SESSION['user_id'], $limit, $offset]);
    $transactions = $stmt->fetchAll();
    
    // 获取总记录数
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM wallet_transactions WHERE user_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $totalTransactions = $stmt->fetchColumn();
    $totalPages = ceil($totalTransactions / $limit);
} catch (Exception $e) {
    $transactions = [];
    $totalPages = 0;
}

// 重新获取钱包信息
try {
    $stmt = $pdo->prepare("SELECT * FROM wallet WHERE user_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $wallet = $stmt->fetch();
} catch (Exception $e) {
    // 使用默认值
}

// 现在包含header，因为所有逻辑处理已完成
$pageTitle = '我的钱包';
$additionalCSS = ['css/member.css', 'css/wallet.css'];
require_once 'includes/header.php';
?>

<!-- 主要内容区 -->
<main class="main member-page">
    <div class="container">
        <div class="member-container">
            <!-- 侧边栏 -->
            <?php include 'includes/member-sidebar.php'; ?>

            <!-- 主内容区 -->
            <div class="member-content">
                <!-- 页面标题 -->
                <div class="page-header">
                    <div class="page-title-section">
                        <h1 class="page-title">我的钱包</h1>
                        <p class="page-subtitle">管理您的账户余额和交易记录</p>
                    </div>
                </div>

                <?php if (isset($error)): ?>
                    <div class="alert alert-error"><?php echo $error; ?></div>
                <?php endif; ?>

                <!-- 钱包概览 -->
                <div class="wallet-overview">
                    <div class="balance-card">
                        <div class="balance-header">
                            <h3>账户余额</h3>
                            <i class="bi bi-wallet2"></i>
                        </div>
                        <div class="balance-amount">
                            <span class="currency">¥</span>
                            <span class="amount"><?php echo number_format($wallet['balance'], 2); ?></span>
                        </div>
                        <div class="balance-actions">
                            <button class="btn btn-primary" onclick="showDepositModal()">
                                <i class="bi bi-plus-circle"></i>
                                <span>充值</span>
                            </button>
                            <button class="btn btn-outline" onclick="showWithdrawModal()">
                                <i class="bi bi-arrow-up-circle"></i>
                                <span>提现</span>
                            </button>
                        </div>
                    </div>

                    <div class="frozen-card">
                        <div class="frozen-header">
                            <h3>冻结金额</h3>
                            <i class="bi bi-lock"></i>
                        </div>
                        <div class="frozen-amount">
                            <span class="currency">¥</span>
                            <span class="amount"><?php echo number_format($wallet['frozen_amount'], 2); ?></span>
                        </div>
                        <div class="frozen-note">
                            <small>交易中的资金将被临时冻结</small>
                        </div>
                    </div>
                </div>

                <!-- 交易记录 -->
                <div class="transactions-section">
                    <div class="section-header">
                        <h3>交易记录</h3>
                        <div class="section-actions">
                            <select class="form-select" onchange="filterTransactions(this.value)">
                                <option value="">全部类型</option>
                                <option value="deposit">充值</option>
                                <option value="withdraw">提现</option>
                                <option value="payment">支付</option>
                                <option value="refund">退款</option>
                            </select>
                        </div>
                    </div>

                    <?php if (empty($transactions)): ?>
                        <div class="empty-state">
                            <div class="empty-icon">
                                <i class="bi bi-receipt"></i>
                            </div>
                            <h3 class="empty-title">暂无交易记录</h3>
                            <p class="empty-description">您还没有任何交易记录</p>
                        </div>
                    <?php else: ?>
                        <div class="transactions-list">
                            <?php foreach ($transactions as $transaction): ?>
                                <div class="transaction-item">
                                    <div class="transaction-icon">
                                        <?php
                                        $iconClass = '';
                                        $iconColor = '';
                                        switch ($transaction['type']) {
                                            case 'deposit':
                                                $iconClass = 'bi-plus-circle';
                                                $iconColor = 'success';
                                                break;
                                            case 'withdraw':
                                                $iconClass = 'bi-arrow-up-circle';
                                                $iconColor = 'warning';
                                                break;
                                            case 'payment':
                                                $iconClass = 'bi-credit-card';
                                                $iconColor = 'primary';
                                                break;
                                            case 'refund':
                                                $iconClass = 'bi-arrow-clockwise';
                                                $iconColor = 'info';
                                                break;
                                            default:
                                                $iconClass = 'bi-circle';
                                                $iconColor = 'secondary';
                                        }
                                        ?>
                                        <i class="bi <?php echo $iconClass; ?> text-<?php echo $iconColor; ?>"></i>
                                    </div>
                                    
                                    <div class="transaction-info">
                                        <div class="transaction-title">
                                            <?php
                                            $typeNames = [
                                                'deposit' => '账户充值',
                                                'withdraw' => '申请提现',
                                                'payment' => '支付订单',
                                                'refund' => '订单退款',
                                                'commission' => '佣金收入'
                                            ];
                                            echo $typeNames[$transaction['type']] ?? $transaction['type'];
                                            ?>
                                        </div>
                                        <div class="transaction-desc">
                                            <?php echo htmlspecialchars($transaction['description']); ?>
                                        </div>
                                        <div class="transaction-time">
                                            <?php echo date('Y-m-d H:i:s', strtotime($transaction['created_at'])); ?>
                                        </div>
                                    </div>
                                    
                                    <div class="transaction-amount">
                                        <span class="amount <?php echo in_array($transaction['type'], ['deposit', 'refund', 'commission']) ? 'positive' : 'negative'; ?>">
                                            <?php echo in_array($transaction['type'], ['deposit', 'refund', 'commission']) ? '+' : '-'; ?>¥<?php echo number_format($transaction['amount'], 2); ?>
                                        </span>
                                        <div class="balance-after">
                                            余额：¥<?php echo number_format($transaction['balance_after'], 2); ?>
                                        </div>
                                    </div>
                                    
                                    <div class="transaction-status">
                                        <?php
                                        $statusClass = '';
                                        $statusText = '';
                                        switch ($transaction['status']) {
                                            case 'completed':
                                                $statusClass = 'success';
                                                $statusText = '已完成';
                                                break;
                                            case 'pending':
                                                $statusClass = 'warning';
                                                $statusText = '处理中';
                                                break;
                                            case 'failed':
                                                $statusClass = 'danger';
                                                $statusText = '失败';
                                                break;
                                        }
                                        ?>
                                        <span class="status-badge status-<?php echo $statusClass; ?>">
                                            <?php echo $statusText; ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <!-- 分页 -->
                        <?php if ($totalPages > 1): ?>
                            <div class="pagination-container">
                                <nav class="pagination">
                                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                        <a href="?page=<?php echo $i; ?>" class="page-link <?php echo $i == $page ? 'active' : ''; ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    <?php endfor; ?>
                                </nav>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</main>

<!-- 充值模态框 -->
<div class="modal" id="depositModal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>账户充值</h3>
            <button class="modal-close" onclick="closeModal('depositModal')">&times;</button>
        </div>
        <form method="POST" class="modal-body">
            <input type="hidden" name="action" value="deposit">
            <div class="form-group">
                <label class="form-label">充值金额</label>
                <div class="amount-input">
                    <span class="currency">¥</span>
                    <input type="number" name="amount" class="form-input" placeholder="请输入充值金额" min="0.01" step="0.01" required>
                </div>
            </div>
            <div class="quick-amounts">
                <button type="button" class="quick-amount" onclick="setAmount(100)">100</button>
                <button type="button" class="quick-amount" onclick="setAmount(500)">500</button>
                <button type="button" class="quick-amount" onclick="setAmount(1000)">1000</button>
                <button type="button" class="quick-amount" onclick="setAmount(5000)">5000</button>
            </div>
            <div class="modal-actions">
                <button type="button" class="btn btn-outline" onclick="closeModal('depositModal')">取消</button>
                <button type="submit" class="btn btn-primary">确认充值</button>
            </div>
        </form>
    </div>
</div>

<!-- 提现模态框 -->
<div class="modal" id="withdrawModal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>申请提现</h3>
            <button class="modal-close" onclick="closeModal('withdrawModal')">&times;</button>
        </div>
        <form method="POST" class="modal-body">
            <input type="hidden" name="action" value="withdraw">
            <div class="form-group">
                <label class="form-label">提现金额</label>
                <div class="amount-input">
                    <span class="currency">¥</span>
                    <input type="number" name="amount" class="form-input" placeholder="请输入提现金额" min="0.01" step="0.01" max="<?php echo $wallet['balance']; ?>" required>
                </div>
                <div class="form-note">
                    可提现余额：¥<?php echo number_format($wallet['balance'], 2); ?>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">提现说明</label>
                <div class="withdraw-note">
                    <p>• 提现申请提交后，预计1-3个工作日到账</p>
                    <p>• 提现金额将从您的账户余额中扣除</p>
                    <p>• 如有疑问，请联系客服</p>
                </div>
            </div>
            <div class="modal-actions">
                <button type="button" class="btn btn-outline" onclick="closeModal('withdrawModal')">取消</button>
                <button type="submit" class="btn btn-primary">确认提现</button>
            </div>
        </form>
    </div>
</div>

<script>
// 钱包交互脚本
function showDepositModal() {
    document.getElementById('depositModal').style.display = 'flex';
}

function showWithdrawModal() {
    document.getElementById('withdrawModal').style.display = 'flex';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

function setAmount(amount) {
    const input = document.querySelector('#depositModal input[name="amount"]');
    input.value = amount;
}

function filterTransactions(type) {
    // 这里可以实现筛选功能
    if (type) {
        window.location.href = '?type=' + type;
    } else {
        window.location.href = 'wallet.php';
    }
}

// 点击模态框外部关闭
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('modal')) {
        e.target.style.display = 'none';
    }
});
</script>

<?php require_once 'includes/footer.php'; ?>
