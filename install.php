<?php
// 数据库安装脚本
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字鱼 - 系统安装</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 0.5rem;
        }
        .form-group {
            margin-bottom: 1rem;
        }
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
            box-sizing: border-box;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 4px;
            font-size: 1rem;
            cursor: pointer;
            width: 100%;
        }
        .btn:hover {
            background: #0056b3;
        }
        .alert {
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .step {
            margin-bottom: 2rem;
        }
        .step-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">数字鱼</div>
            <p>虚拟商品交易平台 - 系统安装</p>
        </div>

        <?php
        $step = $_GET['step'] ?? 1;
        $message = '';
        $error = '';

        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            if ($step == 1) {
                // 数据库配置
                $host = $_POST['host'] ?? 'localhost';
                $dbname = $_POST['dbname'] ?? 'xianyu_db';
                $username = $_POST['username'] ?? 'root';
                $password = $_POST['password'] ?? '';

                try {
                    // 测试数据库连接
                    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
                    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

                    // 创建数据库
                    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                    $pdo->exec("USE `$dbname`");

                    // 读取并执行SQL文件
                    $sql = file_get_contents('config/init_database.sql');
                    $sql = str_replace('xianyu_db', $dbname, $sql);
                    
                    // 分割SQL语句并执行
                    $statements = array_filter(array_map('trim', explode(';', $sql)));
                    foreach ($statements as $statement) {
                        if (!empty($statement)) {
                            $pdo->exec($statement);
                        }
                    }

                    // 更新配置文件
                    $configContent = "<?php
// 数据库配置
define('DB_HOST', '$host');
define('DB_NAME', '$dbname');
define('DB_USER', '$username');
define('DB_PASS', '$password');
define('DB_CHARSET', 'utf8mb4');

// 创建数据库连接
try {
    \$pdo = new PDO(
        \"mysql:host=\" . DB_HOST . \";dbname=\" . DB_NAME . \";charset=\" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]
    );
} catch (PDOException \$e) {
    die(\"数据库连接失败: \" . \$e->getMessage());
}

// 网站配置
define('SITE_NAME', '数字鱼');
define('SITE_URL', 'http://localhost/xianyu');
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

// 会话配置
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
?>";

                    file_put_contents('config/database.php', $configContent);

                    // 创建上传目录
                    if (!is_dir('uploads')) {
                        mkdir('uploads', 0755, true);
                    }

                    $message = '数据库安装成功！';
                    $step = 2;

                } catch (Exception $e) {
                    $error = '数据库安装失败：' . $e->getMessage();
                }
            }
        }
        ?>

        <?php if ($message): ?>
            <div class="alert alert-success"><?php echo $message; ?></div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-error"><?php echo $error; ?></div>
        <?php endif; ?>

        <?php if ($step == 1): ?>
            <div class="step">
                <div class="step-title">步骤 1: 数据库配置</div>
                <form method="POST">
                    <div class="form-group">
                        <label for="host">数据库主机</label>
                        <input type="text" id="host" name="host" value="localhost" required>
                    </div>
                    <div class="form-group">
                        <label for="dbname">数据库名称</label>
                        <input type="text" id="dbname" name="dbname" value="xianyu_db" required>
                    </div>
                    <div class="form-group">
                        <label for="username">数据库用户名</label>
                        <input type="text" id="username" name="username" value="root" required>
                    </div>
                    <div class="form-group">
                        <label for="password">数据库密码</label>
                        <input type="password" id="password" name="password">
                    </div>
                    <button type="submit" class="btn">安装数据库</button>
                </form>
            </div>
        <?php elseif ($step == 2): ?>
            <div class="step">
                <div class="step-title">安装完成</div>
                <div class="alert alert-success">
                    <h4>恭喜！数字鱼系统安装成功！</h4>
                    <p>您现在可以开始使用系统了。</p>
                    <ul>
                        <li>访问首页：<a href="index.php">index.php</a></li>
                        <li>注册账号：<a href="register.php">register.php</a></li>
                        <li>登录系统：<a href="login.php">login.php</a></li>
                    </ul>
                </div>
                <p><strong>重要提示：</strong>为了安全起见，请删除此安装文件 (install.php)。</p>
                <a href="index.php" class="btn">进入首页</a>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
