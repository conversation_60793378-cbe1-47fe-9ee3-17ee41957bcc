<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册 - 闲鱼</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/bootstrap-icons.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <a href="index.html">
                    <img src="images/logo.svg" alt="闲鱼logo">
                </a>
            </div>
            <nav class="nav">
                <ul>
                    <li><a href="index.html">首页</a></li>
                    <li><a href="login.html">登录</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- 主要内容区 -->
    <main class="main register-page">
        <div class="container">
            <div class="auth-container">
                <div class="auth-header">
                    <h2>注册闲鱼账号</h2>
                    <p>已有账号？<a href="login.html">立即登录</a></p>
                </div>
                
                <form class="auth-form" id="registerForm">
                    <div class="form-group">
                        <label for="phone">手机号码</label>
                        <div class="input-group">
                            <span class="input-prefix">+86</span>
                            <input type="tel" id="phone" name="phone" placeholder="请输入手机号码" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="verifyCode">验证码</label>
                        <div class="input-group">
                            <input type="text" id="verifyCode" name="verifyCode" placeholder="请输入验证码" required>
                            <button type="button" class="verify-code-btn">获取验证码</button>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">设置密码</label>
                        <div class="input-group">
                            <input type="password" id="password" name="password" placeholder="请设置登录密码" required>
                            <span class="toggle-password"><i class="bi bi-eye"></i></span>
                        </div>
                        <div class="password-strength">
                            <div class="strength-bar">
                                <div class="strength-level" data-level="0"></div>
                            </div>
                            <div class="strength-text">密码强度：弱</div>
                        </div>
                        <p class="form-tip">密码长度8-20位，必须包含字母、数字</p>
                    </div>
                    
                    <div class="form-group">
                        <label for="confirmPassword">确认密码</label>
                        <div class="input-group">
                            <input type="password" id="confirmPassword" name="confirmPassword" placeholder="请再次输入密码" required>
                            <span class="toggle-password"><i class="bi bi-eye"></i></span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="nickname">昵称</label>
                        <input type="text" id="nickname" name="nickname" placeholder="请设置昵称" required>
                    </div>
                    
                    <div class="form-group checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="agreement" required>
                            <span>我已阅读并同意</span>
                        </label>
                        <a href="#" class="agreement-link">《闲鱼用户协议》</a>
                        <a href="#" class="agreement-link">《隐私政策》</a>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="auth-submit-btn">注册</button>
                    </div>
                    
                    <div class="other-login">
                        <p class="divider"><span>其他方式登录</span></p>
                        <div class="social-login">
                            <a href="#" class="social-icon"><i class="bi bi-wechat"></i></a>
                            <a href="#" class="social-icon"><i class="bi bi-qq"></i></a>
                            <a href="#" class="social-icon"><i class="bi bi-apple"></i></a>
                            <a href="#" class="social-icon"><i class="bi bi-envelope"></i></a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </main>

    <!-- 底部信息 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-links">
                <div class="link-group">
                    <h4>关于我们</h4>
                    <ul>
                        <li><a href="#">平台介绍</a></li>
                        <li><a href="#">联系我们</a></li>
                        <li><a href="#">加入我们</a></li>
                    </ul>
                </div>
                <div class="link-group">
                    <h4>帮助中心</h4>
                    <ul>
                        <li><a href="#">新手指南</a></li>
                        <li><a href="#">常见问题</a></li>
                        <li><a href="#">意见反馈</a></li>
                    </ul>
                </div>
                <div class="link-group">
                    <h4>交易保障</h4>
                    <ul>
                        <li><a href="#">闲鱼规则</a></li>
                        <li><a href="#">安全保障</a></li>
                        <li><a href="#">隐私政策</a></li>
                    </ul>
                </div>
                <div class="link-group">
                    <h4>特色服务</h4>
                    <ul>
                        <li><a href="#">闲鱼寄卖</a></li>
                        <li><a href="#">闲鱼严选</a></li>
                        <li><a href="#">闲鱼优品</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="copyright">
                    <p>© 2023 闲鱼 版权所有</p>
                </div>
                <div class="footer-info">
                    <p>浙ICP备XXXXXXXX号-X | 浙公网安备XXXXXXXXXXXX号</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
</body>
</html>