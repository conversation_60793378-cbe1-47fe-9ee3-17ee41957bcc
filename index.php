<?php
$pageTitle = '首页';
require_once 'includes/header.php';

// 获取热门商品
$hotProducts = getProducts(8, 0);
$categories = getCategories();
?>

<!-- 主要内容区 -->
<main class="main">
    <div class="container">
        <!-- 分类导航 -->
        <div class="category-nav">
            <ul>
                <?php foreach ($categories as $category): ?>
                    <li><a href="list.php?category=<?php echo $category['slug']; ?>"><?php echo htmlspecialchars($category['name']); ?></a></li>
                <?php endforeach; ?>
            </ul>
        </div>

        <div class="banner">
            <img src="images/banner.jpg" alt="数字鱼banner">
            <div class="banner-content">
                <h2>数字商品 即买即用</h2>
                <p>海量虚拟商品，安全便捷的交易体验</p>
                <a href="list.php" class="banner-btn">立即探索</a>
            </div>
        </div>

        <div class="feature-services">
            <div class="feature-item">
                <i class="bi bi-shield-check"></i>
                <span>官方认证</span>
            </div>
            <div class="feature-item">
                <i class="bi bi-arrow-repeat"></i>
                <span>售后保障</span>
            </div>
            <div class="feature-item">
                <i class="bi bi-lightning-charge"></i>
                <span>即时交付</span>
            </div>
            <div class="feature-item">
                <i class="bi bi-cash-stack"></i>
                <span>担保交易</span>
            </div>
        </div>

        <section class="recommend-section">
            <div class="section-header">
                <h2>热门虚拟商品</h2>
                <a href="list.php" class="more-link">查看更多 <i class="bi bi-chevron-right"></i></a>
            </div>
            <div class="product-grid">
                <?php foreach ($hotProducts as $product): ?>
                    <div class="product-card virtual-product">
                        <a href="detail.php?id=<?php echo $product['id']; ?>">
                            <div class="product-img">
                                <?php 
                                $images = json_decode($product['images'], true);
                                $firstImage = $images ? $images[0] : 'images/product-default.svg';
                                ?>
                                <img src="<?php echo $firstImage; ?>" alt="<?php echo htmlspecialchars($product['title']); ?>">
                                <?php if ($product['is_virtual']): ?>
                                    <span class="delivery-tag"><i class="bi bi-lightning-charge"></i> 即时交付</span>
                                <?php endif; ?>
                            </div>
                            <div class="product-info">
                                <h3 class="product-title"><?php echo htmlspecialchars($product['title']); ?></h3>
                                <div class="product-price">
                                    <span class="current-price"><?php echo formatPrice($product['price']); ?></span>
                                    <?php if ($product['original_price']): ?>
                                        <span class="original-price"><?php echo formatPrice($product['original_price']); ?></span>
                                    <?php endif; ?>
                                </div>
                                <?php if ($product['is_virtual']): ?>
                                    <div class="virtual-attrs">
                                        <span class="attr-item"><i class="bi bi-shield-check"></i> 正版保障</span>
                                        <span class="attr-item"><i class="bi bi-lightning-charge"></i> 即时交付</span>
                                    </div>
                                <?php endif; ?>
                                <div class="product-meta">
                                    <div class="seller">
                                        <img src="<?php echo $product['seller_avatar']; ?>" alt="卖家头像" class="seller-avatar">
                                        <span class="seller-name"><?php echo htmlspecialchars($product['seller_name']); ?></span>
                                    </div>
                                    <div class="product-actions">
                                        <span class="like-count"><i class="bi bi-heart"></i> <?php echo $product['likes']; ?></span>
                                        <span class="view-count"><i class="bi bi-eye"></i> <?php echo $product['views']; ?></span>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                <?php endforeach; ?>
            </div>
        </section>

        <!-- 热门鱼塘 -->
        <div class="section-title">
            <h2>热门鱼塘</h2>
            <a href="#" class="more-link">查看更多 <i class="bi bi-chevron-right"></i></a>
        </div>
        <div class="fishpond-grid">
            <div class="fishpond-card">
                <div class="fishpond-header">
                    <img src="images/fishpond1.svg" alt="数码鱼塘" class="fishpond-img">
                    <h3>数码发烧友</h3>
                    <p>28.5万人 · 3.2万件宝贝</p>
                </div>
                <div class="fishpond-products">
                    <img src="images/fp-product1.svg" alt="商品缩略图">
                    <img src="images/fp-product2.svg" alt="商品缩略图">
                    <img src="images/fp-product3.svg" alt="商品缩略图">
                </div>
                <button class="join-btn">加入鱼塘</button>
            </div>

            <div class="fishpond-card">
                <div class="fishpond-header">
                    <img src="images/fishpond2.svg" alt="潮鞋鱼塘" class="fishpond-img">
                    <h3>潮鞋收藏家</h3>
                    <p>15.2万人 · 2.8万件宝贝</p>
                </div>
                <div class="fishpond-products">
                    <img src="images/fp-product4.svg" alt="商品缩略图">
                    <img src="images/fp-product5.svg" alt="商品缩略图">
                    <img src="images/fp-product6.svg" alt="商品缩略图">
                </div>
                <button class="join-btn">加入鱼塘</button>
            </div>

            <div class="fishpond-card">
                <div class="fishpond-header">
                    <img src="images/fishpond3.svg" alt="奢侈品鱼塘" class="fishpond-img">
                    <h3>奢侈品交流</h3>
                    <p>12.8万人 · 1.5万件宝贝</p>
                </div>
                <div class="fishpond-products">
                    <img src="images/fp-product7.svg" alt="商品缩略图">
                    <img src="images/fp-product8.svg" alt="商品缩略图">
                    <img src="images/fp-product9.svg" alt="商品缩略图">
                </div>
                <button class="join-btn">加入鱼塘</button>
            </div>
        </div>
    </div>
</main>

<?php require_once 'includes/footer.php'; ?>
