<?php
$pageTitle = '首页';
$additionalCSS = ['css/homepage-optimized.css'];
$additionalJS = ['js/homepage-enhanced.js'];
require_once 'includes/header.php';

// 获取热门商品
$hotProducts = getProducts(12, 0);
$categories = getCategories();
$featuredCategories = array_slice($categories, 0, 6); // 获取前6个分类作为推荐分类
?>

<!-- 主要内容区 -->
<main class="main homepage">
    <div class="container">
        <!-- 英雄横幅区域 -->
        <section class="hero-banner">
            <div class="banner-content">
                <div class="banner-text">
                    <h1 class="hero-title">数字鱼 - 虚拟商品交易平台</h1>
                    <p class="hero-subtitle">海量正版虚拟商品，即买即用，安全便捷的交易体验</p>
                    <div class="hero-features">
                        <span class="feature-tag"><i class="bi bi-shield-check"></i> 正版保障</span>
                        <span class="feature-tag"><i class="bi bi-lightning-charge"></i> 即时交付</span>
                        <span class="feature-tag"><i class="bi bi-award"></i> 官方认证</span>
                    </div>
                    <div class="hero-actions">
                        <a href="list.php" class="btn btn-primary btn-large">立即探索</a>
                        <a href="publish.php" class="btn btn-outline btn-large">发布商品</a>
                    </div>
                </div>
                <div class="banner-image">
                    <img src="images/hero-illustration.svg" alt="数字商品交易" class="hero-img">
                </div>
            </div>
        </section>

        <!-- 热门分类 -->
        <section class="trending-categories">
            <div class="section-header">
                <h2>热门分类</h2>
                <p>发现最受欢迎的虚拟商品类别</p>
            </div>
            <div class="category-grid">
                <?php foreach ($featuredCategories as $category): ?>
                    <a href="list.php?category=<?php echo $category['slug']; ?>" class="category-card">
                        <div class="category-icon">
                            <i class="bi bi-<?php echo getCategoryIcon($category['slug']); ?>"></i>
                        </div>
                        <h3><?php echo htmlspecialchars($category['name']); ?></h3>
                        <p><?php echo getCategoryDescription($category['slug']); ?></p>
                        <span class="category-count"><?php echo getCategoryProductCount($category['id']); ?>+ 商品</span>
                    </a>
                <?php endforeach; ?>
            </div>
        </section>

        <!-- 平台优势 -->
        <section class="platform-advantages">
            <div class="advantages-grid">
                <div class="advantage-item">
                    <div class="advantage-icon">
                        <i class="bi bi-shield-check"></i>
                    </div>
                    <h3>官方认证</h3>
                    <p>严格审核，确保商品质量</p>
                </div>
                <div class="advantage-item">
                    <div class="advantage-icon">
                        <i class="bi bi-lightning-charge"></i>
                    </div>
                    <h3>即时交付</h3>
                    <p>购买后立即获得商品</p>
                </div>
                <div class="advantage-item">
                    <div class="advantage-icon">
                        <i class="bi bi-arrow-repeat"></i>
                    </div>
                    <h3>售后保障</h3>
                    <p>7天无理由退换货</p>
                </div>
                <div class="advantage-item">
                    <div class="advantage-icon">
                        <i class="bi bi-cash-stack"></i>
                    </div>
                    <h3>担保交易</h3>
                    <p>资金安全，交易无忧</p>
                </div>
            </div>
        </section>

        <!-- 热门商品推荐 -->
        <section class="featured-products">
            <div class="section-header">
                <h2>热门推荐</h2>
                <p>精选优质虚拟商品，为您推荐最受欢迎的商品</p>
                <a href="list.php" class="more-link">查看全部 <i class="bi bi-arrow-right"></i></a>
            </div>
            <div class="product-grid homepage-grid">
                <?php foreach ($hotProducts as $product): ?>
                    <div class="product-card enhanced-card">
                        <a href="detail.php?id=<?php echo $product['id']; ?>" class="product-link">
                            <div class="product-img">
                                <?php
                                $images = json_decode($product['images'], true);
                                $firstImage = $images ? $images[0] : 'images/product-default.svg';
                                ?>
                                <img src="<?php echo $firstImage; ?>" alt="<?php echo htmlspecialchars($product['title']); ?>" loading="lazy">
                                <?php if ($product['is_virtual']): ?>
                                    <div class="product-badges">
                                        <span class="badge instant-delivery">
                                            <i class="bi bi-lightning-charge"></i>
                                            即时交付
                                        </span>
                                    </div>
                                <?php endif; ?>
                                <div class="product-overlay">
                                    <span class="quick-view">快速查看</span>
                                </div>
                            </div>
                            <div class="product-info">
                                <h3 class="product-title"><?php echo htmlspecialchars($product['title']); ?></h3>
                                <div class="product-price">
                                    <span class="current-price"><?php echo formatPrice($product['price']); ?></span>
                                    <?php if ($product['original_price'] && $product['original_price'] > $product['price']): ?>
                                        <span class="original-price"><?php echo formatPrice($product['original_price']); ?></span>
                                        <span class="discount-badge">
                                            <?php echo round((1 - $product['price'] / $product['original_price']) * 100); ?>% OFF
                                        </span>
                                    <?php endif; ?>
                                </div>
                                <div class="product-meta">
                                    <div class="seller-info">
                                        <img src="<?php echo $product['seller_avatar'] ?: 'images/avatar-default.svg'; ?>" alt="卖家头像" class="seller-avatar">
                                        <span class="seller-name"><?php echo htmlspecialchars($product['seller_name']); ?></span>
                                    </div>
                                    <div class="product-stats">
                                        <span class="stat-item">
                                            <i class="bi bi-eye"></i>
                                            <?php echo formatNumber($product['views']); ?>
                                        </span>
                                        <span class="stat-item">
                                            <i class="bi bi-heart"></i>
                                            <?php echo formatNumber($product['likes']); ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                <?php endforeach; ?>
            </div>
        </section>

        <!-- 数据统计 -->
        <section class="platform-stats">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">50,000+</div>
                    <div class="stat-label">注册用户</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">10,000+</div>
                    <div class="stat-label">在售商品</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100,000+</div>
                    <div class="stat-label">成功交易</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">99.9%</div>
                    <div class="stat-label">好评率</div>
                </div>
            </div>
        </section>

        <!-- 热门社区 -->
        <section class="community-section">
            <div class="section-header">
                <h2>热门社区</h2>
                <p>加入活跃的交易社区，发现更多优质商品</p>
                <a href="#" class="more-link">查看全部 <i class="bi bi-arrow-right"></i></a>
            </div>
            <div class="community-grid">
                <div class="community-card">
                    <div class="community-header">
                        <div class="community-avatar">
                            <i class="bi bi-cpu"></i>
                        </div>
                        <div class="community-info">
                            <h3>数码发烧友</h3>
                            <p>专业数码产品交流社区</p>
                        </div>
                    </div>
                    <div class="community-stats">
                        <span class="member-count">28.5万成员</span>
                        <span class="product-count">3.2万商品</span>
                    </div>
                    <div class="community-preview">
                        <img src="images/community-preview-1.jpg" alt="社区预览">
                        <img src="images/community-preview-2.jpg" alt="社区预览">
                        <img src="images/community-preview-3.jpg" alt="社区预览">
                        <span class="more-indicator">+99</span>
                    </div>
                    <button class="join-btn">加入社区</button>
                </div>

                <div class="community-card">
                    <div class="community-header">
                        <div class="community-avatar">
                            <i class="bi bi-controller"></i>
                        </div>
                        <div class="community-info">
                            <h3>游戏玩家联盟</h3>
                            <p>游戏账号、道具交易专区</p>
                        </div>
                    </div>
                    <div class="community-stats">
                        <span class="member-count">35.8万成员</span>
                        <span class="product-count">5.6万商品</span>
                    </div>
                    <div class="community-preview">
                        <img src="images/community-preview-4.jpg" alt="社区预览">
                        <img src="images/community-preview-5.jpg" alt="社区预览">
                        <img src="images/community-preview-6.jpg" alt="社区预览">
                        <span class="more-indicator">+156</span>
                    </div>
                    <button class="join-btn">加入社区</button>
                </div>

                <div class="community-card">
                    <div class="community-header">
                        <div class="community-avatar">
                            <i class="bi bi-palette"></i>
                        </div>
                        <div class="community-info">
                            <h3>设计师资源库</h3>
                            <p>设计素材、字体、模板交易</p>
                        </div>
                    </div>
                    <div class="community-stats">
                        <span class="member-count">18.2万成员</span>
                        <span class="product-count">2.1万商品</span>
                    </div>
                    <div class="community-preview">
                        <img src="images/community-preview-7.jpg" alt="社区预览">
                        <img src="images/community-preview-8.jpg" alt="社区预览">
                        <img src="images/community-preview-9.jpg" alt="社区预览">
                        <span class="more-indicator">+78</span>
                    </div>
                    <button class="join-btn">加入社区</button>
                </div>
            </div>
        </section>
    </div>
</main>

<?php require_once 'includes/footer.php'; ?>
