-- 更新订单表，添加订单号字段
USE xianyu_db;

-- 添加订单号字段
ALTER TABLE orders ADD COLUMN order_number VARCHAR(50) UNIQUE AFTER id;

-- 为现有订单生成订单号
UPDATE orders SET order_number = CONCAT('XY', DATE_FORMAT(created_at, '%Y%m%d%H%i%s'), LPAD(id, 4, '0')) WHERE order_number IS NULL;

-- 添加索引
CREATE INDEX idx_order_number ON orders(order_number);
CREATE INDEX idx_buyer_id ON orders(buyer_id);
CREATE INDEX idx_seller_id ON orders(seller_id);
CREATE INDEX idx_status ON orders(status);
