<?php
$pageTitle = '商品列表';
$additionalCSS = ['css/list-optimized.css'];
$additionalJS = ['js/list-enhanced.js'];
require_once 'includes/header.php';

// 获取筛选参数
$category = $_GET['category'] ?? '';
$search = $_GET['q'] ?? '';
$sort = $_GET['sort'] ?? 'default';
$priceMin = $_GET['price_min'] ?? '';
$priceMax = $_GET['price_max'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 24; // 增加每页显示数量
$offset = ($page - 1) * $limit;

// 获取商品列表
$products = getProducts($limit, $offset, $category, $search, $sort, $priceMin, $priceMax);
$categories = getCategories();

// 获取当前分类信息
$currentCategory = null;
if ($category) {
    foreach ($categories as $cat) {
        if ($cat['slug'] === $category) {
            $currentCategory = $cat;
            break;
        }
    }
}

// 计算总页数（简化版本）
$totalProducts = getTotalProductCount($category, $search, $priceMin, $priceMax);
$totalPages = ceil($totalProducts / $limit);

// 构建查询参数
$queryParams = [];
if ($category) $queryParams['category'] = $category;
if ($search) $queryParams['q'] = $search;
if ($sort && $sort !== 'default') $queryParams['sort'] = $sort;
if ($priceMin) $queryParams['price_min'] = $priceMin;
if ($priceMax) $queryParams['price_max'] = $priceMax;
$queryString = http_build_query($queryParams);
?>

<!-- 主要内容区 -->
<main class="main list-page">
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <!-- 面包屑导航 -->
            <nav class="breadcrumb">
                <a href="index.php" class="breadcrumb-item">
                    <i class="bi bi-house"></i>
                    首页
                </a>
                <span class="breadcrumb-separator">/</span>
                <?php if ($currentCategory): ?>
                    <span class="breadcrumb-item current"><?php echo htmlspecialchars($currentCategory['name']); ?></span>
                <?php elseif ($search): ?>
                    <span class="breadcrumb-item current">搜索: "<?php echo htmlspecialchars($search); ?>"</span>
                <?php else: ?>
                    <span class="breadcrumb-item current">全部商品</span>
                <?php endif; ?>
            </nav>

            <!-- 页面标题和统计 -->
            <div class="page-title-section">
                <h1 class="page-title">
                    <?php if ($currentCategory): ?>
                        <?php echo htmlspecialchars($currentCategory['name']); ?>
                    <?php elseif ($search): ?>
                        搜索结果
                    <?php else: ?>
                        全部商品
                    <?php endif; ?>
                </h1>
                <p class="page-subtitle">
                    <?php if ($search): ?>
                        为您找到 <strong><?php echo $totalProducts; ?></strong> 件相关商品
                    <?php else: ?>
                        共 <strong><?php echo $totalProducts; ?></strong> 件商品
                    <?php endif; ?>
                </p>
            </div>
        </div>

        <!-- 筛选和排序区域 -->
        <div class="filter-sort-section">
            <!-- 快速筛选 -->
            <div class="quick-filters">
                <div class="filter-group">
                    <span class="filter-label">分类</span>
                    <div class="filter-tags">
                        <a href="list.php<?php echo $search ? '?q=' . urlencode($search) : ''; ?>"
                           class="filter-tag <?php echo empty($category) ? 'active' : ''; ?>">
                            全部
                        </a>
                        <?php foreach ($categories as $cat): ?>
                            <a href="?category=<?php echo $cat['slug']; ?><?php echo $search ? '&q=' . urlencode($search) : ''; ?>"
                               class="filter-tag <?php echo $category === $cat['slug'] ? 'active' : ''; ?>">
                                <?php echo htmlspecialchars($cat['name']); ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- 高级筛选 -->
            <div class="advanced-filters" id="advancedFilters">
                <form method="GET" action="list.php" class="filter-form">
                    <?php if ($category): ?>
                        <input type="hidden" name="category" value="<?php echo htmlspecialchars($category); ?>">
                    <?php endif; ?>
                    <?php if ($search): ?>
                        <input type="hidden" name="q" value="<?php echo htmlspecialchars($search); ?>">
                    <?php endif; ?>

                    <div class="filter-row">
                        <div class="filter-group">
                            <label class="filter-label">价格范围</label>
                            <div class="price-range">
                                <input type="number" name="price_min" placeholder="最低价"
                                       value="<?php echo htmlspecialchars($priceMin); ?>" class="price-input">
                                <span class="price-separator">-</span>
                                <input type="number" name="price_max" placeholder="最高价"
                                       value="<?php echo htmlspecialchars($priceMax); ?>" class="price-input">
                            </div>
                        </div>

                        <div class="filter-group">
                            <label class="filter-label">商品特性</label>
                            <div class="checkbox-group">
                                <label class="checkbox-item">
                                    <input type="checkbox" name="features[]" value="instant_delivery">
                                    <span class="checkmark"></span>
                                    即时交付
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" name="features[]" value="official_cert">
                                    <span class="checkmark"></span>
                                    官方认证
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" name="features[]" value="warranty">
                                    <span class="checkmark"></span>
                                    售后保障
                                </label>
                            </div>
                        </div>

                        <div class="filter-actions">
                            <button type="submit" class="btn btn-primary">应用筛选</button>
                            <a href="list.php<?php echo $category ? '?category=' . $category : ''; ?><?php echo $search ? ($category ? '&' : '?') . 'q=' . urlencode($search) : ''; ?>"
                               class="btn btn-outline">重置</a>
                        </div>
                    </div>
                </form>
            </div>

            <!-- 排序和视图切换 -->
            <div class="sort-view-section">
                <div class="sort-options">
                    <span class="sort-label">排序:</span>
                    <a href="?<?php echo http_build_query(array_merge($queryParams, ['sort' => 'default'])); ?>"
                       class="sort-item <?php echo $sort === 'default' ? 'active' : ''; ?>">
                        综合排序
                    </a>
                    <a href="?<?php echo http_build_query(array_merge($queryParams, ['sort' => 'newest'])); ?>"
                       class="sort-item <?php echo $sort === 'newest' ? 'active' : ''; ?>">
                        最新发布
                    </a>
                    <a href="?<?php echo http_build_query(array_merge($queryParams, ['sort' => 'price_asc'])); ?>"
                       class="sort-item <?php echo $sort === 'price_asc' ? 'active' : ''; ?>">
                        价格升序
                    </a>
                    <a href="?<?php echo http_build_query(array_merge($queryParams, ['sort' => 'price_desc'])); ?>"
                       class="sort-item <?php echo $sort === 'price_desc' ? 'active' : ''; ?>">
                        价格降序
                    </a>
                    <a href="?<?php echo http_build_query(array_merge($queryParams, ['sort' => 'popular'])); ?>"
                       class="sort-item <?php echo $sort === 'popular' ? 'active' : ''; ?>">
                        最受欢迎
                    </a>
                </div>

                <div class="view-controls">
                    <button class="filter-toggle" onclick="toggleAdvancedFilters()">
                        <i class="bi bi-funnel"></i>
                        高级筛选
                    </button>
                    <div class="view-switch">
                        <button class="view-btn grid-view active" data-view="grid">
                            <i class="bi bi-grid-3x3-gap"></i>
                        </button>
                        <button class="view-btn list-view" data-view="list">
                            <i class="bi bi-list"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 商品列表 -->
        <div class="products-container">
            <div class="product-grid" id="productGrid">
                <?php foreach ($products as $product): ?>
                    <div class="product-card enhanced-product-card">
                        <a href="detail.php?id=<?php echo $product['id']; ?>" class="product-link">
                            <div class="product-image">
                                <?php
                                $images = json_decode($product['images'], true);
                                $firstImage = $images ? $images[0] : 'images/product-default.svg';
                                ?>
                                <img src="<?php echo $firstImage; ?>" alt="<?php echo htmlspecialchars($product['title']); ?>" loading="lazy">

                                <!-- 商品标签 -->
                                <div class="product-badges">
                                    <?php if ($product['is_virtual']): ?>
                                        <span class="badge virtual-badge">
                                            <i class="bi bi-lightning-charge"></i>
                                            虚拟商品
                                        </span>
                                    <?php endif; ?>
                                    <?php if (isset($product['is_featured']) && $product['is_featured']): ?>
                                        <span class="badge featured-badge">
                                            <i class="bi bi-star"></i>
                                            精选
                                        </span>
                                    <?php endif; ?>
                                </div>

                                <!-- 悬停操作 -->
                                <div class="product-overlay">
                                    <div class="overlay-actions">
                                        <button class="action-btn like-btn" data-product-id="<?php echo $product['id']; ?>">
                                            <i class="bi bi-heart"></i>
                                        </button>
                                        <button class="action-btn quick-view-btn" data-product-id="<?php echo $product['id']; ?>">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button class="action-btn share-btn" data-product-id="<?php echo $product['id']; ?>">
                                            <i class="bi bi-share"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="product-content">
                                <h3 class="product-title"><?php echo htmlspecialchars($product['title']); ?></h3>

                                <div class="product-price">
                                    <span class="current-price"><?php echo formatPrice($product['price']); ?></span>
                                    <?php if ($product['original_price'] && $product['original_price'] > $product['price']): ?>
                                        <span class="original-price"><?php echo formatPrice($product['original_price']); ?></span>
                                        <span class="discount-tag">
                                            <?php echo round((1 - $product['price'] / $product['original_price']) * 100); ?>% OFF
                                        </span>
                                    <?php endif; ?>
                                </div>

                                <!-- 商品特性 -->
                                <?php if ($product['is_virtual']): ?>
                                    <div class="product-features">
                                        <span class="feature-tag">
                                            <i class="bi bi-shield-check"></i>
                                            正版保障
                                        </span>
                                        <span class="feature-tag">
                                            <i class="bi bi-lightning-charge"></i>
                                            即时交付
                                        </span>
                                    </div>
                                <?php endif; ?>

                                <div class="product-meta">
                                    <div class="seller-info">
                                        <img src="<?php echo $product['seller_avatar'] ?: 'images/avatar-default.svg'; ?>"
                                             alt="卖家头像" class="seller-avatar">
                                        <span class="seller-name"><?php echo htmlspecialchars($product['seller_name']); ?></span>
                                        <?php if (isset($product['seller_rating']) && $product['seller_rating']): ?>
                                            <div class="seller-rating">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <i class="bi bi-star<?php echo $i <= $product['seller_rating'] ? '-fill' : ''; ?>"></i>
                                                <?php endfor; ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <div class="product-stats">
                                        <span class="stat-item">
                                            <i class="bi bi-eye"></i>
                                            <?php echo formatNumber($product['views']); ?>
                                        </span>
                                        <span class="stat-item">
                                            <i class="bi bi-heart"></i>
                                            <?php echo formatNumber($product['likes']); ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- 空状态 -->
            <?php if (empty($products)): ?>
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="bi bi-search"></i>
                    </div>
                    <h3 class="empty-title">未找到相关商品</h3>
                    <p class="empty-description">
                        <?php if ($search): ?>
                            没有找到与 "<?php echo htmlspecialchars($search); ?>" 相关的商品
                        <?php elseif ($currentCategory): ?>
                            该分类下暂时没有商品
                        <?php else: ?>
                            暂时没有商品
                        <?php endif; ?>
                    </p>
                    <div class="empty-actions">
                        <?php if ($search || $category): ?>
                            <a href="list.php" class="btn btn-primary">查看全部商品</a>
                        <?php endif; ?>
                        <a href="index.php" class="btn btn-outline">返回首页</a>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- 分页导航 -->
        <?php if (!empty($products) && $totalPages > 1): ?>
            <div class="pagination-wrapper">
                <nav class="pagination" aria-label="商品列表分页">
                    <!-- 上一页 -->
                    <?php if ($page > 1): ?>
                        <a href="?<?php echo http_build_query(array_merge($queryParams, ['page' => $page - 1])); ?>"
                           class="pagination-btn prev-btn">
                            <i class="bi bi-chevron-left"></i>
                            上一页
                        </a>
                    <?php endif; ?>

                    <!-- 页码 -->
                    <div class="pagination-numbers">
                        <?php
                        $startPage = max(1, $page - 2);
                        $endPage = min($totalPages, $page + 2);

                        // 如果当前页靠近开始，显示更多后面的页码
                        if ($page <= 3) {
                            $endPage = min($totalPages, 5);
                        }

                        // 如果当前页靠近结束，显示更多前面的页码
                        if ($page > $totalPages - 3) {
                            $startPage = max(1, $totalPages - 4);
                        }
                        ?>

                        <?php if ($startPage > 1): ?>
                            <a href="?<?php echo http_build_query(array_merge($queryParams, ['page' => 1])); ?>"
                               class="pagination-number">1</a>
                            <?php if ($startPage > 2): ?>
                                <span class="pagination-ellipsis">...</span>
                            <?php endif; ?>
                        <?php endif; ?>

                        <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                            <a href="?<?php echo http_build_query(array_merge($queryParams, ['page' => $i])); ?>"
                               class="pagination-number <?php echo $i === $page ? 'active' : ''; ?>">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>

                        <?php if ($endPage < $totalPages): ?>
                            <?php if ($endPage < $totalPages - 1): ?>
                                <span class="pagination-ellipsis">...</span>
                            <?php endif; ?>
                            <a href="?<?php echo http_build_query(array_merge($queryParams, ['page' => $totalPages])); ?>"
                               class="pagination-number"><?php echo $totalPages; ?></a>
                        <?php endif; ?>
                    </div>

                    <!-- 下一页 -->
                    <?php if ($page < $totalPages): ?>
                        <a href="?<?php echo http_build_query(array_merge($queryParams, ['page' => $page + 1])); ?>"
                           class="pagination-btn next-btn">
                            下一页
                            <i class="bi bi-chevron-right"></i>
                        </a>
                    <?php endif; ?>
                </nav>

                <!-- 分页信息 -->
                <div class="pagination-info">
                    <span class="page-info">
                        第 <?php echo $page; ?> 页，共 <?php echo $totalPages; ?> 页
                    </span>
                    <div class="page-jump">
                        <span>跳转到</span>
                        <input type="number" id="jumpPage" min="1" max="<?php echo $totalPages; ?>"
                               value="<?php echo $page; ?>" class="jump-input">
                        <button onclick="jumpToPage()" class="jump-btn">确定</button>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</main>

<script>
// 高级筛选切换
function toggleAdvancedFilters() {
    const filters = document.getElementById('advancedFilters');
    const isVisible = filters.style.display !== 'none';
    filters.style.display = isVisible ? 'none' : 'block';
}

// 视图切换
document.addEventListener('DOMContentLoaded', function() {
    const viewBtns = document.querySelectorAll('.view-btn');
    const productGrid = document.getElementById('productGrid');

    viewBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            viewBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');

            const view = this.dataset.view;
            productGrid.className = view === 'list' ? 'product-list' : 'product-grid';
        });
    });
});

// 页面跳转
function jumpToPage() {
    const pageInput = document.getElementById('jumpPage');
    const page = parseInt(pageInput.value);
    const maxPage = parseInt(pageInput.max);

    if (page >= 1 && page <= maxPage) {
        const url = new URL(window.location);
        url.searchParams.set('page', page);
        window.location.href = url.toString();
    } else {
        alert('请输入有效的页码');
    }
}

// 商品操作
document.addEventListener('DOMContentLoaded', function() {
    // 点赞功能
    document.querySelectorAll('.like-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            // 这里添加点赞逻辑
            this.classList.toggle('liked');
        });
    });

    // 快速查看
    document.querySelectorAll('.quick-view-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            // 这里添加快速查看逻辑
            const productId = this.dataset.productId;
            console.log('Quick view product:', productId);
        });
    });

    // 分享功能
    document.querySelectorAll('.share-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            // 这里添加分享逻辑
            const productId = this.dataset.productId;
            console.log('Share product:', productId);
        });
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
