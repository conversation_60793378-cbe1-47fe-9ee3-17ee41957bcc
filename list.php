<?php
$pageTitle = '商品列表';
require_once 'includes/header.php';

// 获取筛选参数
$category = $_GET['category'] ?? '';
$search = $_GET['q'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// 获取商品列表
$products = getProducts($limit, $offset, $category, $search);
$categories = getCategories();

// 获取当前分类信息
$currentCategory = null;
if ($category) {
    foreach ($categories as $cat) {
        if ($cat['slug'] === $category) {
            $currentCategory = $cat;
            break;
        }
    }
}

// 计算总页数（简化版本）
$totalPages = 100; // 实际应该查询数据库获取总数
?>

<!-- 主要内容区 -->
<main class="main list-page">
    <div class="container">
        <!-- 面包屑导航 -->
        <div class="breadcrumb">
            <a href="index.php">首页</a> &gt; 
            <?php if ($currentCategory): ?>
                <span class="current-category"><?php echo htmlspecialchars($currentCategory['name']); ?></span>
            <?php elseif ($search): ?>
                <span class="current-category">搜索结果</span>
            <?php else: ?>
                <span class="current-category">全部商品</span>
            <?php endif; ?>
        </div>

        <!-- 筛选条件 -->
        <div class="filter-section">
            <div class="filter-group">
                <div class="filter-label">分类：</div>
                <div class="filter-options">
                    <a href="list.php" <?php echo empty($category) ? 'class="active"' : ''; ?>>全部</a>
                    <?php foreach ($categories as $cat): ?>
                        <a href="list.php?category=<?php echo $cat['slug']; ?>" 
                           <?php echo $category === $cat['slug'] ? 'class="active"' : ''; ?>>
                            <?php echo htmlspecialchars($cat['name']); ?>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <div class="filter-group">
                <div class="filter-label">价格：</div>
                <div class="filter-options">
                    <a href="#" class="active">全部</a>
                    <a href="#">0-50元</a>
                    <a href="#">50-100元</a>
                    <a href="#">100-500元</a>
                    <a href="#">500-1000元</a>
                    <a href="#">1000元以上</a>
                    <div class="price-input">
                        <input type="text" placeholder="¥">
                        <span>-</span>
                        <input type="text" placeholder="¥">
                        <button>确定</button>
                    </div>
                </div>
            </div>
            
            <div class="filter-group">
                <div class="filter-label">服务：</div>
                <div class="filter-options">
                    <label class="checkbox-label">
                        <input type="checkbox"> 官方认证
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox"> 即时交付
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox"> 售后保障
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox"> 担保交易
                    </label>
                </div>
            </div>
        </div>

        <!-- 排序选项 -->
        <div class="sort-section">
            <div class="sort-options">
                <a href="#" class="active">综合</a>
                <a href="#">最新发布</a>
                <a href="#">价格 <i class="bi bi-arrow-down-up"></i></a>
                <a href="#">销量 <i class="bi bi-arrow-down"></i></a>
            </div>
            <div class="view-switch">
                <span class="view-count">共<?php echo count($products); ?>件商品</span>
                <a href="#" class="grid-view active"><i class="bi bi-grid"></i></a>
                <a href="#" class="list-view"><i class="bi bi-list"></i></a>
            </div>
        </div>

        <!-- 商品列表 -->
        <div class="product-grid list-grid">
            <?php foreach ($products as $product): ?>
                <div class="product-card">
                    <a href="detail.php?id=<?php echo $product['id']; ?>">
                        <div class="product-img">
                            <?php 
                            $images = json_decode($product['images'], true);
                            $firstImage = $images ? $images[0] : 'images/product-default.svg';
                            ?>
                            <img src="<?php echo $firstImage; ?>" alt="<?php echo htmlspecialchars($product['title']); ?>">
                            <?php if ($product['is_virtual']): ?>
                                <span class="product-tag">虚拟商品</span>
                            <?php endif; ?>
                        </div>
                        <div class="product-info">
                            <h3 class="product-title"><?php echo htmlspecialchars($product['title']); ?></h3>
                            <div class="product-meta">
                                <span class="price"><?php echo formatPrice($product['price']); ?></span>
                                <?php if ($product['original_price']): ?>
                                    <span class="original-price"><?php echo formatPrice($product['original_price']); ?></span>
                                <?php endif; ?>
                            </div>
                            <div class="seller-info">
                                <img src="<?php echo $product['seller_avatar']; ?>" alt="卖家头像" class="seller-avatar">
                                <span class="seller-name"><?php echo htmlspecialchars($product['seller_name']); ?></span>
                            </div>
                        </div>
                    </a>
                    <div class="product-actions">
                        <span class="like-btn"><i class="bi bi-heart"></i> <?php echo $product['likes']; ?></span>
                        <span class="view-btn"><i class="bi bi-eye"></i> <?php echo $product['views']; ?></span>
                    </div>
                </div>
            <?php endforeach; ?>
            
            <?php if (empty($products)): ?>
                <div class="empty-state">
                    <i class="bi bi-inbox"></i>
                    <h3>暂无商品</h3>
                    <p>该分类下暂时没有商品，去看看其他分类吧</p>
                    <a href="index.php" class="btn btn-primary">返回首页</a>
                </div>
            <?php endif; ?>
        </div>

        <!-- 分页 -->
        <?php if (!empty($products)): ?>
            <div class="pagination">
                <?php if ($page > 1): ?>
                    <a href="?page=<?php echo $page - 1; ?><?php echo $category ? '&category=' . $category : ''; ?><?php echo $search ? '&q=' . urlencode($search) : ''; ?>" class="prev-page">
                        <i class="bi bi-chevron-left"></i>
                    </a>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                    <a href="?page=<?php echo $i; ?><?php echo $category ? '&category=' . $category : ''; ?><?php echo $search ? '&q=' . urlencode($search) : ''; ?>" 
                       <?php echo $i === $page ? 'class="active"' : ''; ?>>
                        <?php echo $i; ?>
                    </a>
                <?php endfor; ?>
                
                <?php if ($page < $totalPages): ?>
                    <a href="?page=<?php echo $page + 1; ?><?php echo $category ? '&category=' . $category : ''; ?><?php echo $search ? '&q=' . urlencode($search) : ''; ?>" class="next-page">
                        <i class="bi bi-chevron-right"></i>
                    </a>
                <?php endif; ?>
                
                <div class="page-jump">
                    <span>到第</span>
                    <input type="text" value="<?php echo $page; ?>">
                    <span>页</span>
                    <button>确定</button>
                </div>
            </div>
        <?php endif; ?>
    </div>
</main>

<?php require_once 'includes/footer.php'; ?>
