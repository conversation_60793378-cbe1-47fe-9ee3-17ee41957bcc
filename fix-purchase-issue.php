<?php
// 修复购买功能问题
require_once 'includes/functions.php';

// 设置JSON响应头
header('Content-Type: application/json; charset=utf-8');

try {
    $fixes = [];
    $errors = [];
    
    // 1. 检查并修复orders表结构
    $stmt = $pdo->query("SHOW COLUMNS FROM orders LIKE 'order_number'");
    $hasOrderNumber = $stmt->fetch() !== false;
    
    if (!$hasOrderNumber) {
        // 添加order_number字段
        $pdo->exec("ALTER TABLE orders ADD COLUMN order_number VARCHAR(50) UNIQUE AFTER id");
        $fixes[] = "添加 order_number 字段";
        
        // 为现有订单生成订单号
        $stmt = $pdo->query("SELECT id, created_at FROM orders WHERE order_number IS NULL OR order_number = ''");
        $orders = $stmt->fetchAll();
        
        foreach ($orders as $order) {
            $orderNumber = 'XY' . date('YmdHis', strtotime($order['created_at'])) . str_pad($order['id'], 4, '0', STR_PAD_LEFT);
            $updateStmt = $pdo->prepare("UPDATE orders SET order_number = ? WHERE id = ?");
            $updateStmt->execute([$orderNumber, $order['id']]);
        }
        $fixes[] = "为现有订单生成订单号";
    } else {
        $fixes[] = "order_number 字段已存在";
    }
    
    // 2. 检查virtual_attributes表
    $stmt = $pdo->query("SHOW TABLES LIKE 'virtual_attributes'");
    $virtualTableExists = $stmt->fetch() !== false;
    
    if (!$virtualTableExists) {
        // 创建virtual_attributes表
        $sql = "CREATE TABLE virtual_attributes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            product_id INT NOT NULL,
            delivery_method ENUM('manual', 'automatic') DEFAULT 'manual',
            content TEXT,
            download_link VARCHAR(255),
            activation_code VARCHAR(100),
            instructions TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
            UNIQUE KEY unique_product_virtual (product_id)
        )";
        $pdo->exec($sql);
        $fixes[] = "创建 virtual_attributes 表";
    } else {
        $fixes[] = "virtual_attributes 表已存在";
    }
    
    // 3. 检查products表是否有is_virtual字段
    $stmt = $pdo->query("SHOW COLUMNS FROM products LIKE 'is_virtual'");
    $hasIsVirtual = $stmt->fetch() !== false;
    
    if (!$hasIsVirtual) {
        $pdo->exec("ALTER TABLE products ADD COLUMN is_virtual BOOLEAN DEFAULT TRUE AFTER stock");
        $fixes[] = "添加 is_virtual 字段";
        
        // 将所有现有商品设为虚拟商品
        $pdo->exec("UPDATE products SET is_virtual = TRUE WHERE is_virtual IS NULL");
        $fixes[] = "设置现有商品为虚拟商品";
    } else {
        $fixes[] = "is_virtual 字段已存在";
    }
    
    // 4. 添加必要的索引
    try {
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_order_number ON orders(order_number)");
        $fixes[] = "添加 order_number 索引";
    } catch (Exception $e) {
        // 索引可能已存在
    }
    
    try {
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_buyer_id ON orders(buyer_id)");
        $fixes[] = "添加 buyer_id 索引";
    } catch (Exception $e) {
        // 索引可能已存在
    }
    
    try {
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_seller_id ON orders(seller_id)");
        $fixes[] = "添加 seller_id 索引";
    } catch (Exception $e) {
        // 索引可能已存在
    }
    
    // 5. 测试购买功能
    $testResult = [];
    
    // 检查是否有测试用户
    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
    $userCount = $stmt->fetchColumn();
    $testResult['user_count'] = $userCount;
    
    // 检查是否有测试商品
    $stmt = $pdo->query("SELECT COUNT(*) FROM products WHERE status = 'active'");
    $productCount = $stmt->fetchColumn();
    $testResult['product_count'] = $productCount;
    
    // 检查表结构
    $stmt = $pdo->query("DESCRIBE orders");
    $orderColumns = [];
    while ($row = $stmt->fetch()) {
        $orderColumns[] = $row['Field'];
    }
    $testResult['order_columns'] = $orderColumns;
    
    echo json_encode([
        'success' => true,
        'message' => '购买功能修复完成',
        'fixes' => $fixes,
        'errors' => $errors,
        'test_result' => $testResult
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '修复失败：' . $e->getMessage(),
        'fixes' => $fixes ?? [],
        'errors' => [$e->getMessage()],
        'debug' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ], JSON_UNESCAPED_UNICODE);
}
?>
