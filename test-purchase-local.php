<?php
// 本地购买功能测试
require_once 'includes/functions.php';

echo "=== 购买功能本地测试 ===\n\n";

try {
    // 1. 检查基础环境
    echo "1. 检查基础环境...\n";
    echo "   PHP版本: " . PHP_VERSION . "\n";
    echo "   数据库连接: " . (isset($pdo) ? "✅ 已连接" : "❌ 未连接") . "\n";
    
    if (!isset($pdo)) {
        throw new Exception("数据库连接失败");
    }
    
    // 2. 检查会话状态
    echo "\n2. 检查会话状态...\n";
    echo "   会话状态: " . (session_status() === PHP_SESSION_ACTIVE ? "✅ 已启动" : "❌ 未启动") . "\n";
    
    // 3. 模拟用户登录
    echo "\n3. 模拟用户登录...\n";
    $stmt = $pdo->query("SELECT id, username FROM users LIMIT 2");
    $users = $stmt->fetchAll();
    
    if (count($users) < 2) {
        throw new Exception("需要至少2个用户进行测试");
    }
    
    // 使用第一个用户作为买家
    $_SESSION['user_id'] = $users[0]['id'];
    $_SESSION['username'] = $users[0]['username'];
    echo "   登录用户: {$users[0]['username']} (ID: {$users[0]['id']})\n";
    
    // 4. 获取测试商品
    echo "\n4. 获取测试商品...\n";
    $stmt = $pdo->prepare("SELECT * FROM products WHERE status = 'active' AND user_id != ? LIMIT 1");
    $stmt->execute([$_SESSION['user_id']]);
    $product = $stmt->fetch();
    
    if (!$product) {
        throw new Exception("没有找到可购买的商品");
    }
    
    echo "   商品ID: {$product['id']}\n";
    echo "   商品标题: {$product['title']}\n";
    echo "   商品价格: ¥{$product['price']}\n";
    echo "   商品库存: {$product['stock']}\n";
    echo "   卖家ID: {$product['user_id']}\n";
    
    // 5. 模拟购买请求
    echo "\n5. 模拟购买请求...\n";
    
    // 模拟POST数据
    $_POST = [
        'product_id' => $product['id'],
        'quantity' => 1,
        'action' => 'buy_now'
    ];
    $_SERVER['REQUEST_METHOD'] = 'POST';
    
    // 开始输出缓冲
    ob_start();
    
    // 包含购买API
    include 'api/purchase-minimal.php';
    
    // 获取API响应
    $response = ob_get_clean();
    
    echo "   API响应: $response\n";
    
    // 解析响应
    $data = json_decode($response, true);
    
    if ($data && $data['success']) {
        echo "   ✅ 购买成功！\n";
        echo "   订单ID: {$data['order_id']}\n";
        echo "   订单号: {$data['order_number']}\n";
        echo "   商品标题: {$data['product_title']}\n";
        echo "   总价: ¥{$data['total_price']}\n";
        
        // 6. 验证数据库变化
        echo "\n6. 验证数据库变化...\n";
        
        // 检查订单是否创建
        $stmt = $pdo->prepare("SELECT * FROM orders WHERE id = ?");
        $stmt->execute([$data['order_id']]);
        $order = $stmt->fetch();
        
        if ($order) {
            echo "   ✅ 订单已创建\n";
            echo "   买家ID: {$order['buyer_id']}\n";
            echo "   卖家ID: {$order['seller_id']}\n";
            echo "   商品ID: {$order['product_id']}\n";
            echo "   数量: {$order['quantity']}\n";
            echo "   总价: ¥{$order['total_price']}\n";
            echo "   状态: {$order['status']}\n";
        } else {
            echo "   ❌ 订单未找到\n";
        }
        
        // 检查库存是否减少
        $stmt = $pdo->prepare("SELECT stock FROM products WHERE id = ?");
        $stmt->execute([$product['id']]);
        $newStock = $stmt->fetchColumn();
        
        echo "   库存变化: {$product['stock']} -> $newStock\n";
        
        if ($newStock == $product['stock'] - 1) {
            echo "   ✅ 库存正确减少\n";
        } else {
            echo "   ❌ 库存减少异常\n";
        }
        
    } else {
        echo "   ❌ 购买失败\n";
        if ($data && isset($data['message'])) {
            echo "   错误信息: {$data['message']}\n";
        }
        if ($data && isset($data['error_details'])) {
            echo "   错误详情: {$data['error_details']['file']}:{$data['error_details']['line']}\n";
        }
    }
    
    echo "\n=== 测试完成 ===\n";
    
} catch (Exception $e) {
    echo "\n❌ 测试失败: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
?>
