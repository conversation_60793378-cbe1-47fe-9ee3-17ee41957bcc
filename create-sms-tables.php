<?php
require_once 'includes/functions.php';

header('Content-Type: application/json; charset=utf-8');

try {
    $createdTables = [];
    $errors = [];
    
    // 创建短信验证码表
    $sql = "CREATE TABLE IF NOT EXISTS sms_codes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        phone VARCHAR(20) NOT NULL,
        code VARCHAR(10) NOT NULL,
        type ENUM('login', 'register', 'reset_password', 'bind_phone', 'security') DEFAULT 'login',
        used BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_phone (phone),
        INDEX idx_phone_type (phone, type),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($pdo->exec($sql) !== false) {
        $createdTables[] = 'sms_codes';
    }
    
    // 创建短信发送日志表
    $sql = "CREATE TABLE IF NOT EXISTS sms_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        phone VARCHAR(20) NOT NULL,
        type ENUM('login', 'register', 'reset_password', 'bind_phone', 'security') DEFAULT 'login',
        status ENUM('success', 'failed', 'pending') DEFAULT 'pending',
        provider VARCHAR(20) DEFAULT 'mock',
        error_message TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_phone (phone),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at),
        INDEX idx_phone_date (phone, created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($pdo->exec($sql) !== false) {
        $createdTables[] = 'sms_logs';
    }
    
    // 创建短信模板表
    $sql = "CREATE TABLE IF NOT EXISTS sms_templates (
        id INT AUTO_INCREMENT PRIMARY KEY,
        type VARCHAR(50) NOT NULL UNIQUE,
        name VARCHAR(100) NOT NULL,
        content TEXT NOT NULL,
        template_code VARCHAR(50),
        provider VARCHAR(20) DEFAULT 'mock',
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_type (type),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($pdo->exec($sql) !== false) {
        $createdTables[] = 'sms_templates';
    }
    
    // 插入默认短信模板
    $templates = [
        [
            'type' => 'login',
            'name' => '登录验证码',
            'content' => '您的登录验证码是：{code}，有效期10分钟，请勿泄露给他人。【数字鱼】',
            'template_code' => 'SMS_LOGIN_001'
        ],
        [
            'type' => 'register',
            'name' => '注册验证码',
            'content' => '欢迎注册数字鱼！您的验证码是：{code}，有效期10分钟。【数字鱼】',
            'template_code' => 'SMS_REGISTER_001'
        ],
        [
            'type' => 'reset_password',
            'name' => '重置密码验证码',
            'content' => '您正在重置密码，验证码是：{code}，有效期10分钟，请勿泄露。【数字鱼】',
            'template_code' => 'SMS_RESET_001'
        ],
        [
            'type' => 'bind_phone',
            'name' => '绑定手机验证码',
            'content' => '您正在绑定手机号，验证码是：{code}，有效期10分钟。【数字鱼】',
            'template_code' => 'SMS_BIND_001'
        ],
        [
            'type' => 'security',
            'name' => '安全验证码',
            'content' => '安全验证码：{code}，用于重要操作验证，有效期10分钟，请勿泄露。【数字鱼】',
            'template_code' => 'SMS_SECURITY_001'
        ]
    ];
    
    foreach ($templates as $template) {
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO sms_templates (type, name, content, template_code) 
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([
            $template['type'],
            $template['name'],
            $template['content'],
            $template['template_code']
        ]);
    }
    
    // 创建短信配置表
    $sql = "CREATE TABLE IF NOT EXISTS sms_config (
        id INT AUTO_INCREMENT PRIMARY KEY,
        provider VARCHAR(20) NOT NULL,
        access_key VARCHAR(100),
        access_secret VARCHAR(100),
        sign_name VARCHAR(50),
        region VARCHAR(20) DEFAULT 'cn-hangzhou',
        is_active BOOLEAN DEFAULT FALSE,
        daily_limit INT DEFAULT 1000,
        rate_limit INT DEFAULT 60,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_provider (provider)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($pdo->exec($sql) !== false) {
        $createdTables[] = 'sms_config';
    }
    
    // 插入默认配置
    $configs = [
        [
            'provider' => 'mock',
            'sign_name' => '数字鱼',
            'is_active' => true,
            'daily_limit' => 100,
            'rate_limit' => 60
        ],
        [
            'provider' => 'aliyun',
            'sign_name' => '数字鱼',
            'is_active' => false,
            'daily_limit' => 1000,
            'rate_limit' => 60
        ],
        [
            'provider' => 'tencent',
            'sign_name' => '数字鱼',
            'is_active' => false,
            'daily_limit' => 1000,
            'rate_limit' => 60
        ]
    ];
    
    foreach ($configs as $config) {
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO sms_config (provider, sign_name, is_active, daily_limit, rate_limit) 
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $config['provider'],
            $config['sign_name'],
            $config['is_active'],
            $config['daily_limit'],
            $config['rate_limit']
        ]);
    }
    
    echo json_encode([
        'success' => true,
        'message' => '短信功能表创建成功',
        'created_tables' => $createdTables,
        'errors' => $errors
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}
?>
