<?php
// 调试购买功能
require_once 'includes/functions.php';

// 设置JSON响应头
header('Content-Type: application/json; charset=utf-8');

try {
    // 检查orders表结构
    $stmt = $pdo->query('DESCRIBE orders');
    $ordersStructure = [];
    while ($row = $stmt->fetch()) {
        $ordersStructure[] = $row['Field'] . ' - ' . $row['Type'];
    }
    
    // 检查virtual_attributes表是否存在
    $stmt = $pdo->query('SHOW TABLES LIKE "virtual_attributes"');
    $virtualTableExists = $stmt->fetch() !== false;
    
    // 检查所有表
    $stmt = $pdo->query('SHOW TABLES');
    $allTables = [];
    while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
        $allTables[] = $row[0];
    }
    
    // 测试一个简单的查询
    $stmt = $pdo->query('SELECT COUNT(*) as count FROM products');
    $productCount = $stmt->fetchColumn();
    
    echo json_encode([
        'success' => true,
        'orders_structure' => $ordersStructure,
        'virtual_table_exists' => $virtualTableExists,
        'all_tables' => $allTables,
        'product_count' => $productCount,
        'session_user_id' => $_SESSION['user_id'] ?? 'not logged in'
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}
?>
