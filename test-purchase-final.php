<?php
require_once 'includes/functions.php';

// 模拟登录（仅用于测试）
if (!isLoggedIn()) {
    // 获取第一个用户进行测试
    $stmt = $pdo->query("SELECT id, username FROM users LIMIT 1");
    $user = $stmt->fetch();
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
    }
}

// 获取一个测试商品
$stmt = $pdo->query("SELECT * FROM products WHERE status = 'active' LIMIT 1");
$testProduct = $stmt->fetch();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>购买功能最终测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .info-box {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .debug {
            background: #e2e3e5;
            color: #383d41;
            border: 1px solid #d6d8db;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>购买功能最终测试</h1>
        
        <div class="info-box">
            <h3>当前状态</h3>
            <p><strong>登录状态:</strong> <?php echo isLoggedIn() ? '已登录' : '未登录'; ?></p>
            <p><strong>用户ID:</strong> <?php echo $_SESSION['user_id'] ?? '无'; ?></p>
            <p><strong>用户名:</strong> <?php echo $_SESSION['username'] ?? '无'; ?></p>
        </div>
        
        <?php if ($testProduct): ?>
        <div class="info-box">
            <h3>测试商品</h3>
            <p><strong>ID:</strong> <?php echo $testProduct['id']; ?></p>
            <p><strong>标题:</strong> <?php echo htmlspecialchars($testProduct['title']); ?></p>
            <p><strong>价格:</strong> ¥<?php echo number_format($testProduct['price'], 2); ?></p>
            <p><strong>库存:</strong> <?php echo $testProduct['stock']; ?></p>
            <p><strong>卖家ID:</strong> <?php echo $testProduct['user_id']; ?></p>
        </div>
        
        <div>
            <button class="btn" onclick="testAPI()">1. 测试API基础功能</button>
            <button class="btn" onclick="testPurchase(<?php echo $testProduct['id']; ?>)">2. 测试购买功能</button>
            <button class="btn" onclick="testDirectPurchase(<?php echo $testProduct['id']; ?>)">3. 直接购买测试</button>
        </div>
        
        <?php else: ?>
        <div class="info-box error">
            <p>没有找到测试商品。请先添加商品数据。</p>
        </div>
        <?php endif; ?>
        
        <div id="result" class="result"></div>
    </div>

    <script>
    function showResult(message, type = 'success') {
        const resultDiv = document.getElementById('result');
        resultDiv.className = 'result ' + type;
        resultDiv.innerHTML = message;
        resultDiv.style.display = 'block';
    }

    function testAPI() {
        showResult('正在测试API基础功能...', 'debug');
        
        fetch('api/test-basic.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'test=1'
        })
        .then(response => response.text())
        .then(text => {
            try {
                const data = JSON.parse(text);
                let message = '<h4>API基础功能测试结果:</h4>';
                message += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                showResult(message, data.success ? 'success' : 'error');
            } catch (e) {
                showResult('API响应解析失败:<br>' + text, 'error');
            }
        })
        .catch(error => {
            showResult('API测试失败: ' + error.message, 'error');
        });
    }

    function testPurchase(productId) {
        showResult('正在测试购买功能...', 'debug');
        
        const formData = new FormData();
        formData.append('product_id', productId);
        formData.append('quantity', 1);
        formData.append('action', 'buy_now');

        fetch('api/purchase-simple.php', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log('Response status:', response.status);
            return response.text();
        })
        .then(text => {
            console.log('Raw response:', text);
            try {
                const data = JSON.parse(text);
                if (data.success) {
                    let message = `<h4>购买成功！</h4>`;
                    message += `<p>订单ID: ${data.order_id}</p>`;
                    message += `<p>订单号: ${data.order_number}</p>`;
                    message += `<p>商品: ${data.product_title}</p>`;
                    message += `<p>总价: ¥${data.total_price}</p>`;
                    showResult(message, 'success');
                } else {
                    let message = `<h4>购买失败</h4><p>${data.message}</p>`;
                    if (data.debug) {
                        message += `<p>调试信息: ${data.debug.file}:${data.debug.line}</p>`;
                    }
                    showResult(message, 'error');
                }
            } catch (e) {
                showResult(`JSON解析错误: ${e.message}<br><br>原始响应:<br>${text}`, 'error');
            }
        })
        .catch(error => {
            showResult('网络错误: ' + error.message, 'error');
        });
    }

    function testDirectPurchase(productId) {
        showResult('正在进行直接购买测试...', 'debug');
        
        // 使用简单的POST请求
        const params = new URLSearchParams();
        params.append('product_id', productId);
        params.append('quantity', 1);
        params.append('action', 'buy_now');

        fetch('api/purchase-simple.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: params
        })
        .then(response => {
            console.log('Direct purchase response status:', response.status);
            return response.text();
        })
        .then(text => {
            console.log('Direct purchase raw response:', text);
            showResult('直接购买响应:<br>' + text, 'debug');
        })
        .catch(error => {
            showResult('直接购买失败: ' + error.message, 'error');
        });
    }
    </script>
</body>
</html>
