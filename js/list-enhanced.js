// 商品列表页增强功能
document.addEventListener('DOMContentLoaded', function() {
    
    // 初始化所有功能
    initFilterToggle();
    initViewSwitcher();
    initProductActions();
    initInfiniteScroll();
    initFilterForm();
    initSortingEffects();
    
    // 筛选器切换
    function initFilterToggle() {
        const filterToggle = document.querySelector('.filter-toggle');
        const advancedFilters = document.getElementById('advancedFilters');
        
        if (filterToggle && advancedFilters) {
            filterToggle.addEventListener('click', function() {
                const isVisible = advancedFilters.style.display !== 'none';
                advancedFilters.style.display = isVisible ? 'none' : 'block';
                
                // 更新按钮状态
                this.classList.toggle('active', !isVisible);
                
                // 添加动画效果
                if (!isVisible) {
                    advancedFilters.style.opacity = '0';
                    advancedFilters.style.transform = 'translateY(-10px)';
                    
                    setTimeout(() => {
                        advancedFilters.style.transition = 'all 0.3s ease';
                        advancedFilters.style.opacity = '1';
                        advancedFilters.style.transform = 'translateY(0)';
                    }, 10);
                }
            });
        }
    }
    
    // 视图切换器
    function initViewSwitcher() {
        const viewBtns = document.querySelectorAll('.view-btn');
        const productGrid = document.getElementById('productGrid');
        
        if (viewBtns.length && productGrid) {
            viewBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 更新按钮状态
                    viewBtns.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 切换视图
                    const view = this.dataset.view;
                    productGrid.className = view === 'list' ? 'product-list' : 'product-grid';
                    
                    // 保存用户偏好
                    localStorage.setItem('preferredView', view);
                    
                    // 添加切换动画
                    productGrid.style.opacity = '0.5';
                    setTimeout(() => {
                        productGrid.style.opacity = '1';
                    }, 150);
                });
            });
            
            // 恢复用户偏好的视图
            const savedView = localStorage.getItem('preferredView');
            if (savedView) {
                const targetBtn = document.querySelector(`[data-view="${savedView}"]`);
                if (targetBtn) {
                    targetBtn.click();
                }
            }
        }
    }
    
    // 商品操作
    function initProductActions() {
        // 点赞功能
        document.querySelectorAll('.like-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const productId = this.dataset.productId;
                const isLiked = this.classList.contains('liked');
                
                // 切换状态
                this.classList.toggle('liked');
                
                // 添加动画效果
                this.style.transform = 'scale(1.2)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
                
                // 这里可以添加 AJAX 请求到服务器
                console.log(`${isLiked ? 'Unlike' : 'Like'} product:`, productId);
            });
        });
        
        // 快速查看
        document.querySelectorAll('.quick-view-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const productId = this.dataset.productId;
                showQuickView(productId);
            });
        });
        
        // 分享功能
        document.querySelectorAll('.share-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const productId = this.dataset.productId;
                showShareModal(productId);
            });
        });
    }
    
    // 无限滚动加载
    function initInfiniteScroll() {
        let loading = false;
        let currentPage = parseInt(new URLSearchParams(window.location.search).get('page')) || 1;
        
        window.addEventListener('scroll', function() {
            if (loading) return;
            
            const scrollTop = window.scrollY;
            const windowHeight = window.innerHeight;
            const documentHeight = document.documentElement.scrollHeight;
            
            if (scrollTop + windowHeight >= documentHeight - 1000) {
                loadMoreProducts();
            }
        });
        
        function loadMoreProducts() {
            loading = true;
            currentPage++;
            
            // 显示加载指示器
            showLoadingIndicator();
            
            // 构建请求URL
            const url = new URL(window.location);
            url.searchParams.set('page', currentPage);
            url.searchParams.set('ajax', '1');
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.products && data.products.length > 0) {
                        appendProducts(data.products);
                    } else {
                        // 没有更多商品了
                        showNoMoreMessage();
                    }
                })
                .catch(error => {
                    console.error('加载失败:', error);
                    currentPage--; // 回退页码
                })
                .finally(() => {
                    loading = false;
                    hideLoadingIndicator();
                });
        }
        
        function showLoadingIndicator() {
            const indicator = document.createElement('div');
            indicator.className = 'loading-indicator';
            indicator.innerHTML = `
                <div class="loading-spinner"></div>
                <span>加载中...</span>
            `;
            document.querySelector('.products-container').appendChild(indicator);
        }
        
        function hideLoadingIndicator() {
            const indicator = document.querySelector('.loading-indicator');
            if (indicator) {
                indicator.remove();
            }
        }
        
        function appendProducts(products) {
            const productGrid = document.getElementById('productGrid');
            products.forEach(product => {
                const productElement = createProductElement(product);
                productGrid.appendChild(productElement);
            });
        }
        
        function showNoMoreMessage() {
            const message = document.createElement('div');
            message.className = 'no-more-message';
            message.textContent = '没有更多商品了';
            document.querySelector('.products-container').appendChild(message);
        }
    }
    
    // 筛选表单增强
    function initFilterForm() {
        const filterForm = document.querySelector('.filter-form');
        if (filterForm) {
            // 价格输入验证
            const priceInputs = filterForm.querySelectorAll('.price-input');
            priceInputs.forEach(input => {
                input.addEventListener('input', function() {
                    this.value = this.value.replace(/[^\d.]/g, '');
                });
            });
            
            // 实时筛选（防抖）
            let debounceTimer;
            filterForm.addEventListener('input', function() {
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => {
                    // 这里可以添加实时筛选逻辑
                    console.log('实时筛选');
                }, 500);
            });
        }
    }
    
    // 排序效果
    function initSortingEffects() {
        const sortItems = document.querySelectorAll('.sort-item');
        sortItems.forEach(item => {
            item.addEventListener('click', function(e) {
                if (!this.classList.contains('active')) {
                    // 添加加载效果
                    const productGrid = document.getElementById('productGrid');
                    productGrid.style.opacity = '0.5';
                    productGrid.style.transform = 'scale(0.98)';
                    
                    // 模拟加载时间
                    setTimeout(() => {
                        productGrid.style.opacity = '1';
                        productGrid.style.transform = 'scale(1)';
                    }, 300);
                }
            });
        });
    }
    
    // 快速查看模态框
    function showQuickView(productId) {
        const modal = document.createElement('div');
        modal.className = 'quick-view-modal';
        modal.innerHTML = `
            <div class="modal-overlay"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3>商品快速查看</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="loading-spinner"></div>
                    <p>加载中...</p>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // 关闭模态框
        modal.querySelector('.modal-close').addEventListener('click', () => {
            modal.remove();
        });
        
        modal.querySelector('.modal-overlay').addEventListener('click', () => {
            modal.remove();
        });
        
        // 这里可以添加 AJAX 请求获取商品详情
        setTimeout(() => {
            modal.querySelector('.modal-body').innerHTML = `
                <p>商品ID: ${productId}</p>
                <p>这里显示商品详细信息</p>
            `;
        }, 1000);
    }
    
    // 分享模态框
    function showShareModal(productId) {
        const modal = document.createElement('div');
        modal.className = 'share-modal';
        modal.innerHTML = `
            <div class="modal-overlay"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3>分享商品</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="share-options">
                        <button class="share-btn" data-platform="wechat">微信</button>
                        <button class="share-btn" data-platform="weibo">微博</button>
                        <button class="share-btn" data-platform="qq">QQ</button>
                        <button class="share-btn" data-platform="copy">复制链接</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // 关闭模态框
        modal.querySelector('.modal-close').addEventListener('click', () => {
            modal.remove();
        });
        
        modal.querySelector('.modal-overlay').addEventListener('click', () => {
            modal.remove();
        });
        
        // 分享按钮事件
        modal.querySelectorAll('.share-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const platform = this.dataset.platform;
                handleShare(platform, productId);
                modal.remove();
            });
        });
    }
    
    // 处理分享
    function handleShare(platform, productId) {
        const url = `${window.location.origin}/detail.php?id=${productId}`;
        
        switch (platform) {
            case 'copy':
                navigator.clipboard.writeText(url).then(() => {
                    showToast('链接已复制到剪贴板');
                });
                break;
            case 'wechat':
                // 微信分享逻辑
                showToast('请使用微信扫码分享');
                break;
            default:
                console.log(`分享到 ${platform}:`, url);
        }
    }
    
    // 显示提示消息
    function showToast(message) {
        const toast = document.createElement('div');
        toast.className = 'toast';
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #333;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            z-index: 10000;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateX(0)';
        }, 10);
        
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }
    
    // 创建商品元素（用于无限滚动）
    function createProductElement(product) {
        const div = document.createElement('div');
        div.className = 'product-card enhanced-product-card';
        div.innerHTML = `
            <a href="detail.php?id=${product.id}" class="product-link">
                <div class="product-image">
                    <img src="${product.image}" alt="${product.title}" loading="lazy">
                    <div class="product-badges">
                        ${product.is_virtual ? '<span class="badge virtual-badge"><i class="bi bi-lightning-charge"></i>虚拟商品</span>' : ''}
                    </div>
                    <div class="product-overlay">
                        <div class="overlay-actions">
                            <button class="action-btn like-btn" data-product-id="${product.id}">
                                <i class="bi bi-heart"></i>
                            </button>
                            <button class="action-btn quick-view-btn" data-product-id="${product.id}">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="action-btn share-btn" data-product-id="${product.id}">
                                <i class="bi bi-share"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="product-content">
                    <h3 class="product-title">${product.title}</h3>
                    <div class="product-price">
                        <span class="current-price">¥${product.price}</span>
                    </div>
                    <div class="product-meta">
                        <div class="seller-info">
                            <img src="${product.seller_avatar}" alt="卖家头像" class="seller-avatar">
                            <span class="seller-name">${product.seller_name}</span>
                        </div>
                        <div class="product-stats">
                            <span class="stat-item">
                                <i class="bi bi-eye"></i>
                                ${product.views}
                            </span>
                            <span class="stat-item">
                                <i class="bi bi-heart"></i>
                                ${product.likes}
                            </span>
                        </div>
                    </div>
                </div>
            </a>
        `;
        
        return div;
    }
});
