/**
 * 发布页面交互脚本 - 数字鱼虚拟商品交易平台
 */

document.addEventListener('DOMContentLoaded', function() {
    // 初始化各个模块
    initProductTypeToggle();
    initUsagePeriodToggle();
    initImageUpload();
    initFileUpload();
    initContentTabs();
    initFormValidation();
    initFormActions();
    
    // 添加表单提交事件
    document.getElementById('publishForm').addEventListener('submit', handleFormSubmit);
});

/**
 * 初始化商品类型切换
 */
function initProductTypeToggle() {
    const typePhysical = document.getElementById('typePhysical');
    const typeVirtual = document.getElementById('typeVirtual');
    const virtualCategoryGroup = document.getElementById('virtualCategoryGroup');
    const virtualAttributesGroup = document.getElementById('virtualAttributesGroup');
    const contentGroup = document.getElementById('contentGroup');
    
    // 切换商品类型时的处理
    function toggleProductType() {
        if (typeVirtual.checked) {
            virtualCategoryGroup.style.display = 'block';
            virtualAttributesGroup.style.display = 'block';
            contentGroup.style.display = 'block';
            
            // 添加动画效果
            virtualCategoryGroup.classList.add('animate-fade-in');
            virtualAttributesGroup.classList.add('animate-fade-in');
            contentGroup.classList.add('animate-fade-in');
        } else {
            virtualCategoryGroup.style.display = 'none';
            virtualAttributesGroup.style.display = 'none';
            contentGroup.style.display = 'none';
        }
    }
    
    // 初始化时执行一次
    toggleProductType();
    
    // 添加事件监听
    typePhysical.addEventListener('change', toggleProductType);
    typeVirtual.addEventListener('change', toggleProductType);
    
    // 添加选择效果
    const typeOptions = document.querySelectorAll('.type-option');
    typeOptions.forEach(option => {
        option.addEventListener('click', function() {
            const radio = this.querySelector('input[type="radio"]');
            radio.checked = true;
            
            // 触发change事件
            const event = new Event('change');
            radio.dispatchEvent(event);
        });
    });
}

/**
 * 初始化使用期限切换
 */
function initUsagePeriodToggle() {
    const usagePeriod = document.getElementById('usagePeriod');
    const periodInput = document.querySelector('.period-input');
    
    function togglePeriodInput() {
        if (usagePeriod.value === 'limited') {
            periodInput.style.display = 'flex';
            periodInput.classList.add('animate-fade-in');
        } else {
            periodInput.style.display = 'none';
        }
    }
    
    // 初始化时执行一次
    togglePeriodInput();
    
    // 添加事件监听
    usagePeriod.addEventListener('change', togglePeriodInput);
}

/**
 * 初始化图片上传
 */
function initImageUpload() {
    const imageUpload = document.getElementById('imageUpload');
    const uploadPreview = document.querySelector('.upload-preview');
    const addImageBtn = document.querySelector('.add-image');
    
    // 最大上传数量
    const maxImages = 9;
    // 已上传的图片数量
    let uploadedCount = 0;
    
    // 处理图片上传
    imageUpload.addEventListener('change', function(e) {
        const files = e.target.files;
        
        if (!files || !files.length) return;
        
        // 检查是否超过最大数量
        if (uploadedCount + files.length > maxImages) {
            showMessage('最多只能上传9张图片', 'warning');
            return;
        }
        
        // 处理每个文件
        Array.from(files).forEach(file => {
            // 检查文件类型
            if (!file.type.startsWith('image/')) {
                showMessage(`${file.name} 不是有效的图片文件`, 'error');
                return;
            }
            
            // 检查文件大小
            if (file.size > 5 * 1024 * 1024) { // 5MB
                showMessage(`${file.name} 超过5MB限制`, 'error');
                return;
            }
            
            // 创建预览
            createImagePreview(file);
            uploadedCount++;
        });
        
        // 重置input，允许重复选择同一文件
        imageUpload.value = '';
        
        // 如果达到最大数量，隐藏添加按钮
        if (uploadedCount >= maxImages) {
            addImageBtn.style.display = 'none';
        }
    });
    
    // 创建图片预览
    function createImagePreview(file) {
        const reader = new FileReader();

        reader.onload = function(e) {
            const previewItem = document.createElement('div');
            previewItem.className = 'upload-item preview-image';

            // 创建隐藏的文件输入框来存储文件
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'file';
            hiddenInput.name = 'images[]';
            hiddenInput.style.display = 'none';
            hiddenInput.files = createFileList([file]);

            previewItem.innerHTML = `
                <img src="${e.target.result}" alt="预览图">
                <div class="preview-actions">
                    <span class="preview-remove" title="删除"><i class="bi bi-x-lg"></i></span>
                </div>
            `;

            // 添加隐藏的输入框
            previewItem.appendChild(hiddenInput);

            // 插入到添加按钮之前
            uploadPreview.insertBefore(previewItem, addImageBtn);

            // 添加动画效果
            setTimeout(() => {
                previewItem.classList.add('animate-scale-in');
            }, 10);

            // 添加删除事件
            const removeBtn = previewItem.querySelector('.preview-remove');
            removeBtn.addEventListener('click', function() {
                previewItem.classList.add('animate-fade-out');

                setTimeout(() => {
                    uploadPreview.removeChild(previewItem);
                    uploadedCount--;

                    // 如果低于最大数量，显示添加按钮
                    if (uploadedCount < maxImages) {
                        addImageBtn.style.display = 'flex';
                    }
                }, 300);
            });
        };

        reader.readAsDataURL(file);
    }

    // 创建FileList对象的辅助函数
    function createFileList(files) {
        const dt = new DataTransfer();
        files.forEach(file => dt.items.add(file));
        return dt.files;
    }
}

/**
 * 初始化文件上传
 */
function initFileUpload() {
    const fileUpload = document.getElementById('fileUpload');
    const fileList = document.querySelector('.file-list');
    
    // 处理文件上传
    fileUpload.addEventListener('change', function(e) {
        const files = e.target.files;
        
        if (!files || !files.length) return;
        
        // 处理每个文件
        Array.from(files).forEach(file => {
            // 检查文件大小
            if (file.size > 50 * 1024 * 1024) { // 50MB
                showMessage(`${file.name} 超过50MB限制`, 'error');
                return;
            }
            
            // 创建文件项
            createFileItem(file);
        });
        
        // 重置input，允许重复选择同一文件
        fileUpload.value = '';
    });
    
    // 创建文件项
    function createFileItem(file) {
        // 获取文件图标
        const fileIcon = getFileIcon(file.name);
        
        // 格式化文件大小
        const fileSize = formatFileSize(file.size);
        
        const fileItem = document.createElement('div');
        fileItem.className = 'file-item';
        fileItem.innerHTML = `
            <i class="file-icon bi ${fileIcon}"></i>
            <div class="file-info">
                <div class="file-name">${file.name}</div>
                <div class="file-size">${fileSize}</div>
            </div>
            <i class="file-remove bi bi-x-circle"></i>
        `;
        
        // 添加到文件列表
        fileList.appendChild(fileItem);
        
        // 添加动画效果
        setTimeout(() => {
            fileItem.classList.add('animate-fade-in');
        }, 10);
        
        // 添加删除事件
        const removeBtn = fileItem.querySelector('.file-remove');
        removeBtn.addEventListener('click', function() {
            fileItem.classList.add('animate-fade-out');
            
            setTimeout(() => {
                fileList.removeChild(fileItem);
            }, 300);
        });
    }
    
    // 根据文件扩展名获取图标
    function getFileIcon(filename) {
        const ext = filename.split('.').pop().toLowerCase();
        
        switch (ext) {
            case 'pdf':
                return 'bi-file-earmark-pdf';
            case 'doc':
            case 'docx':
                return 'bi-file-earmark-word';
            case 'xls':
            case 'xlsx':
                return 'bi-file-earmark-excel';
            case 'ppt':
            case 'pptx':
                return 'bi-file-earmark-ppt';
            case 'zip':
            case 'rar':
            case '7z':
                return 'bi-file-earmark-zip';
            case 'jpg':
            case 'jpeg':
            case 'png':
            case 'gif':
                return 'bi-file-earmark-image';
            case 'mp3':
            case 'wav':
            case 'ogg':
                return 'bi-file-earmark-music';
            case 'mp4':
            case 'avi':
            case 'mov':
                return 'bi-file-earmark-play';
            default:
                return 'bi-file-earmark';
        }
    }
    
    // 格式化文件大小
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

/**
 * 初始化内容选项卡
 */
function initContentTabs() {
    const contentTabs = document.querySelectorAll('.content-tab');
    const contentPanels = document.querySelectorAll('.content-panel');
    
    contentTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // 移除所有活动状态
            contentTabs.forEach(t => t.classList.remove('active'));
            contentPanels.forEach(p => p.classList.remove('active'));
            
            // 添加当前活动状态
            this.classList.add('active');
            
            // 显示对应面板
            const tabId = this.getAttribute('data-tab');
            const panel = document.getElementById(tabId);
            panel.classList.add('active');
        });
    });
}

/**
 * 初始化表单验证
 */
function initFormValidation() {
    const form = document.getElementById('publishForm');
    const requiredFields = [
        { id: 'productTitle', message: '请输入商品标题' },
        { id: 'productDescription', message: '请输入商品描述' },
        { id: 'productPrice', message: '请输入商品价格' }
    ];
    
    // 添加输入事件监听
    requiredFields.forEach(field => {
        const element = document.getElementById(field.id);
        
        // 添加失焦验证
        element.addEventListener('blur', function() {
            validateField(element, field.message);
        });
        
        // 添加输入验证
        element.addEventListener('input', function() {
            // 如果已经显示了错误，则在输入时重新验证
            if (element.classList.contains('input-error')) {
                validateField(element, field.message);
            }
        });
    });
    
    // 验证单个字段
    function validateField(element, message) {
        // 移除现有错误提示
        const parent = element.parentElement;
        const existingError = parent.querySelector('.error-message');
        if (existingError) {
            parent.removeChild(existingError);
        }
        
        // 移除错误样式
        element.classList.remove('input-error');
        
        // 检查是否为空
        if (!element.value.trim()) {
            // 添加错误样式
            element.classList.add('input-error');
            
            // 创建错误提示
            const errorMessage = document.createElement('div');
            errorMessage.className = 'error-message';
            errorMessage.textContent = message;
            
            // 添加到父元素
            parent.appendChild(errorMessage);
            
            return false;
        }
        
        return true;
    }
    
    // 验证所有必填字段
    window.validateForm = function() {
        let isValid = true;
        
        requiredFields.forEach(field => {
            const element = document.getElementById(field.id);
            if (!validateField(element, field.message)) {
                isValid = false;
            }
        });
        
        // 验证虚拟商品分类
        if (document.getElementById('typeVirtual').checked) {
            const virtualCategory = document.getElementById('virtualCategory');
            if (!virtualCategory.value) {
                validateField(virtualCategory, '请选择虚拟商品分类');
                isValid = false;
            }
        }
        
        return isValid;
    };
}

/**
 * 初始化表单操作
 */
function initFormActions() {
    const saveDraftBtn = document.getElementById('saveDraft');
    const publishBtn = document.getElementById('publishBtn');
    
    // 保存草稿
    saveDraftBtn.addEventListener('click', function() {
        // 这里可以不进行严格验证，直接保存
        saveFormData('draft');
        showMessage('已保存为草稿', 'success');
    });
    
    // 发布按钮添加loading效果
    publishBtn.addEventListener('click', function() {
        if (window.validateForm()) {
            this.classList.add('btn-loading');
            this.innerHTML = '<i class="bi bi-arrow-repeat spin"></i> 发布中...';
            this.disabled = true;
        }
    });
}

/**
 * 处理表单提交
 */
function handleFormSubmit(e) {
    e.preventDefault();

    // 验证表单
    if (!window.validateForm()) {
        return;
    }

    // 显示加载状态
    const publishBtn = document.getElementById('publishBtn');
    publishBtn.classList.add('btn-loading');
    publishBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> 发布中...';
    publishBtn.disabled = true;

    // 创建FormData对象来处理文件上传
    const formData = new FormData(e.target);

    // 移除原有的images字段，重新添加
    formData.delete('images[]');

    // 添加图片文件
    const imageInputs = document.querySelectorAll('.preview-image input[type="file"]');
    imageInputs.forEach((input, index) => {
        if (input.files && input.files[0]) {
            formData.append('images[]', input.files[0]);
        }
    });

    // 发送到服务器
    fetch('publish.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(data => {
        // 检查响应中是否包含成功信息
        if (data.includes('商品发布成功') || data.includes('success')) {
            showMessage('商品发布成功！', 'success');
            // 延迟跳转到商品管理页面
            setTimeout(() => {
                window.location.href = 'my-products.php';
            }, 1500);
        } else {
            // 解析错误信息
            const parser = new DOMParser();
            const doc = parser.parseFromString(data, 'text/html');
            const errorElement = doc.querySelector('.alert-error');
            const errorMessage = errorElement ? errorElement.textContent.trim() : '发布失败，请重试';
            showMessage(errorMessage, 'error');
        }
    })
    .catch(error => {
        console.error('发布失败:', error);
        showMessage('网络错误，请重试', 'error');
    })
    .finally(() => {
        // 重置按钮状态
        publishBtn.classList.remove('btn-loading');
        publishBtn.innerHTML = '立即发布';
        publishBtn.disabled = false;
    });
}

/**
 * 保存表单数据
 */
function saveFormData(type) {
    // 收集表单数据
    const formData = {
        type: type, // draft 或 publish
        productType: document.querySelector('input[name="productType"]:checked').value,
        title: document.getElementById('productTitle').value,
        description: document.getElementById('productDescription').value,
        price: document.getElementById('productPrice').value,
        originalPrice: document.getElementById('originalPrice').value,
        inventory: document.getElementById('inventory').value,
        // 其他字段...
        timestamp: new Date().getTime()
    };
    
    // 如果是虚拟商品，收集相关字段
    if (formData.productType === 'virtual') {
        formData.virtualCategory = document.getElementById('virtualCategory').value;
        formData.usagePeriod = document.getElementById('usagePeriod').value;
        formData.platform = document.getElementById('platform').value;
        formData.deliveryMethod = document.getElementById('deliveryMethod').value;
        
        // 内容选项卡
        const activeTab = document.querySelector('.content-tab.active').getAttribute('data-tab');
        formData.contentType = activeTab;
        
        if (activeTab === 'contentText') {
            formData.textContent = document.getElementById('textContent').value;
        }
        
        // 售后服务
        formData.serviceRefund = document.getElementById('serviceRefund').checked;
        formData.serviceReplace = document.getElementById('serviceReplace').checked;
        formData.serviceConsult = document.getElementById('serviceConsult').checked;
        formData.serviceInvoice = document.getElementById('serviceInvoice').checked;
        formData.serviceDescription = document.getElementById('serviceDescription').value;
    }
    
    // 保存到localStorage（实际应用中应该发送到服务器）
    try {
        const savedItems = JSON.parse(localStorage.getItem('publishItems') || '[]');
        savedItems.push(formData);
        localStorage.setItem('publishItems', JSON.stringify(savedItems));
    } catch (e) {
        console.error('保存数据失败', e);
    }
    
    return formData;
}

/**
 * 显示消息提示
 */
function showMessage(message, type = 'info') {
    // 创建消息元素
    const messageElement = document.createElement('div');
    messageElement.className = `message-toast ${type}`;
    messageElement.innerHTML = `
        <div class="message-icon">
            <i class="bi ${getIconByType(type)}"></i>
        </div>
        <div class="message-content">${message}</div>
    `;
    
    // 添加到页面
    document.body.appendChild(messageElement);
    
    // 添加显示动画
    setTimeout(() => {
        messageElement.classList.add('show');
    }, 10);
    
    // 自动关闭
    setTimeout(() => {
        messageElement.classList.remove('show');
        messageElement.classList.add('hide');
        
        // 移除元素
        setTimeout(() => {
            document.body.removeChild(messageElement);
        }, 300);
    }, 3000);
    
    // 根据类型获取图标
    function getIconByType(type) {
        switch (type) {
            case 'success':
                return 'bi-check-circle-fill';
            case 'error':
                return 'bi-x-circle-fill';
            case 'warning':
                return 'bi-exclamation-triangle-fill';
            default:
                return 'bi-info-circle-fill';
        }
    }
}

// 添加CSS样式
function addStyles() {
    const style = document.createElement('style');
    style.textContent = `
        /* 动画类 */
        .animate-fade-in {
            animation: fadeIn 0.3s ease-out forwards;
        }
        
        .animate-fade-out {
            animation: fadeOut 0.3s ease-out forwards;
        }
        
        .animate-scale-in {
            animation: scaleIn 0.3s ease-out forwards;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }
        
        @keyframes scaleIn {
            from { opacity: 0; transform: scale(0.9); }
            to { opacity: 1; transform: scale(1); }
        }
        
        /* 图片预览 */
        .preview-image {
            position: relative;
        }
        
        .preview-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .preview-actions {
            position: absolute;
            top: 0;
            right: 0;
            display: flex;
            gap: 5px;
            padding: 5px;
        }
        
        .preview-remove {
            width: 24px;
            height: 24px;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .preview-remove:hover {
            background-color: rgba(255, 0, 0, 0.7);
            transform: scale(1.1);
        }
        
        /* 错误提示 */
        .input-error {
            border-color: var(--danger-color) !important;
            box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.1) !important;
        }
        
        .error-message {
            color: var(--danger-color);
            font-size: 13px;
            margin-top: 5px;
            animation: fadeIn 0.3s ease-out;
        }
        
        /* 按钮加载状态 */
        .btn-loading {
            position: relative;
            pointer-events: none;
        }
        
        .spin {
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        /* 消息提示 */
        .message-toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 12px;
            z-index: 1000;
            transform: translateY(-20px);
            opacity: 0;
            transition: all 0.3s ease;
        }
        
        .message-toast.show {
            transform: translateY(0);
            opacity: 1;
        }
        
        .message-toast.hide {
            transform: translateY(-20px);
            opacity: 0;
        }
        
        .message-icon {
            font-size: 20px;
        }
        
        .message-toast.success .message-icon {
            color: var(--success-color);
        }
        
        .message-toast.error .message-icon {
            color: var(--danger-color);
        }
        
        .message-toast.warning .message-icon {
            color: var(--warning-color);
        }
        
        .message-toast.info .message-icon {
            color: var(--info-color);
        }
        
        .message-content {
            font-size: 14px;
            color: var(--text-primary);
        }
    `;
    
    document.head.appendChild(style);
}

// 添加样式
addStyles();