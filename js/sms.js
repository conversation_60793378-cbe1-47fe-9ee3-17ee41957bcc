/**
 * 短信验证码功能 JavaScript 库
 * 提供发送验证码、验证码倒计时、验证等功能
 */

class SMSManager {
    constructor() {
        this.countdownTimers = new Map();
        this.init();
    }
    
    init() {
        // 绑定所有验证码按钮的事件
        document.querySelectorAll('.verify-code-btn, .get-code-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.handleSendCode(e));
        });
        
        // 绑定手机号输入框的验证事件
        document.querySelectorAll('input[type="tel"], input[name="phone"]').forEach(input => {
            input.addEventListener('input', (e) => this.validatePhone(e.target));
            input.addEventListener('blur', (e) => this.validatePhone(e.target));
        });
        
        // 绑定验证码输入框的验证事件
        document.querySelectorAll('input[name="verifyCode"], input[name="code"]').forEach(input => {
            input.addEventListener('input', (e) => this.validateCode(e.target));
        });
    }
    
    /**
     * 处理发送验证码按钮点击
     */
    async handleSendCode(event) {
        event.preventDefault();
        
        const button = event.target;
        const form = button.closest('form');
        const phoneInput = form.querySelector('input[type="tel"], input[name="phone"]');
        
        if (!phoneInput) {
            this.showMessage('找不到手机号输入框', 'error');
            return;
        }
        
        const phone = phoneInput.value.trim();
        
        // 验证手机号
        if (!this.isValidPhone(phone)) {
            this.showMessage('请输入正确的手机号码', 'error');
            phoneInput.focus();
            return;
        }
        
        // 获取验证码类型
        const type = button.dataset.type || form.dataset.type || 'login';
        
        // 发送验证码
        await this.sendCode(phone, type, button);
    }
    
    /**
     * 发送验证码
     */
    async sendCode(phone, type = 'login', button = null) {
        try {
            if (button) {
                button.disabled = true;
                button.textContent = '发送中...';
            }
            
            const formData = new FormData();
            formData.append('action', 'send');
            formData.append('phone', phone);
            formData.append('type', type);
            
            const response = await fetch('api/sms.php', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showMessage(data.message, 'success');
                
                // 开始倒计时
                if (button) {
                    this.startCountdown(button, data.countdown || 60);
                }
                
                return { success: true, data };
            } else {
                this.showMessage(data.message, 'error');
                
                if (button) {
                    button.disabled = false;
                    button.textContent = '获取验证码';
                }
                
                return { success: false, error: data.message };
            }
            
        } catch (error) {
            console.error('发送验证码失败:', error);
            this.showMessage('网络错误，请稍后重试', 'error');
            
            if (button) {
                button.disabled = false;
                button.textContent = '获取验证码';
            }
            
            return { success: false, error: error.message };
        }
    }
    
    /**
     * 验证验证码
     */
    async verifyCode(phone, code, type = 'login') {
        try {
            const formData = new FormData();
            formData.append('action', 'verify');
            formData.append('phone', phone);
            formData.append('code', code);
            formData.append('type', type);
            
            const response = await fetch('api/sms.php', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showMessage(data.message, 'success');
                return { success: true, data };
            } else {
                this.showMessage(data.message, 'error');
                return { success: false, error: data.message };
            }
            
        } catch (error) {
            console.error('验证码验证失败:', error);
            this.showMessage('网络错误，请稍后重试', 'error');
            return { success: false, error: error.message };
        }
    }
    
    /**
     * 检查手机号是否已注册
     */
    async checkPhone(phone) {
        try {
            const formData = new FormData();
            formData.append('action', 'check_phone');
            formData.append('phone', phone);
            
            const response = await fetch('api/sms.php', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            return data;
            
        } catch (error) {
            console.error('检查手机号失败:', error);
            return { success: false, error: error.message };
        }
    }
    
    /**
     * 开始倒计时
     */
    startCountdown(button, seconds) {
        const originalText = button.textContent;
        let countdown = seconds;
        
        // 清除之前的计时器
        if (this.countdownTimers.has(button)) {
            clearInterval(this.countdownTimers.get(button));
        }
        
        const updateButton = () => {
            if (countdown > 0) {
                button.disabled = true;
                button.textContent = `${countdown}秒后重新获取`;
                countdown--;
            } else {
                button.disabled = false;
                button.textContent = originalText || '获取验证码';
                clearInterval(timer);
                this.countdownTimers.delete(button);
            }
        };
        
        updateButton();
        const timer = setInterval(updateButton, 1000);
        this.countdownTimers.set(button, timer);
    }
    
    /**
     * 验证手机号格式
     */
    isValidPhone(phone) {
        return /^1[3-9]\d{9}$/.test(phone);
    }
    
    /**
     * 验证验证码格式
     */
    isValidCode(code) {
        return /^\d{6}$/.test(code);
    }
    
    /**
     * 验证手机号输入框
     */
    validatePhone(input) {
        const phone = input.value.trim();
        const isValid = this.isValidPhone(phone);
        
        this.toggleInputError(input, isValid, '请输入正确的11位手机号码');
        return isValid;
    }
    
    /**
     * 验证验证码输入框
     */
    validateCode(input) {
        const code = input.value.trim();
        const isValid = this.isValidCode(code);
        
        this.toggleInputError(input, isValid, '请输入6位数字验证码');
        return isValid;
    }
    
    /**
     * 切换输入框错误状态
     */
    toggleInputError(input, isValid, errorMessage) {
        const formGroup = input.closest('.form-group, .input-group');
        let errorElement = formGroup?.querySelector('.error-message');
        
        if (isValid) {
            input.classList.remove('error');
            if (errorElement) {
                errorElement.remove();
            }
        } else {
            input.classList.add('error');
            if (!errorElement && input.value.trim() !== '') {
                errorElement = document.createElement('div');
                errorElement.className = 'error-message';
                errorElement.textContent = errorMessage;
                formGroup.appendChild(errorElement);
            }
        }
    }
    
    /**
     * 显示消息提示
     */
    showMessage(message, type = 'info') {
        // 创建消息元素
        const messageElement = document.createElement('div');
        messageElement.className = `message-toast ${type}`;
        messageElement.innerHTML = `
            <div class="message-icon">
                <i class="bi ${this.getIconByType(type)}"></i>
            </div>
            <div class="message-content">${message}</div>
        `;
        
        // 添加到页面
        document.body.appendChild(messageElement);
        
        // 添加显示动画
        setTimeout(() => {
            messageElement.classList.add('show');
        }, 10);
        
        // 自动关闭
        setTimeout(() => {
            messageElement.classList.remove('show');
            messageElement.classList.add('hide');
            
            // 移除元素
            setTimeout(() => {
                if (messageElement.parentNode) {
                    messageElement.parentNode.removeChild(messageElement);
                }
            }, 300);
        }, 3000);
    }
    
    /**
     * 根据类型获取图标
     */
    getIconByType(type) {
        const icons = {
            'success': 'bi-check-circle',
            'error': 'bi-exclamation-circle',
            'warning': 'bi-exclamation-triangle',
            'info': 'bi-info-circle'
        };
        return icons[type] || icons.info;
    }
    
    /**
     * 清理所有计时器
     */
    destroy() {
        this.countdownTimers.forEach(timer => clearInterval(timer));
        this.countdownTimers.clear();
    }
}

// 全局实例
let smsManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    smsManager = new SMSManager();
});

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    if (smsManager) {
        smsManager.destroy();
    }
});

// 导出全局函数供其他脚本使用
window.SMSManager = SMSManager;
window.sendSMSCode = function(phone, type = 'login') {
    return smsManager ? smsManager.sendCode(phone, type) : Promise.reject('SMS Manager not initialized');
};
window.verifySMSCode = function(phone, code, type = 'login') {
    return smsManager ? smsManager.verifyCode(phone, code, type) : Promise.reject('SMS Manager not initialized');
};
