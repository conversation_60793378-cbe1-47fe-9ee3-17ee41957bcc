<?php
require_once 'includes/functions.php';

// 模拟登录（仅用于测试）
if (!isLoggedIn()) {
    // 获取第一个用户进行测试
    $stmt = $pdo->query("SELECT id, username FROM users LIMIT 1");
    $user = $stmt->fetch();
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
    }
}

// 获取一个测试商品
$stmt = $pdo->query("SELECT * FROM products WHERE status = 'active' LIMIT 1");
$testProduct = $stmt->fetch();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>购买功能调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .product-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .debug-info {
            background: #e2e3e5;
            color: #383d41;
            border: 1px solid #d6d8db;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>购买功能调试测试</h1>
        
        <div class="product-info">
            <h3>当前登录用户</h3>
            <p><strong>用户ID:</strong> <?php echo $_SESSION['user_id'] ?? '未登录'; ?></p>
            <p><strong>用户名:</strong> <?php echo $_SESSION['username'] ?? '未登录'; ?></p>
        </div>
        
        <?php if ($testProduct): ?>
        <div class="product-info">
            <h3>测试商品信息</h3>
            <p><strong>商品ID:</strong> <?php echo $testProduct['id']; ?></p>
            <p><strong>商品名称:</strong> <?php echo htmlspecialchars($testProduct['title']); ?></p>
            <p><strong>价格:</strong> ¥<?php echo number_format($testProduct['price'], 2); ?></p>
            <p><strong>库存:</strong> <?php echo $testProduct['stock']; ?></p>
            <p><strong>状态:</strong> <?php echo $testProduct['status']; ?></p>
            <p><strong>卖家ID:</strong> <?php echo $testProduct['user_id']; ?></p>
            <p><strong>是否虚拟商品:</strong> <?php echo isset($testProduct['is_virtual']) ? ($testProduct['is_virtual'] ? '是' : '否') : '未设置'; ?></p>
        </div>
        
        <div>
            <button class="btn" onclick="testPurchase(<?php echo $testProduct['id']; ?>)">测试购买</button>
            <button class="btn" onclick="checkDatabase()">检查数据库状态</button>
            <button class="btn" onclick="testAPI()">测试API连接</button>
        </div>
        
        <?php else: ?>
        <div class="result error" style="display: block;">
            <p>没有找到可测试的商品。请先添加一些商品。</p>
        </div>
        <?php endif; ?>
        
        <div id="result" class="result"></div>
    </div>

    <script>
    function showResult(message, type = 'success') {
        const resultDiv = document.getElementById('result');
        resultDiv.className = 'result ' + type;
        resultDiv.innerHTML = message;
        resultDiv.style.display = 'block';
    }

    function testPurchase(productId) {
        showResult('正在测试购买...', 'debug-info');
        
        const formData = new FormData();
        formData.append('product_id', productId);
        formData.append('quantity', 1);
        formData.append('action', 'buy_now');

        fetch('api/purchase.php', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);
            return response.text();
        })
        .then(text => {
            console.log('Raw response:', text);
            try {
                const data = JSON.parse(text);
                if (data.success) {
                    let message = `购买成功！<br>订单ID: ${data.order_id}<br>订单号: ${data.order_number || '未生成'}`;
                    showResult(message, 'success');
                } else {
                    let message = `购买失败: ${data.message}`;
                    if (data.debug) {
                        message += `<br><br>调试信息:<br>文件: ${data.debug.file}<br>行号: ${data.debug.line}`;
                    }
                    showResult(message, 'error');
                }
            } catch (e) {
                showResult(`JSON解析错误: ${e.message}<br><br>原始响应:<br>${text}`, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showResult(`网络错误: ${error.message}`, 'error');
        });
    }

    function checkDatabase() {
        fetch('debug-purchase.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let message = '<h4>数据库状态:</h4>';
                message += `<p>商品数量: ${data.product_count}</p>`;
                message += `<p>用户ID: ${data.session_user_id}</p>`;
                message += `<p>Virtual表存在: ${data.virtual_table_exists ? '是' : '否'}</p>`;
                message += '<p>所有表: ' + data.all_tables.join(', ') + '</p>';
                if (data.orders_structure) {
                    message += '<p>订单表结构: ' + data.orders_structure.join(', ') + '</p>';
                }
                showResult(message, 'success');
            } else {
                showResult(`数据库检查失败: ${data.error}`, 'error');
            }
        })
        .catch(error => {
            showResult(`检查失败: ${error.message}`, 'error');
        });
    }

    function testAPI() {
        fetch('api/purchase.php', {
            method: 'GET'
        })
        .then(response => response.text())
        .then(text => {
            try {
                const data = JSON.parse(text);
                showResult(`API响应: ${JSON.stringify(data, null, 2)}`, 'debug-info');
            } catch (e) {
                showResult(`API原始响应:<br>${text}`, 'debug-info');
            }
        })
        .catch(error => {
            showResult(`API测试失败: ${error.message}`, 'error');
        });
    }
    </script>
</body>
</html>
