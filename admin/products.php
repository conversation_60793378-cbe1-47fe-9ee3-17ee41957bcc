<?php
session_start();
require_once '../config/database.php';

// 检查管理员登录状态
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// 处理商品操作
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action == 'toggle_status') {
        $productId = intval($_POST['product_id']);
        $newStatus = $_POST['new_status'];
        
        try {
            $stmt = $pdo->prepare("UPDATE products SET status = ? WHERE id = ?");
            $stmt->execute([$newStatus, $productId]);
            $message = '商品状态更新成功';
        } catch (Exception $e) {
            $error = '操作失败：' . $e->getMessage();
        }
    }
    
    if ($action == 'delete_product') {
        $productId = intval($_POST['product_id']);
        
        try {
            $stmt = $pdo->prepare("DELETE FROM products WHERE id = ?");
            $stmt->execute([$productId]);
            $message = '商品删除成功';
        } catch (Exception $e) {
            $error = '删除失败：' . $e->getMessage();
        }
    }
}

// 获取商品列表
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;
$search = $_GET['search'] ?? '';
$status = $_GET['status'] ?? '';

$whereClause = 'WHERE 1=1';
$params = [];

if ($search) {
    $whereClause .= " AND (p.title LIKE ? OR p.description LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($status) {
    $whereClause .= " AND p.status = ?";
    $params[] = $status;
}

try {
    // 获取商品总数
    $countSql = "SELECT COUNT(*) FROM products p $whereClause";
    $stmt = $pdo->prepare($countSql);
    $stmt->execute($params);
    $totalProducts = $stmt->fetchColumn();
    
    // 获取商品列表
    $sql = "SELECT p.*, u.nickname as seller_name, c.name as category_name 
            FROM products p 
            JOIN users u ON p.user_id = u.id 
            JOIN categories c ON p.category_id = c.id 
            $whereClause 
            ORDER BY p.created_at DESC 
            LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $products = $stmt->fetchAll();
    
    $totalPages = ceil($totalProducts / $limit);
} catch (Exception $e) {
    $products = [];
    $totalProducts = 0;
    $totalPages = 0;
    $error = '获取商品列表失败：' . $e->getMessage();
}

function formatPrice($price) {
    return '¥' . number_format($price, 2);
}

$pageTitle = '商品管理';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - 数字鱼管理系统</title>
    <link rel="stylesheet" href="../css/bootstrap-icons.css">
    <link rel="stylesheet" href="css/admin.css">
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>数字鱼管理</h2>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="index.php"><i class="bi bi-speedometer2"></i><span>仪表盘</span></a></li>
                    <li><a href="users.php"><i class="bi bi-people"></i><span>用户管理</span></a></li>
                    <li class="active"><a href="products.php"><i class="bi bi-box-seam"></i><span>商品管理</span></a></li>
                    <li><a href="categories.php"><i class="bi bi-tags"></i><span>分类管理</span></a></li>
                    <li><a href="orders.php"><i class="bi bi-cart-check"></i><span>订单管理</span></a></li>
                    <li><a href="reports.php"><i class="bi bi-graph-up"></i><span>数据报表</span></a></li>
                    <li><a href="settings.php"><i class="bi bi-gear"></i><span>系统设置</span></a></li>
                    <li><a href="logout.php"><i class="bi bi-box-arrow-right"></i><span>退出登录</span></a></li>
                </ul>
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部栏 -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <h1>商品管理</h1>
                </div>
                <div class="top-bar-right">
                    <span class="admin-info">管理员：<?php echo htmlspecialchars($_SESSION['admin_username'] ?? 'Admin'); ?></span>
                </div>
            </header>

            <?php if ($message): ?>
                <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
            <?php endif; ?>

            <!-- 搜索和筛选栏 -->
            <div class="data-table">
                <div class="table-header">
                    <h3>商品列表 (共 <?php echo $totalProducts; ?> 个商品)</h3>
                    <div class="table-actions">
                        <form method="GET" style="display: inline-flex; gap: 0.5rem; align-items: center;">
                            <select name="status" style="padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
                                <option value="">全部状态</option>
                                <option value="active" <?php echo $status == 'active' ? 'selected' : ''; ?>>在售</option>
                                <option value="sold" <?php echo $status == 'sold' ? 'selected' : ''; ?>>已售出</option>
                                <option value="inactive" <?php echo $status == 'inactive' ? 'selected' : ''; ?>>已下架</option>
                            </select>
                            <input type="text" name="search" placeholder="搜索商品..." 
                                   value="<?php echo htmlspecialchars($search); ?>" 
                                   style="padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> 搜索
                            </button>
                        </form>
                    </div>
                </div>

                <!-- 商品表格 -->
                <div style="overflow-x: auto;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead style="background: #f8f9fa;">
                            <tr>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">ID</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">图片</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">标题</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">分类</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">卖家</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">价格</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">类型</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">状态</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">浏览/收藏</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">发布时间</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($products as $product): ?>
                                <?php 
                                $images = json_decode($product['images'], true);
                                $firstImage = $images ? $images[0] : '../images/product-default.svg';
                                ?>
                                <tr style="border-bottom: 1px solid #f1f3f4;">
                                    <td style="padding: 1rem;"><?php echo $product['id']; ?></td>
                                    <td style="padding: 1rem;">
                                        <img src="../<?php echo $firstImage; ?>" alt="商品图片" 
                                             style="width: 50px; height: 50px; border-radius: 8px; object-fit: cover;">
                                    </td>
                                    <td style="padding: 1rem; max-width: 200px;">
                                        <div style="font-weight: 500; margin-bottom: 0.25rem;">
                                            <?php echo htmlspecialchars(mb_substr($product['title'], 0, 30)); ?>
                                        </div>
                                        <div style="font-size: 0.875rem; color: #666;">
                                            <?php echo htmlspecialchars(mb_substr($product['description'], 0, 50)); ?>...
                                        </div>
                                    </td>
                                    <td style="padding: 1rem;"><?php echo htmlspecialchars($product['category_name']); ?></td>
                                    <td style="padding: 1rem;"><?php echo htmlspecialchars($product['seller_name']); ?></td>
                                    <td style="padding: 1rem;">
                                        <div style="font-weight: 500; color: #e74c3c;">
                                            <?php echo formatPrice($product['price']); ?>
                                        </div>
                                        <?php if ($product['original_price']): ?>
                                            <div style="font-size: 0.875rem; color: #999; text-decoration: line-through;">
                                                <?php echo formatPrice($product['original_price']); ?>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td style="padding: 1rem;">
                                        <span style="padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.875rem; 
                                                     background: <?php echo $product['is_virtual'] ? '#fff3cd' : '#d1ecf1'; ?>; 
                                                     color: <?php echo $product['is_virtual'] ? '#856404' : '#0c5460'; ?>;">
                                            <?php echo $product['is_virtual'] ? '虚拟商品' : '实物商品'; ?>
                                        </span>
                                    </td>
                                    <td style="padding: 1rem;">
                                        <span style="padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.875rem; 
                                                     background: <?php 
                                                         echo $product['status'] == 'active' ? '#d4edda' : 
                                                              ($product['status'] == 'sold' ? '#fff3cd' : '#f8d7da'); 
                                                     ?>; 
                                                     color: <?php 
                                                         echo $product['status'] == 'active' ? '#155724' : 
                                                              ($product['status'] == 'sold' ? '#856404' : '#721c24'); 
                                                     ?>;">
                                            <?php 
                                            echo $product['status'] == 'active' ? '在售' : 
                                                 ($product['status'] == 'sold' ? '已售出' : '已下架'); 
                                            ?>
                                        </span>
                                    </td>
                                    <td style="padding: 1rem;">
                                        <div style="font-size: 0.875rem;">
                                            <div><i class="bi bi-eye"></i> <?php echo $product['views']; ?></div>
                                            <div><i class="bi bi-heart"></i> <?php echo $product['likes']; ?></div>
                                        </div>
                                    </td>
                                    <td style="padding: 1rem; font-size: 0.875rem;">
                                        <?php echo date('Y-m-d H:i', strtotime($product['created_at'])); ?>
                                    </td>
                                    <td style="padding: 1rem;">
                                        <div style="display: flex; flex-direction: column; gap: 0.25rem;">
                                            <a href="../detail.php?id=<?php echo $product['id']; ?>" target="_blank" 
                                               class="btn btn-outline" style="font-size: 0.75rem; padding: 0.25rem 0.5rem; text-decoration: none;">
                                                查看
                                            </a>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="toggle_status">
                                                <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                                                <input type="hidden" name="new_status" value="<?php echo $product['status'] == 'active' ? 'inactive' : 'active'; ?>">
                                                <button type="submit" class="btn <?php echo $product['status'] == 'active' ? 'btn-outline' : 'btn-success'; ?>" 
                                                        style="font-size: 0.75rem; padding: 0.25rem 0.5rem; width: 100%;">
                                                    <?php echo $product['status'] == 'active' ? '下架' : '上架'; ?>
                                                </button>
                                            </form>
                                            <form method="POST" style="display: inline;" 
                                                  onsubmit="return confirm('确定要删除这个商品吗？')">
                                                <input type="hidden" name="action" value="delete_product">
                                                <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                                                <button type="submit" class="btn btn-danger" 
                                                        style="font-size: 0.75rem; padding: 0.25rem 0.5rem; width: 100%;">
                                                    删除
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                            
                            <?php if (empty($products)): ?>
                                <tr>
                                    <td colspan="11" style="padding: 2rem; text-align: center; color: #666;">
                                        没有找到商品
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <?php if ($totalPages > 1): ?>
                    <div style="padding: 1rem; border-top: 1px solid #e9ecef; display: flex; justify-content: center; gap: 0.5rem;">
                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                            <a href="?page=<?php echo $i; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?><?php echo $status ? '&status=' . urlencode($status) : ''; ?>" 
                               class="btn <?php echo $i == $page ? 'btn-primary' : 'btn-outline'; ?>"
                               style="padding: 0.5rem 0.75rem; text-decoration: none;">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <style>
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</body>
</html>
