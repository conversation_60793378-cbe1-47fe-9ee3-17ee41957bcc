<?php
session_start();
require_once '../config/database.php';

// 检查管理员登录状态
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// 处理用户操作
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action == 'toggle_status') {
        $userId = intval($_POST['user_id']);
        $newStatus = $_POST['new_status'] == '1' ? 1 : 0;
        
        try {
            $stmt = $pdo->prepare("UPDATE users SET is_verified = ? WHERE id = ?");
            $stmt->execute([$newStatus, $userId]);
            $message = '用户状态更新成功';
        } catch (Exception $e) {
            $error = '操作失败：' . $e->getMessage();
        }
    }
    
    if ($action == 'delete_user') {
        $userId = intval($_POST['user_id']);
        
        try {
            // 检查用户是否有相关数据
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE user_id = ?");
            $stmt->execute([$userId]);
            $productCount = $stmt->fetchColumn();
            
            if ($productCount > 0) {
                $error = '该用户还有商品，无法删除';
            } else {
                $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
                $stmt->execute([$userId]);
                $message = '用户删除成功';
            }
        } catch (Exception $e) {
            $error = '删除失败：' . $e->getMessage();
        }
    }
}

// 获取用户列表
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;
$search = $_GET['search'] ?? '';

$whereClause = '';
$params = [];

if ($search) {
    $whereClause = "WHERE username LIKE ? OR email LIKE ? OR nickname LIKE ?";
    $params = ["%$search%", "%$search%", "%$search%"];
}

try {
    // 获取用户总数
    $countSql = "SELECT COUNT(*) FROM users $whereClause";
    $stmt = $pdo->prepare($countSql);
    $stmt->execute($params);
    $totalUsers = $stmt->fetchColumn();
    
    // 获取用户列表
    $sql = "SELECT * FROM users $whereClause ORDER BY created_at DESC LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $users = $stmt->fetchAll();
    
    $totalPages = ceil($totalUsers / $limit);
} catch (Exception $e) {
    $users = [];
    $totalUsers = 0;
    $totalPages = 0;
    $error = '获取用户列表失败：' . $e->getMessage();
}

$pageTitle = '用户管理';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - 数字鱼管理系统</title>
    <link rel="stylesheet" href="../css/bootstrap-icons.css">
    <link rel="stylesheet" href="css/admin.css">
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>数字鱼管理</h2>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="index.php"><i class="bi bi-speedometer2"></i><span>仪表盘</span></a></li>
                    <li class="active"><a href="users.php"><i class="bi bi-people"></i><span>用户管理</span></a></li>
                    <li><a href="products.php"><i class="bi bi-box-seam"></i><span>商品管理</span></a></li>
                    <li><a href="categories.php"><i class="bi bi-tags"></i><span>分类管理</span></a></li>
                    <li><a href="orders.php"><i class="bi bi-cart-check"></i><span>订单管理</span></a></li>
                    <li><a href="reports.php"><i class="bi bi-graph-up"></i><span>数据报表</span></a></li>
                    <li><a href="settings.php"><i class="bi bi-gear"></i><span>系统设置</span></a></li>
                    <li><a href="logout.php"><i class="bi bi-box-arrow-right"></i><span>退出登录</span></a></li>
                </ul>
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部栏 -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <h1>用户管理</h1>
                </div>
                <div class="top-bar-right">
                    <span class="admin-info">管理员：<?php echo htmlspecialchars($_SESSION['admin_username'] ?? 'Admin'); ?></span>
                </div>
            </header>

            <?php if ($message): ?>
                <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
            <?php endif; ?>

            <!-- 搜索和操作栏 -->
            <div class="data-table">
                <div class="table-header">
                    <h3>用户列表 (共 <?php echo $totalUsers; ?> 个用户)</h3>
                    <div class="table-actions">
                        <form method="GET" style="display: inline-flex; gap: 0.5rem;">
                            <input type="text" name="search" placeholder="搜索用户..." 
                                   value="<?php echo htmlspecialchars($search); ?>" 
                                   style="padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> 搜索
                            </button>
                        </form>
                    </div>
                </div>

                <!-- 用户表格 -->
                <div style="overflow-x: auto;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead style="background: #f8f9fa;">
                            <tr>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">ID</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">头像</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">用户名</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">昵称</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">邮箱</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">手机</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">状态</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">注册时间</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                                <tr style="border-bottom: 1px solid #f1f3f4;">
                                    <td style="padding: 1rem;"><?php echo $user['id']; ?></td>
                                    <td style="padding: 1rem;">
                                        <img src="../<?php echo $user['avatar']; ?>" alt="头像" 
                                             style="width: 40px; height: 40px; border-radius: 50%; object-fit: cover;">
                                    </td>
                                    <td style="padding: 1rem;"><?php echo htmlspecialchars($user['username']); ?></td>
                                    <td style="padding: 1rem;"><?php echo htmlspecialchars($user['nickname']); ?></td>
                                    <td style="padding: 1rem;"><?php echo htmlspecialchars($user['email']); ?></td>
                                    <td style="padding: 1rem;"><?php echo htmlspecialchars($user['phone']); ?></td>
                                    <td style="padding: 1rem;">
                                        <span style="padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.875rem; 
                                                     background: <?php echo $user['is_verified'] ? '#d4edda' : '#f8d7da'; ?>; 
                                                     color: <?php echo $user['is_verified'] ? '#155724' : '#721c24'; ?>;">
                                            <?php echo $user['is_verified'] ? '已认证' : '未认证'; ?>
                                        </span>
                                    </td>
                                    <td style="padding: 1rem;"><?php echo date('Y-m-d H:i', strtotime($user['created_at'])); ?></td>
                                    <td style="padding: 1rem;">
                                        <div style="display: flex; gap: 0.5rem;">
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="toggle_status">
                                                <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                <input type="hidden" name="new_status" value="<?php echo $user['is_verified'] ? '0' : '1'; ?>">
                                                <button type="submit" class="btn <?php echo $user['is_verified'] ? 'btn-outline' : 'btn-success'; ?>" 
                                                        style="font-size: 0.75rem; padding: 0.25rem 0.5rem;">
                                                    <?php echo $user['is_verified'] ? '取消认证' : '认证'; ?>
                                                </button>
                                            </form>
                                            <form method="POST" style="display: inline;" 
                                                  onsubmit="return confirm('确定要删除这个用户吗？')">
                                                <input type="hidden" name="action" value="delete_user">
                                                <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                <button type="submit" class="btn btn-danger" 
                                                        style="font-size: 0.75rem; padding: 0.25rem 0.5rem;">
                                                    删除
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                            
                            <?php if (empty($users)): ?>
                                <tr>
                                    <td colspan="9" style="padding: 2rem; text-align: center; color: #666;">
                                        没有找到用户
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <?php if ($totalPages > 1): ?>
                    <div style="padding: 1rem; border-top: 1px solid #e9ecef; display: flex; justify-content: center; gap: 0.5rem;">
                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                            <a href="?page=<?php echo $i; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>" 
                               class="btn <?php echo $i == $page ? 'btn-primary' : 'btn-outline'; ?>"
                               style="padding: 0.5rem 0.75rem; text-decoration: none;">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <style>
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</body>
</html>
