<?php
session_start();
require_once '../config/database.php';

// 检查管理员登录状态
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// 获取统计数据
try {
    // 用户统计
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM users");
    $userStats['total'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as today FROM users WHERE DATE(created_at) = CURDATE()");
    $userStats['today'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as this_week FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
    $userStats['this_week'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as this_month FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)");
    $userStats['this_month'] = $stmt->fetchColumn();
    
    // 商品统计
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM products");
    $productStats['total'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as active FROM products WHERE status = 'active'");
    $productStats['active'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as virtual FROM products WHERE is_virtual = 1");
    $productStats['virtual'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as today FROM products WHERE DATE(created_at) = CURDATE()");
    $productStats['today'] = $stmt->fetchColumn();
    
    // 分类统计
    $stmt = $pdo->query("
        SELECT c.name, COUNT(p.id) as product_count 
        FROM categories c 
        LEFT JOIN products p ON c.id = p.category_id 
        GROUP BY c.id, c.name 
        ORDER BY product_count DESC
    ");
    $categoryStats = $stmt->fetchAll();
    
    // 最近7天用户注册趋势
    $stmt = $pdo->query("
        SELECT DATE(created_at) as date, COUNT(*) as count 
        FROM users 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) 
        GROUP BY DATE(created_at) 
        ORDER BY date
    ");
    $userTrend = $stmt->fetchAll();
    
    // 最近7天商品发布趋势
    $stmt = $pdo->query("
        SELECT DATE(created_at) as date, COUNT(*) as count 
        FROM products 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) 
        GROUP BY DATE(created_at) 
        ORDER BY date
    ");
    $productTrend = $stmt->fetchAll();
    
    // 热门商品
    $stmt = $pdo->query("
        SELECT p.title, p.views, p.likes, u.nickname as seller_name 
        FROM products p 
        JOIN users u ON p.user_id = u.id 
        WHERE p.status = 'active' 
        ORDER BY p.views DESC 
        LIMIT 10
    ");
    $hotProducts = $stmt->fetchAll();
    
} catch (Exception $e) {
    $error = '获取统计数据失败：' . $e->getMessage();
}

$pageTitle = '数据报表';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - 数字鱼管理系统</title>
    <link rel="stylesheet" href="../css/bootstrap-icons.css">
    <link rel="stylesheet" href="css/admin.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>数字鱼管理</h2>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="index.php"><i class="bi bi-speedometer2"></i><span>仪表盘</span></a></li>
                    <li><a href="users.php"><i class="bi bi-people"></i><span>用户管理</span></a></li>
                    <li><a href="products.php"><i class="bi bi-box-seam"></i><span>商品管理</span></a></li>
                    <li><a href="categories.php"><i class="bi bi-tags"></i><span>分类管理</span></a></li>
                    <li><a href="orders.php"><i class="bi bi-cart-check"></i><span>订单管理</span></a></li>
                    <li class="active"><a href="reports.php"><i class="bi bi-graph-up"></i><span>数据报表</span></a></li>
                    <li><a href="settings.php"><i class="bi bi-gear"></i><span>系统设置</span></a></li>
                    <li><a href="logout.php"><i class="bi bi-box-arrow-right"></i><span>退出登录</span></a></li>
                </ul>
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部栏 -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <h1>数据报表</h1>
                </div>
                <div class="top-bar-right">
                    <span class="admin-info">管理员：<?php echo htmlspecialchars($_SESSION['admin_username'] ?? 'Admin'); ?></span>
                </div>
            </header>

            <!-- 统计概览 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon users">
                        <i class="bi bi-people"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo number_format($userStats['total']); ?></h3>
                        <p>总用户数</p>
                        <span class="stat-change">本月新增: <?php echo $userStats['this_month']; ?></span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon products">
                        <i class="bi bi-box-seam"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo number_format($productStats['total']); ?></h3>
                        <p>总商品数</p>
                        <span class="stat-change">在售: <?php echo $productStats['active']; ?></span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon virtual">
                        <i class="bi bi-lightning-charge"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo number_format($productStats['virtual']); ?></h3>
                        <p>虚拟商品</p>
                        <span class="stat-change">占比: <?php echo $productStats['total'] > 0 ? round($productStats['virtual'] / $productStats['total'] * 100, 1) : 0; ?>%</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon orders">
                        <i class="bi bi-graph-up"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo $userStats['today'] + $productStats['today']; ?></h3>
                        <p>今日活跃</p>
                        <span class="stat-change">用户+商品</span>
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
                <!-- 用户注册趋势 -->
                <div style="background: white; border-radius: 12px; padding: 1.5rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <h3 style="margin-bottom: 1rem; color: #2c3e50;">用户注册趋势（最近7天）</h3>
                    <canvas id="userTrendChart" width="400" height="200"></canvas>
                </div>

                <!-- 商品发布趋势 -->
                <div style="background: white; border-radius: 12px; padding: 1.5rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <h3 style="margin-bottom: 1rem; color: #2c3e50;">商品发布趋势（最近7天）</h3>
                    <canvas id="productTrendChart" width="400" height="200"></canvas>
                </div>
            </div>

            <!-- 分类统计和热门商品 -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                <!-- 分类统计 -->
                <div style="background: white; border-radius: 12px; padding: 1.5rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <h3 style="margin-bottom: 1rem; color: #2c3e50;">商品分类统计</h3>
                    <div style="max-height: 300px; overflow-y: auto;">
                        <?php foreach ($categoryStats as $category): ?>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.5rem 0; border-bottom: 1px solid #f1f3f4;">
                                <span><?php echo htmlspecialchars($category['name']); ?></span>
                                <span style="background: #3498db; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.875rem;">
                                    <?php echo $category['product_count']; ?>
                                </span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- 热门商品 -->
                <div style="background: white; border-radius: 12px; padding: 1.5rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <h3 style="margin-bottom: 1rem; color: #2c3e50;">热门商品 TOP 10</h3>
                    <div style="max-height: 300px; overflow-y: auto;">
                        <?php foreach ($hotProducts as $index => $product): ?>
                            <div style="display: flex; align-items: center; padding: 0.5rem 0; border-bottom: 1px solid #f1f3f4;">
                                <span style="width: 24px; height: 24px; background: #e74c3c; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 0.875rem; margin-right: 0.75rem;">
                                    <?php echo $index + 1; ?>
                                </span>
                                <div style="flex: 1;">
                                    <div style="font-weight: 500; margin-bottom: 0.25rem;">
                                        <?php echo htmlspecialchars(mb_substr($product['title'], 0, 30)); ?>
                                    </div>
                                    <div style="font-size: 0.875rem; color: #666;">
                                        卖家: <?php echo htmlspecialchars($product['seller_name']); ?>
                                    </div>
                                </div>
                                <div style="text-align: right; font-size: 0.875rem; color: #666;">
                                    <div><i class="bi bi-eye"></i> <?php echo $product['views']; ?></div>
                                    <div><i class="bi bi-heart"></i> <?php echo $product['likes']; ?></div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 用户注册趋势图表
        const userTrendCtx = document.getElementById('userTrendChart').getContext('2d');
        const userTrendChart = new Chart(userTrendCtx, {
            type: 'line',
            data: {
                labels: [<?php 
                    $dates = [];
                    for ($i = 6; $i >= 0; $i--) {
                        $dates[] = "'" . date('m-d', strtotime("-$i days")) . "'";
                    }
                    echo implode(',', $dates);
                ?>],
                datasets: [{
                    label: '新注册用户',
                    data: [<?php 
                        $data = [];
                        for ($i = 6; $i >= 0; $i--) {
                            $date = date('Y-m-d', strtotime("-$i days"));
                            $count = 0;
                            foreach ($userTrend as $trend) {
                                if ($trend['date'] == $date) {
                                    $count = $trend['count'];
                                    break;
                                }
                            }
                            $data[] = $count;
                        }
                        echo implode(',', $data);
                    ?>],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // 商品发布趋势图表
        const productTrendCtx = document.getElementById('productTrendChart').getContext('2d');
        const productTrendChart = new Chart(productTrendCtx, {
            type: 'line',
            data: {
                labels: [<?php echo implode(',', $dates); ?>],
                datasets: [{
                    label: '新发布商品',
                    data: [<?php 
                        $data = [];
                        for ($i = 6; $i >= 0; $i--) {
                            $date = date('Y-m-d', strtotime("-$i days"));
                            $count = 0;
                            foreach ($productTrend as $trend) {
                                if ($trend['date'] == $date) {
                                    $count = $trend['count'];
                                    break;
                                }
                            }
                            $data[] = $count;
                        }
                        echo implode(',', $data);
                    ?>],
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>
</body>
</html>
