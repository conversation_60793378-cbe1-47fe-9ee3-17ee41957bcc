<?php
session_start();
require_once '../config/database.php';

// 检查管理员登录状态
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$message = '';
$error = '';

// 处理订单操作
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action == 'update_status') {
        $orderId = intval($_POST['order_id']);
        $newStatus = $_POST['new_status'];
        
        try {
            $stmt = $pdo->prepare("UPDATE orders SET status = ? WHERE id = ?");
            $stmt->execute([$newStatus, $orderId]);
            $message = '订单状态更新成功';
        } catch (Exception $e) {
            $error = '操作失败：' . $e->getMessage();
        }
    }
}

// 获取订单列表
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;
$status = $_GET['status'] ?? '';
$search = $_GET['search'] ?? '';

$whereClause = 'WHERE 1=1';
$params = [];

if ($status) {
    $whereClause .= " AND o.status = ?";
    $params[] = $status;
}

if ($search) {
    $whereClause .= " AND (p.title LIKE ? OR buyer.nickname LIKE ? OR seller.nickname LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

try {
    // 获取订单总数
    $countSql = "SELECT COUNT(*) FROM orders o 
                 JOIN products p ON o.product_id = p.id 
                 JOIN users buyer ON o.buyer_id = buyer.id 
                 JOIN users seller ON o.seller_id = seller.id 
                 $whereClause";
    $stmt = $pdo->prepare($countSql);
    $stmt->execute($params);
    $totalOrders = $stmt->fetchColumn();
    
    // 获取订单列表
    $sql = "SELECT o.*, p.title as product_title, p.price as product_price, p.is_virtual,
                   buyer.nickname as buyer_name, seller.nickname as seller_name
            FROM orders o 
            JOIN products p ON o.product_id = p.id 
            JOIN users buyer ON o.buyer_id = buyer.id 
            JOIN users seller ON o.seller_id = seller.id 
            $whereClause 
            ORDER BY o.created_at DESC 
            LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $orders = $stmt->fetchAll();
    
    $totalPages = ceil($totalOrders / $limit);
} catch (Exception $e) {
    $orders = [];
    $totalOrders = 0;
    $totalPages = 0;
    $error = '获取订单列表失败：' . $e->getMessage();
}

function formatPrice($price) {
    return '¥' . number_format($price, 2);
}

function getStatusText($status) {
    $statusMap = [
        'pending' => '待付款',
        'paid' => '已付款',
        'shipped' => '已发货',
        'completed' => '已完成',
        'cancelled' => '已取消'
    ];
    return $statusMap[$status] ?? $status;
}

function getStatusColor($status) {
    $colorMap = [
        'pending' => '#f39c12',
        'paid' => '#3498db',
        'shipped' => '#9b59b6',
        'completed' => '#27ae60',
        'cancelled' => '#e74c3c'
    ];
    return $colorMap[$status] ?? '#666';
}

$pageTitle = '订单管理';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - 数字鱼管理系统</title>
    <link rel="stylesheet" href="../css/bootstrap-icons.css">
    <link rel="stylesheet" href="css/admin.css">
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>数字鱼管理</h2>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="index.php"><i class="bi bi-speedometer2"></i><span>仪表盘</span></a></li>
                    <li><a href="users.php"><i class="bi bi-people"></i><span>用户管理</span></a></li>
                    <li><a href="products.php"><i class="bi bi-box-seam"></i><span>商品管理</span></a></li>
                    <li><a href="categories.php"><i class="bi bi-tags"></i><span>分类管理</span></a></li>
                    <li class="active"><a href="orders.php"><i class="bi bi-cart-check"></i><span>订单管理</span></a></li>
                    <li><a href="reports.php"><i class="bi bi-graph-up"></i><span>数据报表</span></a></li>
                    <li><a href="settings.php"><i class="bi bi-gear"></i><span>系统设置</span></a></li>
                    <li><a href="logout.php"><i class="bi bi-box-arrow-right"></i><span>退出登录</span></a></li>
                </ul>
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部栏 -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <h1>订单管理</h1>
                </div>
                <div class="top-bar-right">
                    <span class="admin-info">管理员：<?php echo htmlspecialchars($_SESSION['admin_username'] ?? 'Admin'); ?></span>
                </div>
            </header>

            <?php if ($message): ?>
                <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
            <?php endif; ?>

            <!-- 搜索和筛选栏 -->
            <div class="data-table">
                <div class="table-header">
                    <h3>订单列表 (共 <?php echo $totalOrders; ?> 个订单)</h3>
                    <div class="table-actions">
                        <form method="GET" style="display: inline-flex; gap: 0.5rem; align-items: center;">
                            <select name="status" style="padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
                                <option value="">全部状态</option>
                                <option value="pending" <?php echo $status == 'pending' ? 'selected' : ''; ?>>待付款</option>
                                <option value="paid" <?php echo $status == 'paid' ? 'selected' : ''; ?>>已付款</option>
                                <option value="shipped" <?php echo $status == 'shipped' ? 'selected' : ''; ?>>已发货</option>
                                <option value="completed" <?php echo $status == 'completed' ? 'selected' : ''; ?>>已完成</option>
                                <option value="cancelled" <?php echo $status == 'cancelled' ? 'selected' : ''; ?>>已取消</option>
                            </select>
                            <input type="text" name="search" placeholder="搜索订单..." 
                                   value="<?php echo htmlspecialchars($search); ?>" 
                                   style="padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> 搜索
                            </button>
                        </form>
                    </div>
                </div>

                <!-- 订单表格 -->
                <div style="overflow-x: auto;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead style="background: #f8f9fa;">
                            <tr>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">订单号</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">商品</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">买家</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">卖家</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">数量</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">总价</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">类型</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">状态</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">创建时间</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($orders as $order): ?>
                                <tr style="border-bottom: 1px solid #f1f3f4;">
                                    <td style="padding: 1rem; font-family: monospace; color: #666;">
                                        #<?php echo str_pad($order['id'], 6, '0', STR_PAD_LEFT); ?>
                                    </td>
                                    <td style="padding: 1rem; max-width: 200px;">
                                        <div style="font-weight: 500; margin-bottom: 0.25rem;">
                                            <?php echo htmlspecialchars(mb_substr($order['product_title'], 0, 30)); ?>
                                        </div>
                                        <div style="font-size: 0.875rem; color: #666;">
                                            单价: <?php echo formatPrice($order['product_price']); ?>
                                        </div>
                                    </td>
                                    <td style="padding: 1rem;"><?php echo htmlspecialchars($order['buyer_name']); ?></td>
                                    <td style="padding: 1rem;"><?php echo htmlspecialchars($order['seller_name']); ?></td>
                                    <td style="padding: 1rem;"><?php echo $order['quantity']; ?></td>
                                    <td style="padding: 1rem;">
                                        <div style="font-weight: 500; color: #e74c3c;">
                                            <?php echo formatPrice($order['total_price']); ?>
                                        </div>
                                    </td>
                                    <td style="padding: 1rem;">
                                        <span style="padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.875rem; 
                                                     background: <?php echo $order['is_virtual'] ? '#fff3cd' : '#d1ecf1'; ?>; 
                                                     color: <?php echo $order['is_virtual'] ? '#856404' : '#0c5460'; ?>;">
                                            <?php echo $order['is_virtual'] ? '虚拟商品' : '实物商品'; ?>
                                        </span>
                                    </td>
                                    <td style="padding: 1rem;">
                                        <span style="padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.875rem; 
                                                     background: rgba(<?php 
                                                         $color = getStatusColor($order['status']);
                                                         echo hexdec(substr($color, 1, 2)) . ',' . 
                                                              hexdec(substr($color, 3, 2)) . ',' . 
                                                              hexdec(substr($color, 5, 2));
                                                     ?>, 0.1); 
                                                     color: <?php echo getStatusColor($order['status']); ?>;">
                                            <?php echo getStatusText($order['status']); ?>
                                        </span>
                                    </td>
                                    <td style="padding: 1rem; font-size: 0.875rem;">
                                        <?php echo date('Y-m-d H:i', strtotime($order['created_at'])); ?>
                                    </td>
                                    <td style="padding: 1rem;">
                                        <div style="display: flex; flex-direction: column; gap: 0.25rem;">
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="update_status">
                                                <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                                <select name="new_status" onchange="this.form.submit()" 
                                                        style="padding: 0.25rem; border: 1px solid #ddd; border-radius: 4px; font-size: 0.75rem;">
                                                    <option value="pending" <?php echo $order['status'] == 'pending' ? 'selected' : ''; ?>>待付款</option>
                                                    <option value="paid" <?php echo $order['status'] == 'paid' ? 'selected' : ''; ?>>已付款</option>
                                                    <option value="shipped" <?php echo $order['status'] == 'shipped' ? 'selected' : ''; ?>>已发货</option>
                                                    <option value="completed" <?php echo $order['status'] == 'completed' ? 'selected' : ''; ?>>已完成</option>
                                                    <option value="cancelled" <?php echo $order['status'] == 'cancelled' ? 'selected' : ''; ?>>已取消</option>
                                                </select>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                            
                            <?php if (empty($orders)): ?>
                                <tr>
                                    <td colspan="10" style="padding: 2rem; text-align: center; color: #666;">
                                        没有找到订单
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <?php if ($totalPages > 1): ?>
                    <div style="padding: 1rem; border-top: 1px solid #e9ecef; display: flex; justify-content: center; gap: 0.5rem;">
                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                            <a href="?page=<?php echo $i; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?><?php echo $status ? '&status=' . urlencode($status) : ''; ?>" 
                               class="btn <?php echo $i == $page ? 'btn-primary' : 'btn-outline'; ?>"
                               style="padding: 0.5rem 0.75rem; text-decoration: none;">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <style>
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</body>
</html>
