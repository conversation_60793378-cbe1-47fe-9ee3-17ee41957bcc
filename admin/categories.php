<?php
session_start();
require_once '../config/database.php';

// 检查管理员登录状态
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// 处理分类操作
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action == 'add_category') {
        $name = trim($_POST['name']);
        $slug = trim($_POST['slug']);
        $description = trim($_POST['description']);
        $isVirtual = isset($_POST['is_virtual']) ? 1 : 0;
        $sortOrder = intval($_POST['sort_order']);
        
        if (empty($name) || empty($slug)) {
            $error = '分类名称和标识不能为空';
        } else {
            try {
                // 检查标识是否已存在
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM categories WHERE slug = ?");
                $stmt->execute([$slug]);
                
                if ($stmt->fetchColumn() > 0) {
                    $error = '分类标识已存在';
                } else {
                    $stmt = $pdo->prepare("INSERT INTO categories (name, slug, description, is_virtual, sort_order) VALUES (?, ?, ?, ?, ?)");
                    $stmt->execute([$name, $slug, $description, $isVirtual, $sortOrder]);
                    $message = '分类添加成功';
                }
            } catch (Exception $e) {
                $error = '添加失败：' . $e->getMessage();
            }
        }
    }
    
    if ($action == 'edit_category') {
        $id = intval($_POST['id']);
        $name = trim($_POST['name']);
        $slug = trim($_POST['slug']);
        $description = trim($_POST['description']);
        $isVirtual = isset($_POST['is_virtual']) ? 1 : 0;
        $sortOrder = intval($_POST['sort_order']);
        
        if (empty($name) || empty($slug)) {
            $error = '分类名称和标识不能为空';
        } else {
            try {
                // 检查标识是否已存在（排除当前分类）
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM categories WHERE slug = ? AND id != ?");
                $stmt->execute([$slug, $id]);
                
                if ($stmt->fetchColumn() > 0) {
                    $error = '分类标识已存在';
                } else {
                    $stmt = $pdo->prepare("UPDATE categories SET name = ?, slug = ?, description = ?, is_virtual = ?, sort_order = ? WHERE id = ?");
                    $stmt->execute([$name, $slug, $description, $isVirtual, $sortOrder, $id]);
                    $message = '分类更新成功';
                }
            } catch (Exception $e) {
                $error = '更新失败：' . $e->getMessage();
            }
        }
    }
    
    if ($action == 'delete_category') {
        $id = intval($_POST['id']);
        
        try {
            // 检查是否有商品使用此分类
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE category_id = ?");
            $stmt->execute([$id]);
            
            if ($stmt->fetchColumn() > 0) {
                $error = '该分类下还有商品，无法删除';
            } else {
                $stmt = $pdo->prepare("DELETE FROM categories WHERE id = ?");
                $stmt->execute([$id]);
                $message = '分类删除成功';
            }
        } catch (Exception $e) {
            $error = '删除失败：' . $e->getMessage();
        }
    }
}

// 获取分类列表
try {
    $stmt = $pdo->query("
        SELECT c.*, COUNT(p.id) as product_count 
        FROM categories c 
        LEFT JOIN products p ON c.id = p.category_id 
        GROUP BY c.id 
        ORDER BY c.sort_order, c.id
    ");
    $categories = $stmt->fetchAll();
} catch (Exception $e) {
    $categories = [];
    $error = '获取分类列表失败：' . $e->getMessage();
}

$pageTitle = '分类管理';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - 数字鱼管理系统</title>
    <link rel="stylesheet" href="../css/bootstrap-icons.css">
    <link rel="stylesheet" href="css/admin.css">
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>数字鱼管理</h2>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="index.php"><i class="bi bi-speedometer2"></i><span>仪表盘</span></a></li>
                    <li><a href="users.php"><i class="bi bi-people"></i><span>用户管理</span></a></li>
                    <li><a href="products.php"><i class="bi bi-box-seam"></i><span>商品管理</span></a></li>
                    <li class="active"><a href="categories.php"><i class="bi bi-tags"></i><span>分类管理</span></a></li>
                    <li><a href="orders.php"><i class="bi bi-cart-check"></i><span>订单管理</span></a></li>
                    <li><a href="reports.php"><i class="bi bi-graph-up"></i><span>数据报表</span></a></li>
                    <li><a href="settings.php"><i class="bi bi-gear"></i><span>系统设置</span></a></li>
                    <li><a href="logout.php"><i class="bi bi-box-arrow-right"></i><span>退出登录</span></a></li>
                </ul>
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部栏 -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <h1>分类管理</h1>
                </div>
                <div class="top-bar-right">
                    <span class="admin-info">管理员：<?php echo htmlspecialchars($_SESSION['admin_username'] ?? 'Admin'); ?></span>
                </div>
            </header>

            <?php if ($message): ?>
                <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
            <?php endif; ?>

            <!-- 添加分类表单 -->
            <div style="background: white; border-radius: 12px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <h3 style="margin-bottom: 1rem; color: #2c3e50;">添加新分类</h3>
                <form method="POST" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; align-items: end;">
                    <input type="hidden" name="action" value="add_category">
                    <div>
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">分类名称</label>
                        <input type="text" name="name" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">分类标识</label>
                        <input type="text" name="slug" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;" placeholder="英文标识">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">排序</label>
                        <input type="number" name="sort_order" value="0" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">描述</label>
                        <input type="text" name="description" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div>
                        <label style="display: flex; align-items: center; gap: 0.5rem;">
                            <input type="checkbox" name="is_virtual" value="1">
                            <span>虚拟商品分类</span>
                        </label>
                    </div>
                    <div>
                        <button type="submit" class="btn btn-primary">添加分类</button>
                    </div>
                </form>
            </div>

            <!-- 分类列表 -->
            <div class="data-table">
                <div class="table-header">
                    <h3>分类列表 (共 <?php echo count($categories); ?> 个分类)</h3>
                </div>

                <div style="overflow-x: auto;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead style="background: #f8f9fa;">
                            <tr>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">ID</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">分类名称</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">标识</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">描述</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">类型</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">商品数量</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">排序</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">创建时间</th>
                                <th style="padding: 1rem; text-align: left; border-bottom: 1px solid #e9ecef;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($categories as $category): ?>
                                <tr style="border-bottom: 1px solid #f1f3f4;">
                                    <td style="padding: 1rem;"><?php echo $category['id']; ?></td>
                                    <td style="padding: 1rem; font-weight: 500;"><?php echo htmlspecialchars($category['name']); ?></td>
                                    <td style="padding: 1rem; font-family: monospace; color: #666;"><?php echo htmlspecialchars($category['slug']); ?></td>
                                    <td style="padding: 1rem; color: #666;"><?php echo htmlspecialchars($category['description']); ?></td>
                                    <td style="padding: 1rem;">
                                        <span style="padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.875rem; 
                                                     background: <?php echo $category['is_virtual'] ? '#fff3cd' : '#d1ecf1'; ?>; 
                                                     color: <?php echo $category['is_virtual'] ? '#856404' : '#0c5460'; ?>;">
                                            <?php echo $category['is_virtual'] ? '虚拟商品' : '实物商品'; ?>
                                        </span>
                                    </td>
                                    <td style="padding: 1rem;">
                                        <span style="background: #3498db; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.875rem;">
                                            <?php echo $category['product_count']; ?>
                                        </span>
                                    </td>
                                    <td style="padding: 1rem;"><?php echo $category['sort_order']; ?></td>
                                    <td style="padding: 1rem; font-size: 0.875rem;"><?php echo date('Y-m-d', strtotime($category['created_at'])); ?></td>
                                    <td style="padding: 1rem;">
                                        <div style="display: flex; gap: 0.5rem;">
                                            <button onclick="editCategory(<?php echo htmlspecialchars(json_encode($category)); ?>)" 
                                                    class="btn btn-outline" style="font-size: 0.75rem; padding: 0.25rem 0.5rem;">
                                                编辑
                                            </button>
                                            <form method="POST" style="display: inline;" 
                                                  onsubmit="return confirm('确定要删除这个分类吗？')">
                                                <input type="hidden" name="action" value="delete_category">
                                                <input type="hidden" name="id" value="<?php echo $category['id']; ?>">
                                                <button type="submit" class="btn btn-danger" 
                                                        style="font-size: 0.75rem; padding: 0.25rem 0.5rem;"
                                                        <?php echo $category['product_count'] > 0 ? 'disabled title="该分类下有商品，无法删除"' : ''; ?>>
                                                    删除
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                            
                            <?php if (empty($categories)): ?>
                                <tr>
                                    <td colspan="9" style="padding: 2rem; text-align: center; color: #666;">
                                        没有找到分类
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <!-- 编辑分类模态框 -->
    <div id="editModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 12px; padding: 2rem; width: 90%; max-width: 500px;">
            <h3 style="margin-bottom: 1rem; color: #2c3e50;">编辑分类</h3>
            <form method="POST" id="editForm">
                <input type="hidden" name="action" value="edit_category">
                <input type="hidden" name="id" id="editId">
                
                <div style="margin-bottom: 1rem;">
                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">分类名称</label>
                    <input type="text" name="name" id="editName" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
                </div>
                
                <div style="margin-bottom: 1rem;">
                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">分类标识</label>
                    <input type="text" name="slug" id="editSlug" required style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
                </div>
                
                <div style="margin-bottom: 1rem;">
                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">描述</label>
                    <input type="text" name="description" id="editDescription" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
                </div>
                
                <div style="margin-bottom: 1rem;">
                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">排序</label>
                    <input type="number" name="sort_order" id="editSortOrder" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
                </div>
                
                <div style="margin-bottom: 1.5rem;">
                    <label style="display: flex; align-items: center; gap: 0.5rem;">
                        <input type="checkbox" name="is_virtual" id="editIsVirtual" value="1">
                        <span>虚拟商品分类</span>
                    </label>
                </div>
                
                <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                    <button type="button" onclick="closeEditModal()" class="btn btn-outline">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <style>
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>

    <script>
        function editCategory(category) {
            document.getElementById('editId').value = category.id;
            document.getElementById('editName').value = category.name;
            document.getElementById('editSlug').value = category.slug;
            document.getElementById('editDescription').value = category.description || '';
            document.getElementById('editSortOrder').value = category.sort_order;
            document.getElementById('editIsVirtual').checked = category.is_virtual == 1;
            document.getElementById('editModal').style.display = 'block';
        }

        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
        }

        // 点击模态框外部关闭
        document.getElementById('editModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeEditModal();
            }
        });
    </script>
</body>
</html>
