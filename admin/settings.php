<?php
session_start();
require_once '../config/database.php';

// 检查管理员登录状态
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$message = '';
$error = '';

// 处理设置更新
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action == 'update_settings') {
        // 这里可以添加系统设置的更新逻辑
        // 由于我们使用的是简化版本，这里只是演示
        $message = '设置更新成功';
    }
    
    if ($action == 'clear_cache') {
        // 清理缓存的逻辑
        $message = '缓存清理成功';
    }
    
    if ($action == 'backup_database') {
        // 数据库备份的逻辑
        $message = '数据库备份成功';
    }
}

// 获取系统信息
$systemInfo = [
    'php_version' => PHP_VERSION,
    'mysql_version' => $pdo->query('SELECT VERSION()')->fetchColumn(),
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'memory_limit' => ini_get('memory_limit'),
    'max_execution_time' => ini_get('max_execution_time'),
];

// 获取磁盘使用情况
$uploadDir = '../uploads/';
$diskUsage = [
    'total_space' => disk_total_space('.'),
    'free_space' => disk_free_space('.'),
    'upload_dir_size' => 0
];

if (is_dir($uploadDir)) {
    $diskUsage['upload_dir_size'] = getDirSize($uploadDir);
}

function getDirSize($dir) {
    $size = 0;
    if (is_dir($dir)) {
        $files = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
        );
        foreach ($files as $file) {
            $size += $file->getSize();
        }
    }
    return $size;
}

function formatBytes($size, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    return round($size, $precision) . ' ' . $units[$i];
}

$pageTitle = '系统设置';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - 数字鱼管理系统</title>
    <link rel="stylesheet" href="../css/bootstrap-icons.css">
    <link rel="stylesheet" href="css/admin.css">
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>数字鱼管理</h2>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="index.php"><i class="bi bi-speedometer2"></i><span>仪表盘</span></a></li>
                    <li><a href="users.php"><i class="bi bi-people"></i><span>用户管理</span></a></li>
                    <li><a href="products.php"><i class="bi bi-box-seam"></i><span>商品管理</span></a></li>
                    <li><a href="categories.php"><i class="bi bi-tags"></i><span>分类管理</span></a></li>
                    <li><a href="orders.php"><i class="bi bi-cart-check"></i><span>订单管理</span></a></li>
                    <li><a href="reports.php"><i class="bi bi-graph-up"></i><span>数据报表</span></a></li>
                    <li class="active"><a href="settings.php"><i class="bi bi-gear"></i><span>系统设置</span></a></li>
                    <li><a href="logout.php"><i class="bi bi-box-arrow-right"></i><span>退出登录</span></a></li>
                </ul>
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部栏 -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <h1>系统设置</h1>
                </div>
                <div class="top-bar-right">
                    <span class="admin-info">管理员：<?php echo htmlspecialchars($_SESSION['admin_username'] ?? 'Admin'); ?></span>
                </div>
            </header>

            <?php if ($message): ?>
                <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
            <?php endif; ?>

            <!-- 系统信息 -->
            <div style="background: white; border-radius: 12px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <h3 style="margin-bottom: 1rem; color: #2c3e50;">系统信息</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem;">
                    <div>
                        <h4 style="margin-bottom: 0.5rem; color: #666;">服务器环境</h4>
                        <ul style="list-style: none; padding: 0;">
                            <li style="padding: 0.25rem 0; border-bottom: 1px solid #f1f3f4;">
                                <strong>PHP版本:</strong> <?php echo $systemInfo['php_version']; ?>
                            </li>
                            <li style="padding: 0.25rem 0; border-bottom: 1px solid #f1f3f4;">
                                <strong>MySQL版本:</strong> <?php echo $systemInfo['mysql_version']; ?>
                            </li>
                            <li style="padding: 0.25rem 0; border-bottom: 1px solid #f1f3f4;">
                                <strong>内存限制:</strong> <?php echo $systemInfo['memory_limit']; ?>
                            </li>
                            <li style="padding: 0.25rem 0;">
                                <strong>执行时间限制:</strong> <?php echo $systemInfo['max_execution_time']; ?>秒
                            </li>
                        </ul>
                    </div>
                    <div>
                        <h4 style="margin-bottom: 0.5rem; color: #666;">上传设置</h4>
                        <ul style="list-style: none; padding: 0;">
                            <li style="padding: 0.25rem 0; border-bottom: 1px solid #f1f3f4;">
                                <strong>最大上传文件:</strong> <?php echo $systemInfo['upload_max_filesize']; ?>
                            </li>
                            <li style="padding: 0.25rem 0; border-bottom: 1px solid #f1f3f4;">
                                <strong>POST最大大小:</strong> <?php echo $systemInfo['post_max_size']; ?>
                            </li>
                            <li style="padding: 0.25rem 0;">
                                <strong>上传目录大小:</strong> <?php echo formatBytes($diskUsage['upload_dir_size']); ?>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 磁盘使用情况 -->
            <div style="background: white; border-radius: 12px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <h3 style="margin-bottom: 1rem; color: #2c3e50;">磁盘使用情况</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                    <div style="text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
                        <div style="font-size: 1.5rem; font-weight: 600; color: #3498db; margin-bottom: 0.5rem;">
                            <?php echo formatBytes($diskUsage['total_space']); ?>
                        </div>
                        <div style="color: #666;">总空间</div>
                    </div>
                    <div style="text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
                        <div style="font-size: 1.5rem; font-weight: 600; color: #27ae60; margin-bottom: 0.5rem;">
                            <?php echo formatBytes($diskUsage['free_space']); ?>
                        </div>
                        <div style="color: #666;">可用空间</div>
                    </div>
                    <div style="text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
                        <div style="font-size: 1.5rem; font-weight: 600; color: #e74c3c; margin-bottom: 0.5rem;">
                            <?php echo formatBytes($diskUsage['total_space'] - $diskUsage['free_space']); ?>
                        </div>
                        <div style="color: #666;">已使用</div>
                    </div>
                </div>
                <div style="margin-top: 1rem;">
                    <?php 
                    $usedPercent = (($diskUsage['total_space'] - $diskUsage['free_space']) / $diskUsage['total_space']) * 100;
                    ?>
                    <div style="background: #e9ecef; border-radius: 10px; height: 20px; overflow: hidden;">
                        <div style="background: linear-gradient(90deg, #27ae60, #f39c12, #e74c3c); height: 100%; width: <?php echo $usedPercent; ?>%; transition: width 0.3s;"></div>
                    </div>
                    <div style="text-align: center; margin-top: 0.5rem; color: #666;">
                        磁盘使用率: <?php echo round($usedPercent, 1); ?>%
                    </div>
                </div>
            </div>

            <!-- 系统操作 -->
            <div style="background: white; border-radius: 12px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <h3 style="margin-bottom: 1rem; color: #2c3e50;">系统操作</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                    <div style="border: 1px solid #e9ecef; border-radius: 8px; padding: 1rem;">
                        <h4 style="margin-bottom: 0.5rem; color: #666;">缓存管理</h4>
                        <p style="color: #666; font-size: 0.875rem; margin-bottom: 1rem;">清理系统缓存文件，提高系统性能</p>
                        <form method="POST">
                            <input type="hidden" name="action" value="clear_cache">
                            <button type="submit" class="btn btn-outline" onclick="return confirm('确定要清理缓存吗？')">
                                <i class="bi bi-trash"></i> 清理缓存
                            </button>
                        </form>
                    </div>

                    <div style="border: 1px solid #e9ecef; border-radius: 8px; padding: 1rem;">
                        <h4 style="margin-bottom: 0.5rem; color: #666;">数据备份</h4>
                        <p style="color: #666; font-size: 0.875rem; margin-bottom: 1rem;">备份数据库数据，确保数据安全</p>
                        <form method="POST">
                            <input type="hidden" name="action" value="backup_database">
                            <button type="submit" class="btn btn-primary" onclick="return confirm('确定要备份数据库吗？')">
                                <i class="bi bi-download"></i> 备份数据库
                            </button>
                        </form>
                    </div>

                    <div style="border: 1px solid #e9ecef; border-radius: 8px; padding: 1rem;">
                        <h4 style="margin-bottom: 0.5rem; color: #666;">系统日志</h4>
                        <p style="color: #666; font-size: 0.875rem; margin-bottom: 1rem;">查看系统运行日志和错误信息</p>
                        <button type="button" class="btn btn-outline" onclick="alert('日志查看功能开发中...')">
                            <i class="bi bi-file-text"></i> 查看日志
                        </button>
                    </div>
                </div>
            </div>

            <!-- 基本设置 -->
            <div style="background: white; border-radius: 12px; padding: 1.5rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <h3 style="margin-bottom: 1rem; color: #2c3e50;">基本设置</h3>
                <form method="POST">
                    <input type="hidden" name="action" value="update_settings">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem;">
                        <div>
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">网站名称</label>
                            <input type="text" name="site_name" value="数字鱼" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">网站URL</label>
                            <input type="url" name="site_url" value="http://localhost/xianyu" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">最大文件上传大小 (MB)</label>
                            <input type="number" name="max_file_size" value="5" min="1" max="100" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">每页显示商品数</label>
                            <input type="number" name="products_per_page" value="20" min="10" max="100" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                    </div>
                    <div style="margin-top: 1.5rem;">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check"></i> 保存设置
                        </button>
                    </div>
                </form>
            </div>
        </main>
    </div>

    <style>
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</body>
</html>
