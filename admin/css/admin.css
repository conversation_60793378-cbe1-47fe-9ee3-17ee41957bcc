/* 管理后台样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f5f6fa;
    color: #333;
}

.admin-container {
    display: flex;
    min-height: 100vh;
}

/* 侧边栏样式 */
.sidebar {
    width: 260px;
    background: #2c3e50;
    color: white;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid #34495e;
}

.sidebar-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
}

.sidebar-nav ul {
    list-style: none;
    padding: 1rem 0;
}

.sidebar-nav li {
    margin-bottom: 0.25rem;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: #bdc3c7;
    text-decoration: none;
    transition: all 0.3s;
}

.sidebar-nav a:hover {
    background: #34495e;
    color: white;
}

.sidebar-nav li.active a {
    background: #3498db;
    color: white;
}

.sidebar-nav i {
    margin-right: 0.75rem;
    font-size: 1.1rem;
    width: 20px;
}

/* 主内容区样式 */
.main-content {
    flex: 1;
    margin-left: 260px;
    padding: 2rem;
}

/* 顶部栏样式 */
.top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.top-bar h1 {
    font-size: 1.75rem;
    font-weight: 600;
    color: #2c3e50;
}

.admin-info {
    color: #666;
    font-size: 0.9rem;
}

/* 统计卡片样式 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    transition: all 0.3s;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.5rem;
    color: white;
}

.stat-icon.users { background: #3498db; }
.stat-icon.products { background: #e74c3c; }
.stat-icon.virtual { background: #f39c12; }
.stat-icon.orders { background: #27ae60; }
.stat-icon.revenue { background: #9b59b6; }

.stat-info h3 {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    color: #2c3e50;
}

.stat-info p {
    color: #666;
    margin-bottom: 0.5rem;
}

.stat-change {
    font-size: 0.875rem;
    color: #27ae60;
}

/* 快速操作样式 */
.quick-actions {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.quick-actions h2 {
    margin-bottom: 1rem;
    color: #2c3e50;
    font-size: 1.25rem;
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.action-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    text-decoration: none;
    color: #666;
    transition: all 0.3s;
    border: 1px solid #e9ecef;
}

.action-card:hover {
    background: #3498db;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.action-card i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

/* 最近活动样式 */
.recent-activity {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.recent-activity h2 {
    margin-bottom: 1rem;
    color: #2c3e50;
    font-size: 1.25rem;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.activity-icon {
    width: 40px;
    height: 40px;
    background: #3498db;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 1rem;
    font-size: 1rem;
}

.activity-content p {
    margin-bottom: 0.25rem;
    color: #333;
    font-weight: 500;
}

.activity-time {
    font-size: 0.875rem;
    color: #666;
}

/* 表格样式 */
.data-table {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-header h3 {
    color: #2c3e50;
    font-size: 1.25rem;
}

.table-actions {
    display: flex;
    gap: 0.5rem;
}

.btn {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    font-size: 0.875rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
}

.btn-success {
    background: #27ae60;
    color: white;
}

.btn-success:hover {
    background: #229954;
}

.btn-danger {
    background: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

.btn-outline {
    background: white;
    color: #666;
    border: 1px solid #ddd;
}

.btn-outline:hover {
    background: #f8f9fa;
    border-color: #3498db;
    color: #3498db;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s;
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .action-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .top-bar {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
}
