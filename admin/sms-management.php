<?php
session_start();
require_once '../config/database.php';
require_once '../includes/sms.php';

// 检查管理员登录状态
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$message = '';
$error = '';

// 处理配置更新
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action == 'update_config') {
        try {
            $provider = $_POST['provider'] ?? '';
            $accessKey = $_POST['access_key'] ?? '';
            $accessSecret = $_POST['access_secret'] ?? '';
            $signName = $_POST['sign_name'] ?? '';
            $dailyLimit = intval($_POST['daily_limit'] ?? 1000);
            $rateLimit = intval($_POST['rate_limit'] ?? 60);
            
            $stmt = $pdo->prepare("
                UPDATE sms_config 
                SET access_key = ?, access_secret = ?, sign_name = ?, 
                    daily_limit = ?, rate_limit = ?, updated_at = NOW()
                WHERE provider = ?
            ");
            $stmt->execute([$accessKey, $accessSecret, $signName, $dailyLimit, $rateLimit, $provider]);
            
            $message = '短信配置更新成功';
        } catch (Exception $e) {
            $error = '配置更新失败：' . $e->getMessage();
        }
    }
    
    if ($action == 'toggle_provider') {
        try {
            $provider = $_POST['provider'] ?? '';
            
            // 先禁用所有服务商
            $pdo->exec("UPDATE sms_config SET is_active = FALSE");
            
            // 启用指定服务商
            $stmt = $pdo->prepare("UPDATE sms_config SET is_active = TRUE WHERE provider = ?");
            $stmt->execute([$provider]);
            
            $message = "已切换到 {$provider} 短信服务商";
        } catch (Exception $e) {
            $error = '切换失败：' . $e->getMessage();
        }
    }
}

// 获取短信配置
try {
    $stmt = $pdo->query("SELECT * FROM sms_config ORDER BY provider");
    $configs = $stmt->fetchAll();
} catch (Exception $e) {
    $configs = [];
    $error = '获取配置失败：' . $e->getMessage();
}

// 获取短信统计
try {
    $sms = new SMSService();
    $stats = $sms->getStatistics();
    
    // 获取今日统计
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as today_total,
            SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as today_success
        FROM sms_logs 
        WHERE DATE(created_at) = CURDATE()
    ");
    $todayStats = $stmt->fetch();
    
    // 获取最近发送记录
    $stmt = $pdo->query("
        SELECT * FROM sms_logs 
        ORDER BY created_at DESC 
        LIMIT 20
    ");
    $recentLogs = $stmt->fetchAll();
    
} catch (Exception $e) {
    $stats = ['total' => 0, 'success' => 0, 'failed' => 0];
    $todayStats = ['today_total' => 0, 'today_success' => 0];
    $recentLogs = [];
}

$pageTitle = '短信管理';
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - 管理后台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-value {
            font-size: 32px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        .config-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        .alert {
            padding: 12px 16px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        .table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }
        .status-active {
            background: #d1ecf1;
            color: #0c5460;
        }
        .provider-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .provider-card.active {
            border-color: #007bff;
            background: #f8f9ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="bi bi-chat-dots"></i> 短信管理</h1>
            <p>管理短信服务配置和查看发送统计</p>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <!-- 统计信息 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value"><?php echo $todayStats['today_total']; ?></div>
                <div class="stat-label">今日发送</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo $todayStats['today_success']; ?></div>
                <div class="stat-label">今日成功</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo $stats['total']; ?></div>
                <div class="stat-label">总发送量</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo $stats['success']; ?></div>
                <div class="stat-label">总成功量</div>
            </div>
        </div>

        <!-- 服务商配置 -->
        <div class="config-section">
            <h3>短信服务商配置</h3>
            
            <?php foreach ($configs as $config): ?>
                <div class="provider-card <?php echo $config['is_active'] ? 'active' : ''; ?>">
                    <h4>
                        <?php echo strtoupper($config['provider']); ?>
                        <?php if ($config['is_active']): ?>
                            <span class="status-badge status-active">当前使用</span>
                        <?php endif; ?>
                    </h4>
                    
                    <form method="POST" style="display: inline-block; margin-right: 10px;">
                        <input type="hidden" name="action" value="update_config">
                        <input type="hidden" name="provider" value="<?php echo $config['provider']; ?>">
                        
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 15px 0;">
                            <div class="form-group">
                                <label class="form-label">Access Key</label>
                                <input type="text" name="access_key" class="form-input" 
                                       value="<?php echo htmlspecialchars($config['access_key'] ?? ''); ?>"
                                       placeholder="请输入Access Key">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Access Secret</label>
                                <input type="password" name="access_secret" class="form-input" 
                                       value="<?php echo htmlspecialchars($config['access_secret'] ?? ''); ?>"
                                       placeholder="请输入Access Secret">
                            </div>
                            <div class="form-group">
                                <label class="form-label">短信签名</label>
                                <input type="text" name="sign_name" class="form-input" 
                                       value="<?php echo htmlspecialchars($config['sign_name']); ?>"
                                       placeholder="请输入短信签名">
                            </div>
                            <div class="form-group">
                                <label class="form-label">每日限制</label>
                                <input type="number" name="daily_limit" class="form-input" 
                                       value="<?php echo $config['daily_limit']; ?>"
                                       min="1" max="10000">
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">更新配置</button>
                    </form>
                    
                    <?php if (!$config['is_active']): ?>
                        <form method="POST" style="display: inline-block;">
                            <input type="hidden" name="action" value="toggle_provider">
                            <input type="hidden" name="provider" value="<?php echo $config['provider']; ?>">
                            <button type="submit" class="btn btn-success">启用此服务商</button>
                        </form>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- 最近发送记录 -->
        <div class="config-section">
            <h3>最近发送记录</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>手机号</th>
                        <th>类型</th>
                        <th>状态</th>
                        <th>服务商</th>
                        <th>发送时间</th>
                        <th>错误信息</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recentLogs as $log): ?>
                        <tr>
                            <td><?php echo substr($log['phone'], 0, 3) . '****' . substr($log['phone'], -4); ?></td>
                            <td><?php echo $log['type']; ?></td>
                            <td>
                                <span class="status-badge status-<?php echo $log['status']; ?>">
                                    <?php echo $log['status'] === 'success' ? '成功' : '失败'; ?>
                                </span>
                            </td>
                            <td><?php echo $log['provider']; ?></td>
                            <td><?php echo $log['created_at']; ?></td>
                            <td><?php echo htmlspecialchars($log['error_message'] ?? '-'); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
