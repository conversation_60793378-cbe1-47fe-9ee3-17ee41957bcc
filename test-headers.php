<?php
// 测试header问题修复
session_start();

// 模拟登录状态
$_SESSION['user_id'] = 1;
$_SESSION['user'] = [
    'id' => 1,
    'nickname' => 'Test User',
    'avatar' => 'images/avatar-default.svg',
    'rating' => 5.0
];

echo "测试开始...\n";

// 测试各个页面是否能正常加载
$pages = [
    'member.php',
    'my-products.php', 
    'my-orders.php',
    'my-favorites.php',
    'messages.php',
    'help.php'
];

foreach ($pages as $page) {
    echo "测试 $page ... ";
    
    // 开启输出缓冲
    ob_start();
    
    try {
        // 捕获任何错误
        $error = '';
        set_error_handler(function($severity, $message, $file, $line) use (&$error) {
            $error = "Error: $message in $file on line $line";
        });
        
        // 包含页面文件
        include $page;
        
        // 恢复错误处理
        restore_error_handler();
        
        // 获取输出
        $output = ob_get_contents();
        
        if ($error) {
            echo "❌ 失败: $error\n";
        } else {
            echo "✅ 成功\n";
        }
        
    } catch (Exception $e) {
        echo "❌ 异常: " . $e->getMessage() . "\n";
    }
    
    // 清理输出缓冲
    ob_end_clean();
}

echo "\n测试完成！\n";
?>
