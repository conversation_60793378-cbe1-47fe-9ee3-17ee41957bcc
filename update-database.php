<?php
require_once 'config/database.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

try {
    $updates = [];
    $errors = [];
    
    // 检查订单表是否有 order_number 字段
    $stmt = $pdo->query("SHOW COLUMNS FROM orders LIKE 'order_number'");
    $hasOrderNumber = $stmt->fetch() !== false;
    
    if (!$hasOrderNumber) {
        // 添加订单号字段
        $pdo->exec("ALTER TABLE orders ADD COLUMN order_number VARCHAR(50) UNIQUE AFTER id");
        $updates[] = "添加 order_number 字段";
        
        // 为现有订单生成订单号
        $pdo->exec("UPDATE orders SET order_number = CONCAT('XY', DATE_FORMAT(created_at, '%Y%m%d%H%i%s'), LPAD(id, 4, '0')) WHERE order_number IS NULL");
        $updates[] = "为现有订单生成订单号";
        
        // 添加索引
        $pdo->exec("CREATE INDEX idx_order_number ON orders(order_number)");
        $updates[] = "添加 order_number 索引";
    } else {
        $updates[] = "order_number 字段已存在";
    }
    
    // 检查其他必要的索引
    try {
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_buyer_id ON orders(buyer_id)");
        $updates[] = "添加 buyer_id 索引";
    } catch (Exception $e) {
        // 索引可能已存在
    }
    
    try {
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_seller_id ON orders(seller_id)");
        $updates[] = "添加 seller_id 索引";
    } catch (Exception $e) {
        // 索引可能已存在
    }
    
    try {
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_status ON orders(status)");
        $updates[] = "添加 status 索引";
    } catch (Exception $e) {
        // 索引可能已存在
    }
    
    echo json_encode([
        'success' => true,
        'message' => '数据库更新完成',
        'updates' => $updates,
        'errors' => $errors
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '数据库更新失败：' . $e->getMessage(),
        'updates' => $updates,
        'errors' => array_merge($errors, [$e->getMessage()])
    ], JSON_UNESCAPED_UNICODE);
}
?>
