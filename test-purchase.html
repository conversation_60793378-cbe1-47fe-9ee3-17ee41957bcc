<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>购买功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-outline {
            background: transparent;
            color: #007bff;
            border: 1px solid #007bff;
        }
        .btn-outline:hover {
            background: #007bff;
            color: white;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }
        .modal-content {
            background-color: #fff;
            margin: 15% auto;
            padding: 20px;
            border-radius: 8px;
            width: 80%;
            max-width: 500px;
        }
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover {
            color: black;
        }
    </style>
</head>
<body>
    <h1>虚拟商品购买功能测试</h1>
    
    <div class="test-section">
        <h2>功能说明</h2>
        <p>这个页面用于测试 detail.php 中新增的购买功能：</p>
        <ul>
            <li>✅ 立即购买功能 - 显示购买确认模态框</li>
            <li>✅ 加入购物车功能 - 调用购物车API</li>
            <li>✅ 收藏功能 - 切换收藏状态</li>
            <li>✅ 分享功能 - 复制链接到剪贴板</li>
            <li>✅ 购买确认模态框 - 数量选择和总价计算</li>
            <li>✅ 消息提示系统 - 成功/错误消息显示</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>主要改进</h2>
        <ul>
            <li><strong>购买模态框</strong>：美观的购买确认界面，支持数量选择</li>
            <li><strong>实时价格计算</strong>：根据数量自动计算总价</li>
            <li><strong>库存检查</strong>：防止超出库存购买</li>
            <li><strong>收藏状态</strong>：正确显示已收藏/未收藏状态</li>
            <li><strong>消息提示</strong>：优雅的成功/错误消息显示</li>
            <li><strong>响应式设计</strong>：移动端友好的界面</li>
            <li><strong>虚拟商品支持</strong>：显示虚拟商品特有信息</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>API 接口</h2>
        <ul>
            <li><strong>api/purchase.php</strong> - 处理立即购买请求</li>
            <li><strong>api/cart.php</strong> - 处理购物车操作</li>
            <li><strong>api/favorite.php</strong> - 处理收藏操作</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>数据库更新</h2>
        <p>需要运行以下SQL来添加订单号字段：</p>
        <pre style="background: #fff; padding: 10px; border-radius: 4px;">
-- 添加订单号字段
ALTER TABLE orders ADD COLUMN order_number VARCHAR(50) UNIQUE AFTER id;

-- 为现有订单生成订单号
UPDATE orders SET order_number = CONCAT('XY', DATE_FORMAT(created_at, '%Y%m%d%H%i%s'), LPAD(id, 4, '0')) WHERE order_number IS NULL;

-- 添加索引
CREATE INDEX idx_order_number ON orders(order_number);
        </pre>
    </div>

    <div class="test-section">
        <h2>测试步骤</h2>
        <ol>
            <li>确保数据库已更新（运行上面的SQL）</li>
            <li>访问任意商品详情页（detail.php?id=1）</li>
            <li>测试立即购买功能</li>
            <li>测试加入购物车功能</li>
            <li>测试收藏功能</li>
            <li>测试分享功能</li>
            <li>检查响应式设计（移动端）</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>注意事项</h2>
        <ul>
            <li>需要登录用户才能进行购买操作</li>
            <li>不能购买自己发布的商品</li>
            <li>购买数量不能超过库存</li>
            <li>虚拟商品支持自动发货</li>
            <li>所有操作都有适当的错误处理</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>样式特性</h2>
        <ul>
            <li>现代化的模态框设计</li>
            <li>平滑的动画效果</li>
            <li>清晰的视觉层次</li>
            <li>一致的颜色方案</li>
            <li>移动端优化</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>快速访问</h2>
        <p>
            <a href="detail.php?id=1" class="btn">测试商品1</a>
            <a href="detail.php?id=2" class="btn">测试商品2</a>
            <a href="detail.php?id=3" class="btn">测试商品3</a>
        </p>
        <p>
            <a href="cart.php" class="btn btn-outline">查看购物车</a>
            <a href="my-orders.php" class="btn btn-outline">我的订单</a>
            <a href="my-favorites.php" class="btn btn-outline">我的收藏</a>
        </p>
    </div>

</body>
</html>
