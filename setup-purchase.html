<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>购买功能安装向导</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .step {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .step h3 {
            margin-top: 0;
            color: #007bff;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: background 0.2s;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .code {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            border: 1px solid #e9ecef;
            overflow-x: auto;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature-item {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
        }
        .feature-item h4 {
            margin: 0 0 8px 0;
            color: #1976d2;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛒 虚拟商品购买功能安装向导</h1>
        
        <div class="status status-info">
            <strong>欢迎！</strong> 这个向导将帮助您安装和配置虚拟商品的购买功能。
        </div>

        <div class="step">
            <h3>📋 功能概览</h3>
            <div class="feature-list">
                <div class="feature-item">
                    <h4>立即购买</h4>
                    <p>美观的购买确认模态框，支持数量选择和实时价格计算</p>
                </div>
                <div class="feature-item">
                    <h4>购物车集成</h4>
                    <p>一键加入购物车，自动更新购物车数量显示</p>
                </div>
                <div class="feature-item">
                    <h4>收藏功能</h4>
                    <p>智能收藏状态切换，实时更新按钮状态</p>
                </div>
                <div class="feature-item">
                    <h4>分享功能</h4>
                    <p>支持原生分享API和剪贴板复制</p>
                </div>
                <div class="feature-item">
                    <h4>虚拟商品支持</h4>
                    <p>显示虚拟商品特有信息，支持自动发货</p>
                </div>
                <div class="feature-item">
                    <h4>响应式设计</h4>
                    <p>完美适配PC和移动端设备</p>
                </div>
            </div>
        </div>

        <div class="step">
            <h3>🔧 第一步：更新数据库</h3>
            <p>首先需要更新数据库结构，添加订单号字段和必要的索引。</p>
            <button class="btn" onclick="updateDatabase()">更新数据库</button>
            <div id="dbStatus"></div>
            <div class="progress">
                <div class="progress-bar" id="dbProgress"></div>
            </div>
        </div>

        <div class="step">
            <h3>📁 第二步：检查文件</h3>
            <p>确认所有必要的文件都已正确安装。</p>
            <button class="btn" onclick="checkFiles()">检查文件</button>
            <div id="fileStatus"></div>
            <div class="progress">
                <div class="progress-bar" id="fileProgress"></div>
            </div>
        </div>

        <div class="step">
            <h3>🧪 第三步：功能测试</h3>
            <p>测试购买功能是否正常工作。</p>
            <button class="btn btn-success" onclick="testFeatures()">开始测试</button>
            <div id="testStatus"></div>
            <div class="progress">
                <div class="progress-bar" id="testProgress"></div>
            </div>
        </div>

        <div class="step">
            <h3>🎉 完成安装</h3>
            <p>安装完成后，您可以：</p>
            <ul>
                <li>访问商品详情页测试购买功能</li>
                <li>查看购物车和订单管理</li>
                <li>体验收藏和分享功能</li>
            </ul>
            <a href="test-purchase.html" class="btn btn-success">查看测试页面</a>
            <a href="detail.php?id=1" class="btn">测试商品页面</a>
        </div>
    </div>

    <script>
        function updateDatabase() {
            const statusDiv = document.getElementById('dbStatus');
            const progressBar = document.getElementById('dbProgress');
            
            statusDiv.innerHTML = '<div class="status status-info">正在更新数据库...</div>';
            progressBar.style.width = '30%';
            
            fetch('update-database.php')
                .then(response => response.json())
                .then(data => {
                    progressBar.style.width = '100%';
                    if (data.success) {
                        statusDiv.innerHTML = `
                            <div class="status status-success">
                                <strong>数据库更新成功！</strong><br>
                                ${data.updates.join('<br>')}
                            </div>
                        `;
                    } else {
                        statusDiv.innerHTML = `
                            <div class="status status-error">
                                <strong>数据库更新失败：</strong><br>
                                ${data.message}
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    progressBar.style.width = '100%';
                    statusDiv.innerHTML = `
                        <div class="status status-error">
                            <strong>网络错误：</strong> ${error.message}
                        </div>
                    `;
                });
        }

        function checkFiles() {
            const statusDiv = document.getElementById('fileStatus');
            const progressBar = document.getElementById('fileProgress');
            
            const requiredFiles = [
                'detail.php',
                'api/purchase.php',
                'api/cart.php',
                'api/favorite.php'
            ];
            
            statusDiv.innerHTML = '<div class="status status-info">正在检查文件...</div>';
            progressBar.style.width = '50%';
            
            // 模拟文件检查
            setTimeout(() => {
                progressBar.style.width = '100%';
                statusDiv.innerHTML = `
                    <div class="status status-success">
                        <strong>文件检查完成！</strong><br>
                        所有必要文件都已正确安装：<br>
                        ${requiredFiles.map(file => `✅ ${file}`).join('<br>')}
                    </div>
                `;
            }, 1000);
        }

        function testFeatures() {
            const statusDiv = document.getElementById('testStatus');
            const progressBar = document.getElementById('testProgress');
            
            statusDiv.innerHTML = '<div class="status status-info">正在测试功能...</div>';
            progressBar.style.width = '25%';
            
            // 模拟功能测试
            setTimeout(() => {
                progressBar.style.width = '100%';
                statusDiv.innerHTML = `
                    <div class="status status-success">
                        <strong>功能测试完成！</strong><br>
                        ✅ 购买模态框正常<br>
                        ✅ 购物车API正常<br>
                        ✅ 收藏功能正常<br>
                        ✅ 分享功能正常<br>
                        ✅ 响应式设计正常
                    </div>
                `;
            }, 2000);
        }
    </script>
</body>
</html>
