<?php
// 清理输出缓冲区
if (ob_get_level()) {
    ob_end_clean();
}

require_once '../includes/functions.php';
require_once '../includes/sms.php';

// 设置JSON响应头
header('Content-Type: application/json; charset=utf-8');

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '无效的请求方法'], JSON_UNESCAPED_UNICODE);
    exit;
}

// 获取请求参数
$action = $_POST['action'] ?? '';
$phone = trim($_POST['phone'] ?? '');
$code = trim($_POST['code'] ?? '');
$type = $_POST['type'] ?? 'login'; // login, register, reset_password, bind_phone

// 验证手机号格式
if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
    echo json_encode(['success' => false, 'message' => '请输入正确的手机号码'], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    $sms = new SMSService();
    
    switch ($action) {
        case 'send':
            // 发送验证码
            $verificationCode = SMSService::generateCode();
            $result = $sms->sendVerificationCode($phone, $verificationCode, $type);
            
            if ($result['success']) {
                echo json_encode([
                    'success' => true,
                    'message' => '验证码发送成功，请注意查收',
                    'countdown' => SMS_RATE_LIMIT
                ], JSON_UNESCAPED_UNICODE);
            } else {
                echo json_encode($result, JSON_UNESCAPED_UNICODE);
            }
            break;
            
        case 'verify':
            // 验证验证码
            if (empty($code)) {
                echo json_encode(['success' => false, 'message' => '请输入验证码'], JSON_UNESCAPED_UNICODE);
                exit;
            }
            
            if (!preg_match('/^\d{6}$/', $code)) {
                echo json_encode(['success' => false, 'message' => '验证码格式错误'], JSON_UNESCAPED_UNICODE);
                exit;
            }
            
            $result = $sms->verifyCode($phone, $code, $type);
            echo json_encode($result, JSON_UNESCAPED_UNICODE);
            break;
            
        case 'check_phone':
            // 检查手机号是否已注册
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE phone = ?");
            $stmt->execute([$phone]);
            $exists = $stmt->fetchColumn() > 0;
            
            echo json_encode([
                'success' => true,
                'exists' => $exists,
                'message' => $exists ? '手机号已注册' : '手机号可用'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'get_stats':
            // 获取发送统计（管理员功能）
            if (!isLoggedIn()) {
                echo json_encode(['success' => false, 'message' => '请先登录'], JSON_UNESCAPED_UNICODE);
                exit;
            }
            
            $stats = $sms->getStatistics($phone);
            echo json_encode([
                'success' => true,
                'stats' => $stats
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => '无效的操作'], JSON_UNESCAPED_UNICODE);
            break;
    }
    
} catch (Exception $e) {
    error_log("SMS API error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '服务暂时不可用，请稍后重试'
    ], JSON_UNESCAPED_UNICODE);
}

exit;
?>
