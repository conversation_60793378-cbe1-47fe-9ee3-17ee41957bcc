<?php
// 简化的表创建脚本

// 清理输出缓冲区
if (ob_get_level()) {
    ob_end_clean();
}

// 设置JSON响应头
header('Content-Type: application/json; charset=utf-8');

// 数据库配置
define('DB_HOST', 'localhost');
define('DB_NAME', 'xianyu_db');
define('DB_USER', 'xianyu_db');
define('DB_PASS', '2CY9SsWpXs6yWHks');
define('DB_CHARSET', 'utf8mb4');

try {
    // 创建数据库连接
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]
    );
    
    $createdTables = [];
    $errors = [];
    
    // 购物车表
    $sql = "CREATE TABLE IF NOT EXISTS cart (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        product_id INT NOT NULL,
        quantity INT DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_product_id (product_id),
        UNIQUE KEY unique_cart_item (user_id, product_id)
    )";
    
    if ($pdo->exec($sql) !== false) {
        $createdTables[] = 'cart';
    }
    
    // 钱包表
    $sql = "CREATE TABLE IF NOT EXISTS wallet (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        balance DECIMAL(10,2) DEFAULT 0.00,
        frozen_amount DECIMAL(10,2) DEFAULT 0.00,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        UNIQUE KEY unique_user_wallet (user_id)
    )";
    
    if ($pdo->exec($sql) !== false) {
        $createdTables[] = 'wallet';
    }
    
    // 钱包交易记录表
    $sql = "CREATE TABLE IF NOT EXISTS wallet_transactions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        type ENUM('deposit', 'withdraw', 'payment', 'refund', 'commission') NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        balance_after DECIMAL(10,2) NOT NULL,
        description TEXT,
        order_id INT NULL,
        status ENUM('pending', 'completed', 'failed') DEFAULT 'completed',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_type (type),
        INDEX idx_status (status)
    )";
    
    if ($pdo->exec($sql) !== false) {
        $createdTables[] = 'wallet_transactions';
    }
    
    // 实名认证表
    $sql = "CREATE TABLE IF NOT EXISTS verification (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        real_name VARCHAR(50) NOT NULL,
        id_number VARCHAR(20) NOT NULL,
        id_front_image VARCHAR(255) NOT NULL,
        id_back_image VARCHAR(255) NOT NULL,
        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
        reject_reason TEXT NULL,
        verified_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_status (status),
        UNIQUE KEY unique_user_verification (user_id)
    )";
    
    if ($pdo->exec($sql) !== false) {
        $createdTables[] = 'verification';
    }
    
    // 检查并添加用户表的is_verified字段
    try {
        $stmt = $pdo->query("SHOW COLUMNS FROM users WHERE Field = 'is_verified'");
        if (!$stmt || !$stmt->fetch()) {
            $pdo->exec("ALTER TABLE users ADD COLUMN is_verified BOOLEAN DEFAULT FALSE");
            $createdTables[] = 'users.is_verified (字段)';
        }
    } catch (Exception $e) {
        // 字段可能已存在或表不存在，记录错误但继续
        $errors[] = "添加is_verified字段失败: " . $e->getMessage();
    }
    
    // 确保uploads目录存在
    if (!is_dir('../uploads')) {
        if (mkdir('../uploads', 0755, true)) {
            $createdTables[] = 'uploads目录';
        }
    }
    
    echo json_encode([
        'success' => count($createdTables) > 0,
        'created_tables' => $createdTables,
        'errors' => $errors,
        'message' => count($createdTables) > 0 ? '表创建成功' : '没有新表需要创建'
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '创建表失败：' . $e->getMessage(),
        'created_tables' => [],
        'errors' => [$e->getMessage()]
    ], JSON_UNESCAPED_UNICODE);
}

exit;
?>
