<?php
// 基础测试API
header('Content-Type: application/json; charset=utf-8');

// 清理输出缓冲区
if (ob_get_level()) {
    ob_end_clean();
}

try {
    // 测试基本PHP功能
    $response = [
        'success' => true,
        'message' => 'API基础功能正常',
        'php_version' => PHP_VERSION,
        'server_time' => date('Y-m-d H:i:s'),
        'request_method' => $_SERVER['REQUEST_METHOD'],
        'post_data' => $_POST,
        'session_status' => session_status()
    ];
    
    // 测试文件包含
    if (file_exists(__DIR__ . '/../includes/functions.php')) {
        $response['functions_file'] = '存在';
        
        // 尝试包含文件
        require_once __DIR__ . '/../includes/functions.php';
        $response['functions_loaded'] = '已加载';
        
        // 测试数据库连接
        if (isset($pdo)) {
            $response['database'] = '已连接';
            
            // 测试简单查询
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM products");
            $count = $stmt->fetchColumn();
            $response['product_count'] = $count;
            
            // 测试会话
            if (function_exists('isLoggedIn')) {
                $response['login_status'] = isLoggedIn() ? '已登录' : '未登录';
                if (isLoggedIn()) {
                    $response['user_id'] = $_SESSION['user_id'] ?? 'unknown';
                }
            }
        } else {
            $response['database'] = '未连接';
        }
    } else {
        $response['functions_file'] = '不存在';
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}

exit;
?>
