<?php
// 安全的数据库测试，使用兼容的SQL语法

// 清理输出缓冲区
if (ob_get_level()) {
    ob_end_clean();
}

// 设置JSON响应头
header('Content-Type: application/json; charset=utf-8');

// 数据库配置
define('DB_HOST', 'localhost');
define('DB_NAME', 'xianyu_db');
define('DB_USER', 'xianyu_db');
define('DB_PASS', '2CY9SsWpXs6yWHks');
define('DB_CHARSET', 'utf8mb4');

try {
    // 创建数据库连接
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]
    );
    
    // 获取所有表
    $stmt = $pdo->query("SHOW TABLES");
    $allTables = [];
    while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
        $allTables[] = $row[0];
    }
    
    // 检查基本表
    $basicTables = ['users', 'products', 'categories'];
    $basicTablesExist = [];
    
    foreach ($basicTables as $table) {
        if (in_array($table, $allTables)) {
            $basicTablesExist[] = $table;
        }
    }
    
    // 检查新增的表
    $newTables = ['cart', 'wallet', 'wallet_transactions', 'verification'];
    $existingTables = [];
    $missingTables = [];
    
    foreach ($newTables as $table) {
        if (in_array($table, $allTables)) {
            $existingTables[] = $table;
        } else {
            $missingTables[] = $table;
        }
    }
    
    // 检查uploads目录权限
    $uploadsPath = '../uploads/';
    
    // 如果目录不存在，尝试创建
    if (!is_dir($uploadsPath)) {
        @mkdir($uploadsPath, 0755, true);
    }
    
    $uploadsWritable = is_writable($uploadsPath);
    $uploadsExists = is_dir($uploadsPath);
    
    // 检查users表是否有is_verified字段
    $hasVerifiedField = false;
    if (in_array('users', $allTables)) {
        try {
            $stmt = $pdo->query("DESCRIBE users");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            $hasVerifiedField = in_array('is_verified', $columns);
        } catch (Exception $e) {
            // 忽略错误
        }
    }
    
    $response = [
        'success' => count($basicTablesExist) >= 2, // 至少要有基本表
        'basic_tables' => $basicTablesExist,
        'existing_tables' => $existingTables,
        'missing_tables' => $missingTables,
        'all_tables' => $allTables,
        'uploads_exists' => $uploadsExists,
        'uploads_writable' => $uploadsWritable,
        'uploads_path' => $uploadsPath,
        'has_verified_field' => $hasVerifiedField,
        'message' => count($basicTablesExist) >= 2 ? '数据库连接正常' : '基本表不存在'
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '数据库连接失败：' . $e->getMessage(),
        'basic_tables' => [],
        'existing_tables' => [],
        'missing_tables' => [],
        'all_tables' => [],
        'uploads_exists' => false,
        'uploads_writable' => false,
        'has_verified_field' => false
    ], JSON_UNESCAPED_UNICODE);
}

exit;
?>
