<?php
require_once '../includes/functions.php';

header('Content-Type: application/json; charset=utf-8');

try {
    // 获取各种统计数据
    $stats = [];
    
    // 用户数量
    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
    $stats['user_count'] = $stmt->fetchColumn();
    
    // 商品数量
    $stmt = $pdo->query("SELECT COUNT(*) FROM products");
    $stats['product_count'] = $stmt->fetchColumn();
    
    // 订单数量
    $stmt = $pdo->query("SELECT COUNT(*) FROM orders");
    $stats['order_count'] = $stmt->fetchColumn();
    
    // 分类数量
    $stmt = $pdo->query("SELECT COUNT(*) FROM categories");
    $stats['category_count'] = $stmt->fetchColumn();
    
    // 检查购物车表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'cart'");
    $cartTableExists = $stmt->fetch() !== false;
    
    if ($cartTableExists) {
        $stmt = $pdo->query("SELECT COUNT(*) FROM cart");
        $stats['cart_items'] = $stmt->fetchColumn();
    } else {
        $stats['cart_items'] = 0;
    }
    
    // 最近订单
    $stmt = $pdo->query("SELECT COUNT(*) FROM orders WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
    $stats['recent_orders'] = $stmt->fetchColumn();
    
    // 活跃商品数量
    $stmt = $pdo->query("SELECT COUNT(*) FROM products WHERE status = 'active'");
    $stats['active_products'] = $stmt->fetchColumn();
    
    echo json_encode([
        'success' => true,
        'stats' => $stats,
        'user_count' => $stats['user_count'],
        'product_count' => $stats['product_count'],
        'order_count' => $stats['order_count'],
        'category_count' => $stats['category_count'],
        'cart_items' => $stats['cart_items'],
        'recent_orders' => $stats['recent_orders'],
        'active_products' => $stats['active_products']
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}
?>
