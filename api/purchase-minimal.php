<?php
// 最简化购买API
session_start();

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 清理输出缓冲区
while (ob_get_level()) {
    ob_end_clean();
}

// 基本错误处理
ini_set('display_errors', 0);
error_reporting(E_ALL);

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '只支持POST请求']);
    exit;
}

try {
    // 数据库配置
    $host = 'localhost';
    $dbname = 'xianyu_db';
    $username = 'xianyu_db';
    $password = '2CY9SsWpXs6yWHks';
    
    // 创建数据库连接
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 检查登录状态
    if (!isset($_SESSION['user_id'])) {
        echo json_encode(['success' => false, 'message' => '请先登录']);
        exit;
    }
    
    // 获取参数
    $productId = intval($_POST['product_id'] ?? 0);
    $quantity = intval($_POST['quantity'] ?? 1);
    
    if ($productId <= 0) {
        echo json_encode(['success' => false, 'message' => '商品ID无效']);
        exit;
    }
    
    if ($quantity <= 0) {
        echo json_encode(['success' => false, 'message' => '购买数量无效']);
        exit;
    }
    
    // 开始事务
    $pdo->beginTransaction();
    
    // 获取商品信息
    $stmt = $pdo->prepare("SELECT * FROM products WHERE id = ? AND status = 'active'");
    $stmt->execute([$productId]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$product) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => '商品不存在或已下架']);
        exit;
    }
    
    // 检查是否是自己的商品
    if ($product['user_id'] == $_SESSION['user_id']) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => '不能购买自己的商品']);
        exit;
    }
    
    // 检查库存
    if ($product['stock'] < $quantity) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => '库存不足']);
        exit;
    }
    
    // 计算总价
    $totalPrice = $product['price'] * $quantity;
    
    // 创建订单 - 先检查表结构
    try {
        $stmt = $pdo->prepare("
            INSERT INTO orders (buyer_id, seller_id, product_id, quantity, total_price, status, created_at)
            VALUES (?, ?, ?, ?, ?, 'pending', NOW())
        ");

        $result = $stmt->execute([
            $_SESSION['user_id'],
            $product['user_id'],
            $productId,
            $quantity,
            $totalPrice
        ]);
    } catch (PDOException $e) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => '订单表结构错误: ' . $e->getMessage()]);
        exit;
    }
    
    if (!$result) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => '订单创建失败']);
        exit;
    }
    
    $orderId = $pdo->lastInsertId();
    
    // 减少库存
    $stmt = $pdo->prepare("UPDATE products SET stock = stock - ? WHERE id = ?");
    $stmt->execute([$quantity, $productId]);
    
    // 提交事务
    $pdo->commit();
    
    // 生成订单号
    $orderNumber = 'XY' . date('YmdHis') . str_pad($orderId, 4, '0', STR_PAD_LEFT);

    // 尝试更新订单号（如果字段存在）
    try {
        $stmt = $pdo->prepare("UPDATE orders SET order_number = ? WHERE id = ?");
        $stmt->execute([$orderNumber, $orderId]);
    } catch (PDOException $e) {
        // 如果order_number字段不存在，忽略这个错误
        error_log("Order number update failed: " . $e->getMessage());
    }

    // 返回成功响应
    echo json_encode([
        'success' => true,
        'message' => '购买成功！',
        'order_id' => $orderId,
        'order_number' => $orderNumber,
        'product_title' => $product['title'],
        'total_price' => $totalPrice
    ]);
    
} catch (Exception $e) {
    // 回滚事务
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    // 返回错误响应
    echo json_encode([
        'success' => false,
        'message' => '购买失败：' . $e->getMessage(),
        'error_details' => [
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}

exit;
?>
