<?php
header('Content-Type: application/json');
require_once '../includes/functions.php';

// 检查登录状态
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => '请先登录']);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '无效的请求方法']);
    exit;
}

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);
$productId = intval($input['product_id'] ?? 0);

if (!$productId) {
    echo json_encode(['success' => false, 'message' => '无效的商品ID']);
    exit;
}

try {
    // 检查商品是否存在
    $stmt = $pdo->prepare("SELECT id FROM products WHERE id = ? AND status = 'active'");
    $stmt->execute([$productId]);
    
    if (!$stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => '商品不存在']);
        exit;
    }
    
    // 检查是否已收藏
    $stmt = $pdo->prepare("SELECT id FROM favorites WHERE user_id = ? AND product_id = ?");
    $stmt->execute([$_SESSION['user_id'], $productId]);
    $favorite = $stmt->fetch();
    
    if ($favorite) {
        // 取消收藏
        $stmt = $pdo->prepare("DELETE FROM favorites WHERE user_id = ? AND product_id = ?");
        $stmt->execute([$_SESSION['user_id'], $productId]);
        
        // 更新商品点赞数
        $stmt = $pdo->prepare("UPDATE products SET likes = likes - 1 WHERE id = ? AND likes > 0");
        $stmt->execute([$productId]);
        
        echo json_encode(['success' => true, 'message' => '已取消收藏', 'action' => 'removed']);
    } else {
        // 添加收藏
        $stmt = $pdo->prepare("INSERT INTO favorites (user_id, product_id) VALUES (?, ?)");
        $stmt->execute([$_SESSION['user_id'], $productId]);
        
        // 更新商品点赞数
        $stmt = $pdo->prepare("UPDATE products SET likes = likes + 1 WHERE id = ?");
        $stmt->execute([$productId]);
        
        echo json_encode(['success' => true, 'message' => '收藏成功', 'action' => 'added']);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => '操作失败：' . $e->getMessage()]);
}
?>
