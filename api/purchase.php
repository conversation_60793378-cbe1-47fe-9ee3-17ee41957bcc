<?php
// 清理输出缓冲区
if (ob_get_level()) {
    ob_end_clean();
}

// 启用错误日志记录，但不显示在页面上
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL);

require_once '../includes/functions.php';

// 设置JSON响应头
header('Content-Type: application/json; charset=utf-8');

// 检查登录状态
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => '请先登录'], JSON_UNESCAPED_UNICODE);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '无效的请求方法'], JSON_UNESCAPED_UNICODE);
    exit;
}

// 获取请求参数
$action = $_POST['action'] ?? '';
$productId = intval($_POST['product_id'] ?? 0);
$quantity = intval($_POST['quantity'] ?? 1);

if ($productId <= 0 || $quantity <= 0) {
    echo json_encode(['success' => false, 'message' => '无效的参数'], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 检查数据库连接
    if (!isset($pdo)) {
        throw new Exception('数据库连接失败');
    }

    // 开始事务
    $pdo->beginTransaction();

    // 获取商品信息
    $stmt = $pdo->prepare("
        SELECT p.*, u.nickname as seller_name
        FROM products p
        JOIN users u ON p.user_id = u.id
        WHERE p.id = ? AND p.status = 'active'
    ");
    $stmt->execute([$productId]);
    $product = $stmt->fetch();

    if (!$product) {
        throw new Exception('商品不存在或已下架');
    }

    // 检查是否是自己的商品
    if ($product['user_id'] == $_SESSION['user_id']) {
        throw new Exception('不能购买自己的商品');
    }

    // 检查库存
    if ($product['stock'] < $quantity) {
        throw new Exception('库存不足');
    }

    // 计算总价
    $totalPrice = $product['price'] * $quantity;
    
    // 创建订单
    $stmt = $pdo->prepare("
        INSERT INTO orders (buyer_id, seller_id, product_id, quantity, total_price, status, created_at)
        VALUES (?, ?, ?, ?, ?, 'pending', NOW())
    ");
    $stmt->execute([
        $_SESSION['user_id'],
        $product['user_id'],
        $productId,
        $quantity,
        $totalPrice
    ]);

    $orderId = $pdo->lastInsertId();

    // 生成订单号
    $orderNumber = 'XY' . date('YmdHis') . str_pad($orderId, 4, '0', STR_PAD_LEFT);

    // 尝试更新订单号（如果字段存在）
    try {
        $stmt = $pdo->prepare("UPDATE orders SET order_number = ? WHERE id = ?");
        $stmt->execute([$orderNumber, $orderId]);
    } catch (Exception $e) {
        // 如果order_number字段不存在，忽略这个错误
        error_log("Order number update failed: " . $e->getMessage());
    }

    // 减少库存
    $stmt = $pdo->prepare("UPDATE products SET stock = stock - ? WHERE id = ? AND stock >= ?");
    $stmt->execute([$quantity, $productId, $quantity]);

    if ($stmt->rowCount() === 0) {
        throw new Exception('库存不足，请刷新页面重试');
    }

    // 简化虚拟商品处理
    if (isset($product['is_virtual']) && $product['is_virtual']) {
        // 虚拟商品默认为待处理状态，后续可以手动发货
        // 暂时不自动完成订单，避免复杂的逻辑错误
    }
    
    // 提交事务
    $pdo->commit();
    
    echo json_encode([
        'success' => true, 
        'message' => '购买成功！',
        'order_id' => $orderId,
        'order_number' => $orderNumber
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    // 回滚事务
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }

    // 记录错误日志
    error_log("Purchase error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());

    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'debug' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ], JSON_UNESCAPED_UNICODE);
}

exit;
