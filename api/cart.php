<?php
// 清理输出缓冲区
if (ob_get_level()) {
    ob_end_clean();
}

require_once '../includes/functions.php';

// 设置JSON响应头
header('Content-Type: application/json; charset=utf-8');

// 检查登录状态
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => '请先登录'], JSON_UNESCAPED_UNICODE);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '无效的请求方法'], JSON_UNESCAPED_UNICODE);
    exit;
}

// 获取请求参数
$action = $_POST['action'] ?? '';
$productId = intval($_POST['product_id'] ?? 0);
$quantity = intval($_POST['quantity'] ?? 1);

if ($productId <= 0) {
    echo json_encode(['success' => false, 'message' => '无效的商品ID'], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    switch ($action) {
        case 'add':
            // 检查商品是否存在且可购买
            $stmt = $pdo->prepare("SELECT id, title, price, stock, status FROM products WHERE id = ? AND status = 'active'");
            $stmt->execute([$productId]);
            $product = $stmt->fetch();
            
            if (!$product) {
                echo json_encode(['success' => false, 'message' => '商品不存在或已下架'], JSON_UNESCAPED_UNICODE);
                exit;
            }

            // 检查是否是自己的商品
            $stmt = $pdo->prepare("SELECT user_id FROM products WHERE id = ?");
            $stmt->execute([$productId]);
            $productOwner = $stmt->fetchColumn();

            if ($productOwner == $_SESSION['user_id']) {
                echo json_encode(['success' => false, 'message' => '不能购买自己的商品'], JSON_UNESCAPED_UNICODE);
                exit;
            }

            // 检查库存
            if ($product['stock'] < $quantity) {
                echo json_encode(['success' => false, 'message' => '库存不足'], JSON_UNESCAPED_UNICODE);
                exit;
            }
            
            // 检查是否已在购物车中
            $stmt = $pdo->prepare("SELECT quantity FROM cart WHERE user_id = ? AND product_id = ?");
            $stmt->execute([$_SESSION['user_id'], $productId]);
            $existingItem = $stmt->fetch();
            
            if ($existingItem) {
                // 更新数量
                $newQuantity = $existingItem['quantity'] + $quantity;
                if ($newQuantity > $product['stock']) {
                    echo json_encode(['success' => false, 'message' => '购物车中商品数量超过库存'], JSON_UNESCAPED_UNICODE);
                    exit;
                }

                $stmt = $pdo->prepare("UPDATE cart SET quantity = ?, updated_at = CURRENT_TIMESTAMP WHERE user_id = ? AND product_id = ?");
                $stmt->execute([$newQuantity, $_SESSION['user_id'], $productId]);

                echo json_encode(['success' => true, 'message' => '已更新购物车中的商品数量'], JSON_UNESCAPED_UNICODE);
            } else {
                // 添加新商品
                $stmt = $pdo->prepare("INSERT INTO cart (user_id, product_id, quantity) VALUES (?, ?, ?)");
                $stmt->execute([$_SESSION['user_id'], $productId, $quantity]);

                echo json_encode(['success' => true, 'message' => '商品已添加到购物车'], JSON_UNESCAPED_UNICODE);
            }
            break;
            
        case 'remove':
            // 从购物车移除商品
            $stmt = $pdo->prepare("DELETE FROM cart WHERE user_id = ? AND product_id = ?");
            $stmt->execute([$_SESSION['user_id'], $productId]);
            
            if ($stmt->rowCount() > 0) {
                echo json_encode(['success' => true, 'message' => '商品已从购物车移除'], JSON_UNESCAPED_UNICODE);
            } else {
                echo json_encode(['success' => false, 'message' => '商品不在购物车中'], JSON_UNESCAPED_UNICODE);
            }
            break;
            
        case 'update':
            // 更新购物车商品数量
            if ($quantity <= 0) {
                // 数量为0时删除商品
                $stmt = $pdo->prepare("DELETE FROM cart WHERE user_id = ? AND product_id = ?");
                $stmt->execute([$_SESSION['user_id'], $productId]);
                echo json_encode(['success' => true, 'message' => '商品已从购物车移除'], JSON_UNESCAPED_UNICODE);
            } else {
                // 检查库存
                $stmt = $pdo->prepare("SELECT stock FROM products WHERE id = ? AND status = 'active'");
                $stmt->execute([$productId]);
                $stock = $stmt->fetchColumn();

                if ($stock === false) {
                    echo json_encode(['success' => false, 'message' => '商品不存在或已下架'], JSON_UNESCAPED_UNICODE);
                    exit;
                }

                if ($quantity > $stock) {
                    echo json_encode(['success' => false, 'message' => '数量超过库存'], JSON_UNESCAPED_UNICODE);
                    exit;
                }

                // 更新数量
                $stmt = $pdo->prepare("UPDATE cart SET quantity = ?, updated_at = CURRENT_TIMESTAMP WHERE user_id = ? AND product_id = ?");
                $stmt->execute([$quantity, $_SESSION['user_id'], $productId]);

                if ($stmt->rowCount() > 0) {
                    echo json_encode(['success' => true, 'message' => '购物车已更新'], JSON_UNESCAPED_UNICODE);
                } else {
                    echo json_encode(['success' => false, 'message' => '商品不在购物车中'], JSON_UNESCAPED_UNICODE);
                }
            }
            break;
            
        case 'clear':
            // 清空购物车
            $stmt = $pdo->prepare("DELETE FROM cart WHERE user_id = ?");
            $stmt->execute([$_SESSION['user_id']]);

            echo json_encode(['success' => true, 'message' => '购物车已清空'], JSON_UNESCAPED_UNICODE);
            break;

        case 'count':
            // 获取购物车商品数量
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM cart WHERE user_id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            $count = $stmt->fetchColumn();

            echo json_encode(['success' => true, 'count' => $count], JSON_UNESCAPED_UNICODE);
            break;

        default:
            echo json_encode(['success' => false, 'message' => '无效的操作'], JSON_UNESCAPED_UNICODE);
            break;
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => '操作失败：' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
}

exit;
