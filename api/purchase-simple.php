<?php
// 简化版购买API - 用于调试
// 清理输出缓冲区
if (ob_get_level()) {
    ob_end_clean();
}

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 启用错误日志，但不显示错误
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL);

try {
    // 包含必要文件
    $functionsPath = __DIR__ . '/../includes/functions.php';
    if (!file_exists($functionsPath)) {
        echo json_encode(['success' => false, 'message' => 'functions.php文件不存在'], JSON_UNESCAPED_UNICODE);
        exit;
    }

    require_once $functionsPath;

    // 检查请求方法
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        echo json_encode(['success' => false, 'message' => '只支持POST请求', 'method' => $_SERVER['REQUEST_METHOD']], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 检查登录状态
    if (!isLoggedIn()) {
        echo json_encode(['success' => false, 'message' => '请先登录'], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 获取参数
    $productId = intval($_POST['product_id'] ?? 0);
    $quantity = intval($_POST['quantity'] ?? 1);
    
    if ($productId <= 0) {
        echo json_encode(['success' => false, 'message' => '商品ID无效'], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    if ($quantity <= 0) {
        echo json_encode(['success' => false, 'message' => '购买数量无效'], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 检查数据库连接
    if (!isset($pdo)) {
        echo json_encode(['success' => false, 'message' => '数据库连接失败', 'pdo_exists' => false], JSON_UNESCAPED_UNICODE);
        exit;
    }

    // 测试数据库连接
    try {
        $pdo->query("SELECT 1");
    } catch (Exception $dbError) {
        echo json_encode(['success' => false, 'message' => '数据库查询失败: ' . $dbError->getMessage()], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 开始事务
    $pdo->beginTransaction();
    
    // 获取商品信息
    $stmt = $pdo->prepare("SELECT * FROM products WHERE id = ? AND status = 'active'");
    $stmt->execute([$productId]);
    $product = $stmt->fetch();
    
    if (!$product) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => '商品不存在或已下架'], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 检查是否是自己的商品
    if ($product['user_id'] == $_SESSION['user_id']) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => '不能购买自己的商品'], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 检查库存
    if ($product['stock'] < $quantity) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => '库存不足'], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 计算总价
    $totalPrice = $product['price'] * $quantity;
    
    // 创建订单 - 使用最基本的字段
    $stmt = $pdo->prepare("
        INSERT INTO orders (buyer_id, seller_id, product_id, quantity, total_price, status, created_at)
        VALUES (?, ?, ?, ?, ?, 'pending', NOW())
    ");
    
    $result = $stmt->execute([
        $_SESSION['user_id'],
        $product['user_id'],
        $productId,
        $quantity,
        $totalPrice
    ]);
    
    if (!$result) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => '订单创建失败'], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    $orderId = $pdo->lastInsertId();
    
    // 减少库存
    $stmt = $pdo->prepare("UPDATE products SET stock = stock - ? WHERE id = ? AND stock >= ?");
    $stmt->execute([$quantity, $productId, $quantity]);
    
    if ($stmt->rowCount() === 0) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => '库存不足，请刷新页面重试'], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 提交事务
    $pdo->commit();
    
    // 生成订单号
    $orderNumber = 'XY' . date('YmdHis') . str_pad($orderId, 4, '0', STR_PAD_LEFT);
    
    // 返回成功响应
    echo json_encode([
        'success' => true,
        'message' => '购买成功！',
        'order_id' => $orderId,
        'order_number' => $orderNumber,
        'product_title' => $product['title'],
        'total_price' => $totalPrice
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    // 回滚事务
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    // 记录错误
    error_log("Purchase error: " . $e->getMessage());
    
    // 返回错误响应
    echo json_encode([
        'success' => false,
        'message' => '购买失败：' . $e->getMessage(),
        'debug' => [
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ], JSON_UNESCAPED_UNICODE);
}

exit;
?>
