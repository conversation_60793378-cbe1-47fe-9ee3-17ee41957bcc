<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发布商品 - 数字鱼</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/bootstrap-icons.css">
    <link rel="stylesheet" href="css/virtual-product.css">
    <link rel="stylesheet" href="css/theme-colors.css">
    <link rel="stylesheet" href="css/enhanced-style.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/publish.css">
    <link rel="stylesheet" href="css/publish-enhanced.css">
    <link rel="stylesheet" href="css/fonts.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="container">
            <div class="header-inner">
                <div class="logo">
                    <a href="index.html">
                        <img src="images/logo.svg" alt="数字鱼logo">
                        <span class="logo-text">数字鱼</span>
                    </a>
                </div>
                <div class="search-box">
                    <input type="text" placeholder="搜索你想要的虚拟商品">
                    <button class="search-btn"><i class="bi bi-search"></i></button>
                </div>
                <div class="nav-links">
                    <ul>
                        <li><a href="index.html">首页</a></li>
                        <li><a href="nearby.html">同城</a></li>
                        <li><a href="publish.html" class="publish-btn active">发布商品</a></li>
                        <li><a href="message.html">消息</a></li>
                        <li><a href="member.html">我的</a></li>
                    </ul>
                </div>
                <div class="user-info">
                    <a href="login.html" class="login-btn">登录/注册</a>
                    <!-- 登录后显示 -->
                    <!-- <div class="user-avatar">
                        <img src="images/avatar.svg" alt="用户头像">
                        <div class="user-menu">
                            <a href="member.html">个人中心</a>
                            <a href="#">退出登录</a>
                        </div>
                    </div> -->
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容区 -->
    <main class="main publish-page">
        <div class="container">
            <!-- 侧边导航栏 -->
            <div class="sidebar-nav">
                <ul>
                    <li><a href="index.html">首页</a></li>
                    <li><a href="nearby.html">同城</a></li>
                    <li><a href="publish.html" class="active">发布商品</a></li>
                    <li><a href="message.html">消息</a></li>
                    <li><a href="member.html">我的</a></li>
                    <li><a href="login.html">登录/注册</a></li>
                </ul>
            </div>
            
            <div class="publish-container">
                <h1 class="page-title">发布商品</h1>
                
                <form class="publish-form" id="publishForm">
                    <!-- 商品类型选择 -->
                    <div class="form-group">
                        <label class="form-label">商品类型</label>
                        <div class="product-type-selector">
                            <div class="type-option">
                                <input type="radio" name="productType" id="typePhysical" value="physical">
                                <label for="typePhysical">实物商品</label>
                            </div>
                            <div class="type-option">
                                <input type="radio" name="productType" id="typeVirtual" value="virtual" checked>
                                <label for="typeVirtual">虚拟商品</label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 虚拟商品分类 -->
                    <div class="form-group" id="virtualCategoryGroup">
                        <label class="form-label">虚拟商品分类</label>
                        <div class="virtual-category-selector">
                            <select class="form-select" id="virtualCategory">
                                <option value="">请选择分类</option>
                                <option value="membership">会员账号</option>
                                <option value="gamecard">游戏点卡</option>
                                <option value="software">软件授权</option>
                                <option value="ebooks">电子书籍</option>
                                <option value="design">设计资源</option>
                                <option value="courses">教程课程</option>
                                <option value="media">音乐影视</option>
                                <option value="services">虚拟服务</option>
                                <option value="others">其他虚拟</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- 商品标题 -->
                    <div class="form-group">
                        <label class="form-label">商品标题</label>
                        <input type="text" class="form-input" id="productTitle" placeholder="请输入商品标题，最多30字" maxlength="30">
                        <div class="input-tips">标题越详细，越容易被买家搜索到</div>
                    </div>
                    
                    <!-- 商品图片 -->
                    <div class="form-group">
                        <label class="form-label">商品图片</label>
                        <div class="image-uploader">
                            <div class="upload-preview">
                                <div class="upload-item add-image">
                                    <i class="bi bi-plus-lg"></i>
                                    <span>添加图片</span>
                                    <input type="file" class="file-input" id="imageUpload" multiple accept="image/*">
                                </div>
                                <!-- 预览图将在这里显示 -->
                            </div>
                            <div class="upload-tips">
                                <p>最多上传9张图片，单张不超过5MB</p>
                                <p>对于虚拟商品，请上传商品截图、使用效果图等</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 商品描述 -->
                    <div class="form-group">
                        <label class="form-label">商品描述</label>
                        <textarea class="form-textarea" id="productDescription" placeholder="请详细描述商品的内容、用途、使用方法等信息，让买家更好地了解商品" rows="6"></textarea>
                    </div>
                    
                    <!-- 虚拟商品属性 -->
                    <div class="form-group" id="virtualAttributesGroup">
                        <label class="form-label">虚拟商品属性</label>
                        <div class="virtual-attributes">
                            <!-- 使用期限 -->
                            <div class="attribute-item">
                                <label class="attribute-label">使用期限</label>
                                <select class="form-select" id="usagePeriod">
                                    <option value="unlimited">永久有效</option>
                                    <option value="limited">限时有效</option>
                                </select>
                                <div class="period-input" style="display: none;">
                                    <input type="number" class="form-input" id="periodValue" min="1">
                                    <select class="form-select" id="periodUnit">
                                        <option value="day">天</option>
                                        <option value="month">月</option>
                                        <option value="year">年</option>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- 发货方式 -->
                            <div class="attribute-item">
                                <label class="attribute-label">发货方式</label>
                                <select class="form-select" id="deliveryMethod">
                                    <option value="automatic">自动发货</option>
                                    <option value="manual">手动发货</option>
                                </select>
                            </div>
                            
                            <!-- 使用平台 -->
                            <div class="attribute-item">
                                <label class="attribute-label">使用平台</label>
                                <input type="text" class="form-input" id="platform" placeholder="如：iOS/安卓/Windows/Mac等">
                            </div>
                        </div>
                    </div>
                    
                    <!-- 商品价格 -->
                    <div class="form-group">
                        <label class="form-label">商品价格</label>
                        <div class="price-input">
                            <span class="price-symbol">¥</span>
                            <input type="number" class="form-input" id="productPrice" placeholder="0.00" min="0" step="0.01">
                        </div>
                    </div>
                    
                    <!-- 原价展示 -->
                    <div class="form-group">
                        <label class="form-label">原价展示（选填）</label>
                        <div class="price-input">
                            <span class="price-symbol">¥</span>
                            <input type="number" class="form-input" id="originalPrice" placeholder="0.00" min="0" step="0.01">
                        </div>
                    </div>
                    
                    <!-- 库存数量 -->
                    <div class="form-group">
                        <label class="form-label">库存数量</label>
                        <input type="number" class="form-input" id="inventory" placeholder="请输入库存数量" min="1" value="1">
                    </div>
                    
                    <!-- 交易方式 -->
                    <div class="form-group">
                        <label class="form-label">交易方式</label>
                        <div class="trade-options">
                            <label class="checkbox-label">
                                <input type="checkbox" id="tradePlatform" checked> 平台担保交易
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" id="tradeOnline"> 在线交付
                            </label>
                        </div>
                    </div>
                    
                    <!-- 商品内容 -->
                    <div class="form-group" id="contentGroup">
                        <label class="form-label">商品内容</label>
                        <div class="content-tabs">
                            <div class="content-tab active" data-tab="contentText">文本内容</div>
                            <div class="content-tab" data-tab="contentFile">附件内容</div>
                        </div>
                        
                        <div class="content-panels">
                            <!-- 文本内容面板 -->
                            <div class="content-panel active" id="contentText">
                                <textarea class="form-textarea" id="textContent" placeholder="请输入激活码、账号密码等文本内容，买家付款后可见" rows="4"></textarea>
                                <div class="input-tips">该内容仅在买家付款后可见，请勿在商品描述中透露</div>
                            </div>
                            
                            <!-- 附件内容面板 -->
                            <div class="content-panel" id="contentFile">
                                <div class="file-uploader">
                                    <div class="upload-btn">
                                        <i class="bi bi-file-earmark-arrow-up"></i>
                                        <span>上传附件</span>
                                        <input type="file" class="file-input" id="fileUpload">
                                    </div>
                                    <div class="upload-tips">
                                        <p>支持PDF、ZIP、RAR等格式，单个文件不超过50MB</p>
                                        <p>附件内容仅在买家付款后可下载</p>
                                    </div>
                                </div>
                                <div class="file-list">
                                    <!-- 上传的文件将在这里显示 -->
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 使用说明 -->
                    <div class="form-group">
                        <label class="form-label">使用说明</label>
                        <textarea class="form-textarea" id="instructions" placeholder="请输入商品的使用方法、注意事项等信息" rows="4"></textarea>
                    </div>
                    
                    <!-- 售后服务 -->
                    <div class="form-group">
                        <label class="form-label">售后服务</label>
                        <div class="service-options">
                            <label class="checkbox-label">
                                <input type="checkbox" id="serviceRefund"> 支持退款
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" id="serviceReplace"> 支持更换
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" id="serviceConsult"> 提供技术支持
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" id="serviceInvoice"> 提供发票
                            </label>
                        </div>
                        <textarea class="form-textarea" id="serviceDescription" placeholder="请详细描述您提供的售后服务内容和政策" rows="3"></textarea>
                    </div>
                    
                    <!-- 提交按钮 -->
                    <div class="form-actions">
                        <button type="button" class="btn btn-outline" id="saveDraft">保存草稿</button>
                        <button type="submit" class="btn btn-primary" id="publishBtn">立即发布</button>
                    </div>
                </form>
            </div>
        </div>
    </main>

    <!-- 底部信息 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-links">
                <div class="link-group">
                    <h4>关于数字鱼</h4>
                    <ul>
                        <li><a href="#">平台介绍</a></li>
                        <li><a href="#">加入我们</a></li>
                        <li><a href="#">联系我们</a></li>
                    </ul>
                </div>
                <div class="link-group">
                    <h4>帮助中心</h4>
                    <ul>
                        <li><a href="#">新手指南</a></li>
                        <li><a href="#">常见问题</a></li>
                        <li><a href="#">意见反馈</a></li>
                    </ul>
                </div>
                <div class="link-group">
                    <h4>交易保障</h4>
                    <ul>
                        <li><a href="#">平台规则</a></li>
                        <li><a href="#">安全交易</a></li>
                        <li><a href="#">投诉举报</a></li>
                    </ul>
                </div>
                <div class="link-group">
                    <h4>特色服务</h4>
                    <ul>
                        <li><a href="#">官方认证</a></li>
                        <li><a href="#">即时交付</a></li>
                        <li><a href="#">优质商家</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="copyright">
                    <p>© 2023 数字鱼 版权所有</p>
                </div>
                <div class="footer-info">
                    <a href="#">隐私政策</a>
                    <a href="#">用户协议</a>
                    <a href="#">营业执照</a>
                </div>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script src="js/publish.js"></script>
</body>
</html>