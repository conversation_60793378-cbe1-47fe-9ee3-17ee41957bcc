<?php
// 先处理所有逻辑，再包含header
require_once 'includes/functions.php';

// 检查登录状态
if (!isLoggedIn()) {
    $_SESSION['error_message'] = '请先登录';
    redirect('login.php');
}

$currentUser = getCurrentUser();
$error = '';
$success = '';

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'update_profile':
            $nickname = sanitizeInput($_POST['nickname'] ?? '');
            $email = sanitizeInput($_POST['email'] ?? '');
            $phone = sanitizeInput($_POST['phone'] ?? '');
            $bio = sanitizeInput($_POST['bio'] ?? '');
            
            if (empty($nickname) || empty($email)) {
                $error = '昵称和邮箱不能为空';
            } else {
                try {
                    // 检查邮箱是否被其他用户使用
                    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
                    $stmt->execute([$email, $_SESSION['user_id']]);
                    if ($stmt->fetch()) {
                        $error = '该邮箱已被其他用户使用';
                    } else {
                        // 更新用户信息
                        $stmt = $pdo->prepare("UPDATE users SET nickname = ?, email = ?, phone = ?, bio = ? WHERE id = ?");
                        $stmt->execute([$nickname, $email, $phone, $bio, $_SESSION['user_id']]);
                        $success = '个人信息更新成功';
                        $currentUser = getCurrentUser(); // 重新获取用户信息
                    }
                } catch (Exception $e) {
                    $error = '更新失败：' . $e->getMessage();
                }
            }
            break;
            
        case 'change_password':
            $currentPassword = $_POST['current_password'] ?? '';
            $newPassword = $_POST['new_password'] ?? '';
            $confirmPassword = $_POST['confirm_password'] ?? '';
            
            if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
                $error = '所有密码字段都不能为空';
            } elseif ($newPassword !== $confirmPassword) {
                $error = '新密码和确认密码不匹配';
            } elseif (strlen($newPassword) < 6) {
                $error = '新密码长度至少6位';
            } else {
                // 验证当前密码
                if (password_verify($currentPassword, $currentUser['password'])) {
                    try {
                        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                        $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
                        $stmt->execute([$hashedPassword, $_SESSION['user_id']]);
                        $success = '密码修改成功';
                    } catch (Exception $e) {
                        $error = '密码修改失败：' . $e->getMessage();
                    }
                } else {
                    $error = '当前密码不正确';
                }
            }
            break;
            
        case 'upload_avatar':
            if (isset($_FILES['avatar']) && $_FILES['avatar']['error'] === UPLOAD_ERR_OK) {
                $uploadedFile = uploadFile($_FILES['avatar'], ['jpg', 'jpeg', 'png']);
                if ($uploadedFile) {
                    try {
                        $avatarPath = UPLOAD_PATH . $uploadedFile;
                        $stmt = $pdo->prepare("UPDATE users SET avatar = ? WHERE id = ?");
                        $stmt->execute([$avatarPath, $_SESSION['user_id']]);
                        $success = '头像上传成功';
                        $currentUser = getCurrentUser(); // 重新获取用户信息
                    } catch (Exception $e) {
                        $error = '头像上传失败：' . $e->getMessage();
                    }
                } else {
                    $error = '头像上传失败，请检查文件格式和大小';
                }
            } else {
                $error = '请选择要上传的头像文件';
            }
            break;
    }
}

// 现在包含header，因为所有逻辑处理已完成
$pageTitle = '账号设置';
$additionalCSS = ['css/member.css', 'css/settings.css'];
require_once 'includes/header.php';
?>

<!-- 主要内容区 -->
<main class="main member-page">
    <div class="container">
        <div class="member-container">
            <!-- 侧边栏 -->
            <?php include 'includes/member-sidebar.php'; ?>
            
            <!-- 主内容区 -->
            <div class="member-content">
                <!-- 页面标题 -->
                <div class="page-header">
                    <div class="page-title-section">
                        <h1 class="page-title">账号设置</h1>
                        <p class="page-subtitle">管理您的个人信息和账号安全</p>
                    </div>
                </div>

                <?php if ($error): ?>
                    <div class="alert alert-error"><?php echo $error; ?></div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="alert alert-success"><?php echo $success; ?></div>
                <?php endif; ?>

                <!-- 设置标签 -->
                <div class="settings-tabs">
                    <button class="tab-btn active" data-tab="profile">
                        <i class="bi bi-person"></i>
                        <span>个人信息</span>
                    </button>
                    <button class="tab-btn" data-tab="security">
                        <i class="bi bi-shield-lock"></i>
                        <span>安全设置</span>
                    </button>
                    <button class="tab-btn" data-tab="avatar">
                        <i class="bi bi-image"></i>
                        <span>头像设置</span>
                    </button>
                </div>

                <!-- 设置内容 -->
                <div class="settings-content">
                    <!-- 个人信息设置 -->
                    <div class="tab-panel active" id="profile">
                        <div class="settings-card">
                            <div class="card-header">
                                <h3>个人信息</h3>
                                <p>更新您的基本信息</p>
                            </div>
                            <form method="POST" class="settings-form">
                                <input type="hidden" name="action" value="update_profile">
                                
                                <div class="form-group">
                                    <label class="form-label">昵称</label>
                                    <input type="text" class="form-input" name="nickname" 
                                           value="<?php echo htmlspecialchars($currentUser['nickname']); ?>" 
                                           placeholder="请输入昵称" required>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">邮箱</label>
                                    <input type="email" class="form-input" name="email" 
                                           value="<?php echo htmlspecialchars($currentUser['email']); ?>" 
                                           placeholder="请输入邮箱" required>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">手机号</label>
                                    <input type="tel" class="form-input" name="phone" 
                                           value="<?php echo htmlspecialchars($currentUser['phone'] ?? ''); ?>" 
                                           placeholder="请输入手机号">
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">个人简介</label>
                                    <textarea class="form-textarea" name="bio" rows="4" 
                                              placeholder="介绍一下自己吧..."><?php echo htmlspecialchars($currentUser['bio'] ?? ''); ?></textarea>
                                </div>
                                
                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check"></i>
                                        <span>保存更改</span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 安全设置 -->
                    <div class="tab-panel" id="security">
                        <div class="settings-card">
                            <div class="card-header">
                                <h3>修改密码</h3>
                                <p>定期更换密码以保护账号安全</p>
                            </div>
                            <form method="POST" class="settings-form">
                                <input type="hidden" name="action" value="change_password">
                                
                                <div class="form-group">
                                    <label class="form-label">当前密码</label>
                                    <input type="password" class="form-input" name="current_password" 
                                           placeholder="请输入当前密码" required>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">新密码</label>
                                    <input type="password" class="form-input" name="new_password" 
                                           placeholder="请输入新密码（至少6位）" required>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">确认新密码</label>
                                    <input type="password" class="form-input" name="confirm_password" 
                                           placeholder="请再次输入新密码" required>
                                </div>
                                
                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-shield-check"></i>
                                        <span>修改密码</span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 头像设置 -->
                    <div class="tab-panel" id="avatar">
                        <div class="settings-card">
                            <div class="card-header">
                                <h3>头像设置</h3>
                                <p>上传您的个人头像</p>
                            </div>
                            <div class="avatar-section">
                                <div class="current-avatar">
                                    <img src="<?php echo $currentUser['avatar'] ?: 'images/avatar-default.svg'; ?>" 
                                         alt="当前头像" id="currentAvatarImg">
                                    <div class="avatar-info">
                                        <h4>当前头像</h4>
                                        <p>支持 JPG、PNG 格式，文件大小不超过 2MB</p>
                                    </div>
                                </div>
                                
                                <form method="POST" enctype="multipart/form-data" class="avatar-upload-form">
                                    <input type="hidden" name="action" value="upload_avatar">
                                    <div class="upload-area" onclick="document.getElementById('avatarFile').click()">
                                        <i class="bi bi-cloud-upload"></i>
                                        <span>点击选择新头像</span>
                                        <input type="file" id="avatarFile" name="avatar" accept="image/*" style="display: none;">
                                    </div>
                                    <button type="submit" class="btn btn-primary" style="display: none;" id="uploadBtn">
                                        <i class="bi bi-upload"></i>
                                        <span>上传头像</span>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 标签切换功能
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabPanels = document.querySelectorAll('.tab-panel');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const tabId = this.dataset.tab;
            
            // 更新按钮状态
            tabBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            // 更新面板显示
            tabPanels.forEach(panel => {
                panel.classList.remove('active');
                if (panel.id === tabId) {
                    panel.classList.add('active');
                }
            });
        });
    });
    
    // 头像上传预览
    const avatarFile = document.getElementById('avatarFile');
    const currentAvatarImg = document.getElementById('currentAvatarImg');
    const uploadBtn = document.getElementById('uploadBtn');
    
    if (avatarFile) {
        avatarFile.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    currentAvatarImg.src = e.target.result;
                    uploadBtn.style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
        });
    }
});
</script>

<?php require_once 'includes/footer.php'; ?>
