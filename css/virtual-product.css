/* 虚拟商品样式 */

/* 虚拟商品卡片 */
.virtual-product {
    position: relative;
    transition: transform 0.3s, box-shadow 0.3s;
}

.virtual-product:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* 即时交付标签 */
.delivery-tag {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background-color: rgba(0, 0, 0, 0.6);
    color: #fff;
    font-size: 12px;
    padding: 3px 8px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.delivery-tag i {
    color: #ffcc00;
}

/* 虚拟商品属性 */
.virtual-attrs {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin: 8px 0;
}

.attr-item {
    font-size: 12px;
    color: #666;
    background-color: #f5f5f5;
    padding: 2px 8px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.attr-item i {
    font-size: 12px;
    color: #ff6f06;
}

/* Banner内容 */
.banner {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
}

.banner-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0 50px;
    background: linear-gradient(90deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.4) 50%, rgba(0,0,0,0) 100%);
}

.banner-content h2 {
    color: #fff;
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 15px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.banner-content p {
    color: #fff;
    font-size: 18px;
    margin-bottom: 25px;
    max-width: 500px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.banner-btn {
    display: inline-block;
    background-color: #ff6f06;
    color: #fff;
    padding: 10px 25px;
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.3s;
    text-decoration: none;
    box-shadow: 0 4px 8px rgba(255, 111, 6, 0.3);
}

.banner-btn:hover {
    background-color: #ff5500;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(255, 111, 6, 0.4);
}

/* 特色服务 */
.feature-services {
    display: flex;
    justify-content: space-between;
    margin: 20px 0;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.feature-item i {
    font-size: 20px;
    color: #ff6f06;
}

.feature-item span {
    font-size: 14px;
    color: #333;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .banner-content {
        padding: 0 20px;
    }
    
    .banner-content h2 {
        font-size: 24px;
    }
    
    .banner-content p {
        font-size: 14px;
    }
    
    .feature-services {
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .feature-item {
        width: 48%;
        margin-bottom: 10px;
    }
}