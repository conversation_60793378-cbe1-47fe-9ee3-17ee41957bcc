/* 首页优化样式 */

/* 英雄横幅区域 */
.hero-banner {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    margin-bottom: 60px;
    overflow: hidden;
    position: relative;
}

.hero-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.banner-content {
    display: flex;
    align-items: center;
    padding: 80px 40px;
    gap: 60px;
    position: relative;
    z-index: 1;
}

.banner-text {
    flex: 1;
    color: white;
}

.hero-title {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
    background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 20px;
    margin-bottom: 30px;
    opacity: 0.9;
    line-height: 1.6;
}

.hero-features {
    display: flex;
    gap: 20px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.feature-tag {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.2);
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 14px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.feature-tag i {
    font-size: 16px;
}

.hero-actions {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.btn-large {
    padding: 15px 30px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-primary.btn-large {
    background: linear-gradient(135deg, #ff6f06, #ff8533);
    border: none;
    box-shadow: 0 8px 25px rgba(255, 111, 6, 0.3);
}

.btn-primary.btn-large:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255, 111, 6, 0.4);
}

.btn-outline.btn-large {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    backdrop-filter: blur(10px);
}

.btn-outline.btn-large:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-3px);
}

.banner-image {
    flex: 0 0 400px;
    text-align: center;
}

.hero-img {
    max-width: 100%;
    height: auto;
    filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.2));
}

/* 热门分类 */
.trending-categories {
    margin-bottom: 80px;
}

.section-header {
    text-align: center;
    margin-bottom: 50px;
}

.section-header h2 {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 15px;
    color: #333;
}

.section-header p {
    font-size: 18px;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

.category-card {
    background: white;
    border-radius: 16px;
    padding: 40px 30px;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #f0f0f0;
    text-decoration: none;
    color: inherit;
}

.category-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border-color: #ff6f06;
}

.category-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #ff6f06, #ff8533);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    transition: all 0.3s ease;
}

.category-card:hover .category-icon {
    transform: scale(1.1);
}

.category-icon i {
    font-size: 32px;
    color: white;
}

.category-card h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
}

.category-card p {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
    line-height: 1.5;
}

.category-count {
    display: inline-block;
    background: #f8f9fa;
    color: #666;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

/* 平台优势 */
.platform-advantages {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 16px;
    padding: 60px 40px;
    margin-bottom: 80px;
}

.advantages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
}

.advantage-item {
    text-align: center;
}

.advantage-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    transition: all 0.3s ease;
}

.advantage-item:hover .advantage-icon {
    transform: scale(1.1) rotate(5deg);
}

.advantage-icon i {
    font-size: 32px;
    color: white;
}

.advantage-item h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
}

.advantage-item p {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
}

/* 热门商品推荐 */
.featured-products {
    margin-bottom: 80px;
}

.section-header .more-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #ff6f06;
    font-weight: 500;
    margin-top: 20px;
    transition: all 0.3s ease;
}

.section-header .more-link:hover {
    transform: translateX(5px);
}

.homepage-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 30px;
}

.enhanced-card {
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #f0f0f0;
}

.enhanced-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.product-link {
    display: block;
    text-decoration: none;
    color: inherit;
}

.enhanced-card .product-img {
    position: relative;
    overflow: hidden;
    height: 200px;
}

.enhanced-card .product-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.enhanced-card:hover .product-img img {
    transform: scale(1.1);
}

.product-badges {
    position: absolute;
    top: 15px;
    left: 15px;
    z-index: 2;
}

.badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    margin-bottom: 8px;
}

.instant-delivery {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.enhanced-card:hover .product-overlay {
    opacity: 1;
}

.quick-view {
    color: white;
    font-weight: 500;
    padding: 10px 20px;
    border: 2px solid white;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.quick-view:hover {
    background: white;
    color: #333;
}

.enhanced-card .product-info {
    padding: 20px;
}

.enhanced-card .product-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    line-height: 1.4;
    height: auto;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.enhanced-card .product-price {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.enhanced-card .current-price {
    font-size: 20px;
    font-weight: 700;
    color: #ff6f06;
}

.enhanced-card .original-price {
    font-size: 14px;
    color: #999;
    text-decoration: line-through;
}

.discount-badge {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
}

.enhanced-card .product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.enhanced-card .seller-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.enhanced-card .seller-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    object-fit: cover;
}

.enhanced-card .seller-name {
    font-size: 13px;
    color: #666;
    font-weight: 500;
}

.product-stats {
    display: flex;
    gap: 15px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #999;
}

.stat-item i {
    font-size: 14px;
}

/* 数据统计 */
.platform-stats {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 60px 40px;
    margin-bottom: 80px;
    position: relative;
    overflow: hidden;
}

.platform-stats::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="stats-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23stats-pattern)"/></svg>');
    pointer-events: none;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    position: relative;
    z-index: 1;
}

.platform-stats .stat-item {
    text-align: center;
    color: white;
}

.stat-number {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 10px;
    background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    font-size: 16px;
    opacity: 0.9;
    font-weight: 500;
}

/* 热门社区 */
.community-section {
    margin-bottom: 80px;
}

.community-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.community-card {
    background: white;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
}

.community-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.community-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
}

.community-avatar {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ff6f06, #ff8533);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.community-avatar i {
    font-size: 24px;
    color: white;
}

.community-info h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 5px;
    color: #333;
}

.community-info p {
    font-size: 14px;
    color: #666;
    line-height: 1.4;
}

.community-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.member-count,
.product-count {
    font-size: 13px;
    color: #666;
    background: #f8f9fa;
    padding: 6px 12px;
    border-radius: 15px;
}

.community-preview {
    display: flex;
    gap: 8px;
    margin-bottom: 25px;
    align-items: center;
}

.community-preview img {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    object-fit: cover;
    border: 2px solid #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.more-indicator {
    width: 40px;
    height: 40px;
    background: #f8f9fa;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #666;
    font-weight: 500;
    border: 2px solid #fff;
}

.join-btn {
    width: 100%;
    padding: 12px;
    background: linear-gradient(135deg, #ff6f06, #ff8533);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.join-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 111, 6, 0.3);
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .banner-content {
        padding: 60px 30px;
        gap: 40px;
    }

    .hero-title {
        font-size: 36px;
    }

    .hero-subtitle {
        font-size: 18px;
    }

    .category-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }

    .homepage-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }
}

@media (max-width: 768px) {
    .banner-content {
        flex-direction: column;
        text-align: center;
        padding: 40px 20px;
        gap: 30px;
    }

    .banner-image {
        flex: none;
        order: -1;
    }

    .hero-title {
        font-size: 28px;
    }

    .hero-subtitle {
        font-size: 16px;
    }

    .hero-features {
        justify-content: center;
    }

    .hero-actions {
        justify-content: center;
    }

    .section-header h2 {
        font-size: 28px;
    }

    .section-header p {
        font-size: 16px;
    }

    .category-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .category-card {
        padding: 30px 20px;
    }

    .platform-advantages {
        padding: 40px 20px;
    }

    .advantages-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 30px;
    }

    .homepage-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 20px;
    }

    .platform-stats {
        padding: 40px 20px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }

    .stat-number {
        font-size: 36px;
    }

    .community-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .community-card {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 24px;
    }

    .hero-subtitle {
        font-size: 14px;
    }

    .btn-large {
        padding: 12px 24px;
        font-size: 14px;
    }

    .hero-features {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }

    .section-header h2 {
        font-size: 24px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .stat-number {
        font-size: 28px;
    }

    .homepage-grid {
        grid-template-columns: 1fr;
    }

    .enhanced-card .product-info {
        padding: 15px;
    }
}
