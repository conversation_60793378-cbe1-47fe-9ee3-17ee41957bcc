/* 用户中心样式 - 数字鱼虚拟商品交易平台 */

/* 页面布局 */
.member-page {
    background-color: #f8f9fa;
    min-height: calc(100vh - 80px);
    padding: 30px 0;
}

.member-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    gap: 30px;
}

/* 侧边栏样式 */
.member-sidebar {
    width: 280px;
    flex-shrink: 0;
}

/* 用户信息卡片 */
.user-profile-card {
    background: white;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.user-avatar-section {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 20px;
}

.user-avatar {
    position: relative;
    width: 64px;
    height: 64px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid #fff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-edit {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, #ff6f06, #ff8533);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid white;
}

.avatar-edit:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(255, 111, 6, 0.3);
}

.user-info {
    flex: 1;
}

.user-name {
    font-size: 18px;
    font-weight: 600;
    color: #212529;
    margin-bottom: 8px;
}

.user-level {
    display: flex;
    align-items: center;
    gap: 8px;
}

.level-badge {
    display: flex;
    align-items: center;
    gap: 4px;
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.level-text {
    font-size: 12px;
    color: #6c757d;
}

.user-stats {
    display: flex;
    justify-content: space-between;
    padding-top: 16px;
    border-top: 1px solid #f1f3f4;
}

.stat-item {
    text-align: center;
    flex: 1;
}

.stat-value {
    font-size: 18px;
    font-weight: 700;
    color: #ff6f06;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: #6c757d;
}

/* 侧边栏菜单 */
.sidebar-menu {
    background: white;
    border-radius: 16px;
    padding: 20px 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.menu-group {
    margin-bottom: 24px;
}

.menu-group:last-child {
    margin-bottom: 0;
}

.menu-title {
    font-size: 14px;
    font-weight: 600;
    color: #6c757d;
    padding: 0 20px 12px;
    border-bottom: 1px solid #f1f3f4;
    margin-bottom: 8px;
}

.menu-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.menu-list li {
    margin: 0;
}

.menu-list a {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    color: #495057;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
    position: relative;
}

.menu-list a:hover {
    background-color: #f8f9fa;
    color: #ff6f06;
}

.menu-list li.active a {
    background: linear-gradient(135deg, #fff2e8, #fff8f5);
    color: #ff6f06;
    font-weight: 600;
    border-right: 3px solid #ff6f06;
}

.menu-list a i {
    width: 18px;
    font-size: 16px;
    text-align: center;
}

.menu-badge {
    background: #dc3545;
    color: white;
    font-size: 11px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: auto;
}

.logout-item a {
    color: #dc3545;
}

.logout-item a:hover {
    background-color: #fff5f5;
    color: #dc3545;
}

/* 主内容区 */
.member-content {
    flex: 1;
    min-width: 0;
}

/* 页面头部 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.page-title-section h1 {
    font-size: 28px;
    font-weight: 700;
    color: #212529;
    margin: 0 0 8px 0;
}

.page-subtitle {
    font-size: 16px;
    color: #6c757d;
    margin: 0;
}

.page-actions .btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    font-weight: 600;
}

/* 统计卡片 */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 16px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, #6c757d, #495057);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.stat-icon.active {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #212529;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    color: #6c757d;
}

/* 筛选标签 */
.filter-tabs {
    display: flex;
    gap: 8px;
    margin-bottom: 30px;
    background: white;
    padding: 8px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-tab {
    padding: 12px 20px;
    border-radius: 8px;
    text-decoration: none;
    color: #6c757d;
    font-weight: 500;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.filter-tab:hover {
    background-color: #f8f9fa;
    color: #495057;
}

.filter-tab.active {
    background: linear-gradient(135deg, #ff6f06, #ff8533);
    color: white;
    box-shadow: 0 2px 8px rgba(255, 111, 6, 0.3);
}

/* 商品网格 */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
}

.product-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.product-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.product-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-status {
    position: absolute;
    top: 12px;
    right: 12px;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    color: white;
}

.status-active {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.status-inactive {
    background: linear-gradient(135deg, #6c757d, #495057);
}

.status-draft {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.product-info {
    padding: 20px;
}

.product-title {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
}

.product-title a {
    color: #212529;
    text-decoration: none;
}

.product-title a:hover {
    color: #ff6f06;
}

.product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.product-category {
    font-size: 12px;
    color: #6c757d;
    background-color: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
}

.product-price {
    font-size: 18px;
    font-weight: 700;
    color: #ff6f06;
}

.product-stats {
    display: flex;
    gap: 16px;
    margin-bottom: 12px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 13px;
    color: #6c757d;
}

.stat-item i {
    font-size: 14px;
}

.product-time {
    font-size: 12px;
    color: #adb5bd;
}

.product-actions {
    display: flex;
    gap: 8px;
    padding: 16px 20px;
    border-top: 1px solid #f1f3f4;
    background-color: #f8f9fa;
}

.action-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    background: white;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    color: #495057;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

.edit-btn:hover {
    background-color: #d1ecf1;
    border-color: #17a2b8;
    color: #17a2b8;
}

.toggle-btn:hover {
    background-color: #d4edda;
    border-color: #28a745;
    color: #28a745;
}

.delete-btn:hover {
    background-color: #f8d7da;
    border-color: #dc3545;
    color: #dc3545;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-icon {
    font-size: 64px;
    color: #dee2e6;
    margin-bottom: 20px;
}

.empty-state h3 {
    font-size: 20px;
    color: #495057;
    margin-bottom: 12px;
}

.empty-state p {
    font-size: 16px;
    color: #6c757d;
    margin-bottom: 24px;
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 40px;
}

.page-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px solid #dee2e6;
    background: white;
    border-radius: 8px;
    color: #495057;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.page-btn:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
}

.page-btn.active {
    background: linear-gradient(135deg, #ff6f06, #ff8533);
    border-color: #ff6f06;
    color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .member-container {
        flex-direction: column;
        padding: 0 15px;
        gap: 20px;
    }

    .member-sidebar {
        width: 100%;
        order: 2;
    }

    .member-content {
        order: 1;
    }

    .user-avatar-section {
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }

    .user-avatar {
        width: 56px;
        height: 56px;
    }

    .user-stats {
        justify-content: space-around;
    }

    .sidebar-menu {
        display: none;
    }

    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 20px;
    }

    .stats-cards {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }

    .filter-tabs {
        flex-wrap: wrap;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .product-actions {
        flex-wrap: wrap;
    }

    .action-btn {
        min-width: 80px;
    }
}

@media (max-width: 480px) {
    .member-container {
        gap: 15px;
    }

    .user-profile-card {
        padding: 20px;
    }

    .user-avatar {
        width: 48px;
        height: 48px;
    }

    .user-name {
        font-size: 16px;
    }

    .stats-cards {
        grid-template-columns: 1fr;
    }

    .stat-card {
        padding: 20px;
    }

    .product-info {
        padding: 16px;
    }

    .product-actions {
        padding: 12px 16px;
    }
}
