/* 钱包样式 - 数字鱼虚拟商品交易平台 */

/* 钱包概览 */
.wallet-overview {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
    margin-bottom: 32px;
}

/* 余额卡片 */
.balance-card {
    background: linear-gradient(135deg, #ff6f06, #ff8533);
    color: white;
    padding: 32px;
    border-radius: 16px;
    box-shadow: 0 8px 24px rgba(255, 111, 6, 0.3);
}

.balance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.balance-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    opacity: 0.9;
}

.balance-header i {
    font-size: 24px;
    opacity: 0.8;
}

.balance-amount {
    margin-bottom: 32px;
}

.balance-amount .currency {
    font-size: 24px;
    font-weight: 300;
    opacity: 0.8;
    margin-right: 4px;
}

.balance-amount .amount {
    font-size: 48px;
    font-weight: 700;
    line-height: 1;
}

.balance-actions {
    display: flex;
    gap: 12px;
}

.balance-actions .btn {
    flex: 1;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    backdrop-filter: blur(10px);
}

.balance-actions .btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
}

/* 冻结金额卡片 */
.frozen-card {
    background: white;
    padding: 32px;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #f1f3f4;
}

.frozen-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.frozen-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    color: #333;
}

.frozen-header i {
    font-size: 24px;
    color: #6c757d;
}

.frozen-amount {
    margin-bottom: 16px;
}

.frozen-amount .currency {
    font-size: 18px;
    font-weight: 300;
    color: #6c757d;
    margin-right: 4px;
}

.frozen-amount .amount {
    font-size: 32px;
    font-weight: 700;
    color: #333;
    line-height: 1;
}

.frozen-note {
    color: #6c757d;
    font-size: 13px;
}

/* 交易记录部分 */
.transactions-section {
    background: white;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f1f3f4;
}

.section-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

.section-actions .form-select {
    min-width: 120px;
}

/* 交易记录列表 */
.transactions-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.transaction-item {
    display: flex;
    align-items: center;
    padding: 20px;
    background: #fafbfc;
    border-radius: 12px;
    border: 1px solid #f1f3f4;
    transition: all 0.3s ease;
}

.transaction-item:hover {
    background: #f8f9fa;
    border-color: #e9ecef;
}

.transaction-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.transaction-icon i {
    font-size: 20px;
}

.transaction-info {
    flex: 1;
    margin-right: 16px;
}

.transaction-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.transaction-desc {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 4px;
}

.transaction-time {
    font-size: 13px;
    color: #adb5bd;
}

.transaction-amount {
    text-align: right;
    margin-right: 16px;
}

.transaction-amount .amount {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 4px;
    display: block;
}

.transaction-amount .amount.positive {
    color: #28a745;
}

.transaction-amount .amount.negative {
    color: #dc3545;
}

.balance-after {
    font-size: 13px;
    color: #6c757d;
}

.transaction-status {
    width: 80px;
}

.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    width: 100%;
}

.status-success {
    background: #d4edda;
    color: #155724;
}

.status-warning {
    background: #fff3cd;
    color: #856404;
}

.status-danger {
    background: #f8d7da;
    color: #721c24;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 16px;
    width: 90%;
    max-width: 480px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 24px 0 24px;
    margin-bottom: 24px;
}

.modal-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: #f8f9fa;
    color: #333;
}

.modal-body {
    padding: 0 24px 24px 24px;
}

.amount-input {
    position: relative;
    display: flex;
    align-items: center;
}

.amount-input .currency {
    position: absolute;
    left: 16px;
    font-size: 18px;
    font-weight: 600;
    color: #6c757d;
    z-index: 1;
}

.amount-input .form-input {
    padding-left: 40px;
    font-size: 18px;
    font-weight: 600;
}

.quick-amounts {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    margin-top: 16px;
}

.quick-amount {
    padding: 12px;
    border: 1px solid #e9ecef;
    background: white;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.3s ease;
}

.quick-amount:hover {
    border-color: #ff6f06;
    color: #ff6f06;
    background: #fff2e8;
}

.modal-actions {
    display: flex;
    gap: 12px;
    margin-top: 24px;
}

.modal-actions .btn {
    flex: 1;
}

.withdraw-note {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid #ff6f06;
}

.withdraw-note p {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #6c757d;
}

.withdraw-note p:last-child {
    margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .wallet-overview {
        grid-template-columns: 1fr;
        gap: 16px;
        margin-bottom: 24px;
    }

    .balance-card,
    .frozen-card {
        padding: 24px;
    }

    .balance-amount .amount {
        font-size: 36px;
    }

    .frozen-amount .amount {
        font-size: 24px;
    }

    .transaction-item {
        padding: 16px;
    }

    .transaction-icon {
        width: 40px;
        height: 40px;
        margin-right: 12px;
    }

    .transaction-icon i {
        font-size: 18px;
    }

    .transaction-amount .amount {
        font-size: 16px;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
    }

    .quick-amounts {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .section-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .transaction-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .transaction-amount {
        margin-right: 0;
        text-align: left;
        width: 100%;
    }

    .transaction-status {
        width: auto;
    }
}
