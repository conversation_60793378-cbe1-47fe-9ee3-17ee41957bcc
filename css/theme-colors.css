/* 主题色彩 - 数字鱼虚拟商品交易平台 */

:root {
    /* 主色调 */
    --primary-color: #ff6f06;
    --primary-light: #ff8c3f;
    --primary-dark: #e55d00;
    --primary-gradient: linear-gradient(135deg, #ff7e1a, #ff5500);
    
    /* 辅助色 */
    --secondary-color: #3498db;
    --secondary-light: #5dade2;
    --secondary-dark: #2980b9;
    
    /* 中性色 */
    --neutral-100: #ffffff;
    --neutral-200: #f8f9fa;
    --neutral-300: #f0f0f0;
    --neutral-400: #e0e0e0;
    --neutral-500: #cccccc;
    --neutral-600: #999999;
    --neutral-700: #666666;
    --neutral-800: #333333;
    --neutral-900: #1a1a1a;
    
    /* 功能色 */
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #3498db;
    
    /* 背景色 */
    --bg-light: #f8f9fa;
    --bg-dark: #333333;
    --bg-card: #ffffff;
    --bg-feature: #fff8f3;
    
    /* 文本色 */
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-muted: #999999;
    --text-light: #ffffff;
    
    /* 边框色 */
    --border-light: #e0e0e0;
    --border-medium: #cccccc;
    --border-dark: #999999;
    
    /* 阴影 */
    --shadow-sm: 0 2px 5px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 5px 15px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
    --shadow-primary: 0 5px 15px rgba(255, 111, 6, 0.2);
}

/* 应用主题色彩 */
body {
    color: var(--text-primary);
    background-color: var(--bg-light);
}

a {
    color: var(--text-primary);
}

a:hover {
    color: var(--primary-color);
}

/* 按钮样式 */
.btn-primary {
    background: var(--primary-gradient);
    color: var(--neutral-100);
    box-shadow: var(--shadow-primary);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.btn-outline {
    border: 1px solid var(--border-light);
    background-color: var(--neutral-100);
}

.btn-outline:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* 头部导航 */
.header {
    background-color: var(--neutral-100);
    box-shadow: var(--shadow-sm);
}

.logo-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.search-box input {
    border: 1px solid var(--border-light);
}

.search-box input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 111, 6, 0.1);
}

.search-btn {
    color: var(--primary-color);
}

.nav a.active,
.publish-btn {
    color: var(--primary-color);
}

.nav a.active:after,
.nav a:hover:after {
    background-color: var(--primary-color);
}

/* 商品卡片 */
.product-card {
    background-color: var(--bg-card);
    box-shadow: var(--shadow-sm);
}

.product-card:hover {
    box-shadow: var(--shadow-md);
}

.product-tag {
    background: var(--primary-gradient);
    color: var(--neutral-100);
}

.current-price {
    color: var(--primary-dark);
}

.original-price {
    color: var(--neutral-600);
}

.attr-item {
    background-color: var(--neutral-300);
    color: var(--neutral-700);
}

.attr-item:hover {
    background-color: var(--bg-feature);
}

.attr-item i {
    color: var(--primary-color);
}

/* Banner */
.banner {
    box-shadow: var(--shadow-md);
}

.banner-content h2,
.banner-content p {
    color: var(--neutral-100);
}

.banner-btn {
    background: var(--primary-gradient);
    color: var(--neutral-100);
    box-shadow: var(--shadow-primary);
}

.banner-btn:hover {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

/* 特色服务 */
.feature-services {
    background-color: var(--bg-card);
    box-shadow: var(--shadow-sm);
}

.feature-item:hover {
    background-color: var(--bg-feature);
}

.feature-item i {
    color: var(--primary-color);
    background-color: var(--bg-feature);
}

.feature-item:hover i {
    background-color: var(--primary-color);
    color: var(--neutral-100);
}

/* 发布页面 */
.publish-container {
    background-color: var(--bg-card);
    box-shadow: var(--shadow-md);
}

.page-title {
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-light);
}

.form-label {
    color: var(--text-primary);
}

.form-input,
.form-select,
.form-textarea {
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 111, 6, 0.1);
}

.input-tips {
    color: var(--text-muted);
}

/* 商品类型选择器 */
.type-option {
    border: 1px solid var(--border-light);
    background-color: var(--neutral-200);
}

.type-option:hover {
    background-color: var(--bg-feature);
    border-color: var(--primary-color);
}

.type-option input[type="radio"] {
    accent-color: var(--primary-color);
}

/* 图片上传 */
.add-image {
    border: 2px dashed var(--border-light);
    background-color: var(--neutral-200);
}

.add-image:hover {
    border-color: var(--primary-color);
    background-color: var(--bg-feature);
}

.add-image i {
    color: var(--primary-color);
}

/* 底部信息 */
.footer {
    background-color: var(--bg-card);
    border-top: 1px solid var(--border-light);
}

.link-group h4 {
    color: var(--text-primary);
}

.link-group h4:after {
    background-color: var(--primary-color);
}

.link-group ul li a {
    color: var(--text-secondary);
}

.link-group ul li a:hover {
    color: var(--primary-color);
}

.copyright, 
.footer-info,
.footer-info a {
    color: var(--text-muted);
}

.footer-info a:hover {
    color: var(--primary-color);
}

/* 响应式调整 */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-light: #1a1a1a;
        --bg-card: #2a2a2a;
        --bg-feature: #333333;
        
        --text-primary: #f0f0f0;
        --text-secondary: #cccccc;
        --text-muted: #999999;
        
        --border-light: #444444;
        --border-medium: #555555;
        
        --shadow-sm: 0 2px 5px rgba(0, 0, 0, 0.2);
        --shadow-md: 0 5px 15px rgba(0, 0, 0, 0.3);
        --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.4);
    }
}