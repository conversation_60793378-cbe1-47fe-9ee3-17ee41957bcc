/* 设置页面样式 - 数字鱼虚拟商品交易平台 */

/* 设置标签 */
.settings-tabs {
    display: flex;
    gap: 8px;
    margin-bottom: 30px;
    background: white;
    padding: 8px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    background: transparent;
    border-radius: 8px;
    color: #6c757d;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.tab-btn:hover {
    background-color: #f8f9fa;
    color: #495057;
}

.tab-btn.active {
    background: linear-gradient(135deg, #ff6f06, #ff8533);
    color: white;
    box-shadow: 0 2px 8px rgba(255, 111, 6, 0.3);
}

.tab-btn i {
    font-size: 16px;
}

/* 设置内容 */
.settings-content {
    position: relative;
}

.tab-panel {
    display: none;
}

.tab-panel.active {
    display: block;
}

/* 设置卡片 */
.settings-card {
    background: white;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.card-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f1f3f4;
}

.card-header h3 {
    font-size: 20px;
    font-weight: 600;
    color: #212529;
    margin: 0 0 8px 0;
}

.card-header p {
    font-size: 14px;
    color: #6c757d;
    margin: 0;
}

/* 表单样式 */
.settings-form {
    max-width: 500px;
}

.form-group {
    margin-bottom: 24px;
}

.form-label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.form-input,
.form-textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #fff;
}

.form-input:focus,
.form-textarea:focus {
    outline: none;
    border-color: #ff6f06;
    box-shadow: 0 0 0 3px rgba(255, 111, 6, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.form-actions {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #f1f3f4;
}

/* 头像设置 */
.avatar-section {
    max-width: 600px;
}

.current-avatar {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
}

.current-avatar img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.avatar-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #212529;
    margin: 0 0 8px 0;
}

.avatar-info p {
    font-size: 13px;
    color: #6c757d;
    margin: 0;
}

.avatar-upload-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.upload-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    border: 2px dashed #dee2e6;
    border-radius: 12px;
    background: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-area:hover {
    border-color: #ff6f06;
    background: #fff2e8;
}

.upload-area i {
    font-size: 32px;
    color: #6c757d;
    margin-bottom: 12px;
}

.upload-area span {
    font-size: 14px;
    color: #495057;
    font-weight: 500;
}

.upload-area:hover i,
.upload-area:hover span {
    color: #ff6f06;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #ff6f06, #ff8533);
    color: white;
    box-shadow: 0 2px 8px rgba(255, 111, 6, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #e55a00, #ff6f06);
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(255, 111, 6, 0.4);
}

.btn i {
    font-size: 16px;
}

/* 警告框样式 */
.alert {
    padding: 16px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 14px;
    font-weight: 500;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .settings-tabs {
        flex-wrap: wrap;
        gap: 4px;
    }
    
    .tab-btn {
        padding: 10px 16px;
        font-size: 13px;
    }
    
    .tab-btn span {
        display: none;
    }
    
    .tab-btn i {
        font-size: 18px;
    }
    
    .settings-card {
        padding: 20px;
    }
    
    .card-header {
        margin-bottom: 20px;
        padding-bottom: 16px;
    }
    
    .card-header h3 {
        font-size: 18px;
    }
    
    .current-avatar {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }
    
    .current-avatar img {
        width: 60px;
        height: 60px;
    }
    
    .upload-area {
        padding: 30px 15px;
    }
    
    .upload-area i {
        font-size: 28px;
    }
}

@media (max-width: 480px) {
    .settings-form {
        max-width: 100%;
    }
    
    .form-input,
    .form-textarea {
        padding: 10px 14px;
        font-size: 13px;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 13px;
    }
    
    .current-avatar {
        padding: 16px;
    }
    
    .upload-area {
        padding: 24px 12px;
    }
}
