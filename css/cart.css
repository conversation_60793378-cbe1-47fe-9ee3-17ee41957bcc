/* 购物车样式 - 数字鱼虚拟商品交易平台 */

/* 购物车容器 */
.cart-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 购物车商品列表 */
.cart-items {
    border-bottom: 1px solid #f1f3f4;
}

/* 购物车商品项 */
.cart-item {
    display: flex;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.3s ease;
}

.cart-item:hover {
    background-color: #fafbfc;
}

.cart-item:last-child {
    border-bottom: none;
}

/* 商品选择框 */
.item-checkbox {
    margin-right: 16px;
}

.item-select {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

/* 商品图片 */
.item-image {
    width: 80px;
    height: 80px;
    margin-right: 16px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 商品信息 */
.item-info {
    flex: 1;
    margin-right: 16px;
}

.item-title {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 500;
}

.item-title a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.item-title a:hover {
    color: #ff6f06;
}

.item-seller {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    color: #6c757d;
}

.item-seller img {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    object-fit: cover;
}

/* 商品价格 */
.item-price {
    width: 100px;
    text-align: center;
    margin-right: 16px;
}

.price {
    font-size: 18px;
    font-weight: 600;
    color: #ff6f06;
}

/* 数量控制 */
.item-quantity {
    width: 120px;
    margin-right: 16px;
}

.quantity-control {
    display: flex;
    align-items: center;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    overflow: hidden;
}

.quantity-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: #f8f9fa;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 500;
}

.quantity-btn:hover {
    background: #e9ecef;
    color: #495057;
}

.quantity-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.quantity-input {
    width: 56px;
    height: 32px;
    border: none;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    outline: none;
}

/* 商品总价 */
.item-total {
    width: 100px;
    text-align: center;
    margin-right: 16px;
}

.total-price {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

/* 商品操作 */
.item-actions {
    width: 40px;
}

.btn-icon {
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    color: #6c757d;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-icon:hover {
    background: #f8f9fa;
    color: #dc3545;
}

/* 购物车底部 */
.cart-footer {
    background: #f8f9fa;
    padding: 20px;
}

.cart-summary {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.select-all {
    display: flex;
    align-items: center;
    gap: 8px;
}

.select-all input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.select-all label {
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    margin: 0;
}

.summary-info {
    display: flex;
    align-items: center;
    gap: 24px;
    font-size: 14px;
}

.selected-count {
    color: #6c757d;
}

.total-amount {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.total-amount span {
    color: #ff6f06;
    font-size: 20px;
}

.btn-large {
    padding: 12px 32px;
    font-size: 16px;
    font-weight: 600;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 80px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-icon {
    font-size: 64px;
    color: #e9ecef;
    margin-bottom: 24px;
}

.empty-title {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
}

.empty-description {
    font-size: 16px;
    color: #6c757d;
    margin-bottom: 32px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .cart-item {
        flex-direction: column;
        align-items: flex-start;
        padding: 16px;
        gap: 12px;
    }

    .item-checkbox {
        margin-right: 0;
        align-self: flex-end;
    }

    .item-image {
        width: 60px;
        height: 60px;
        margin-right: 0;
    }

    .item-info {
        margin-right: 0;
        width: 100%;
    }

    .item-price,
    .item-quantity,
    .item-total {
        width: auto;
        margin-right: 0;
    }

    .cart-summary {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .summary-info {
        justify-content: space-between;
    }

    .btn-large {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .cart-item {
        padding: 12px;
    }

    .item-image {
        width: 50px;
        height: 50px;
    }

    .item-title {
        font-size: 14px;
    }

    .price,
    .total-price {
        font-size: 16px;
    }

    .quantity-control {
        width: 100px;
    }

    .quantity-btn {
        width: 28px;
        height: 28px;
        font-size: 14px;
    }

    .quantity-input {
        width: 44px;
        height: 28px;
        font-size: 13px;
    }

    .summary-info {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }
}
