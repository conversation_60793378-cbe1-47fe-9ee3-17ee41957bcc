/* 动画效果 - 数字鱼虚拟商品交易平台 */

/* 淡入效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* 上浮效果 */
@keyframes floatUp {
    from { 
        opacity: 0;
        transform: translateY(20px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

/* 缩放效果 */
@keyframes scaleIn {
    from { 
        opacity: 0;
        transform: scale(0.9);
    }
    to { 
        opacity: 1;
        transform: scale(1);
    }
}

/* 闪光效果 */
@keyframes shine {
    from {
        background-position: -100% 0;
    }
    to {
        background-position: 200% 0;
    }
}

/* 应用动画到元素 */
.header {
    animation: fadeIn 0.5s ease-out;
}

.banner {
    animation: scaleIn 0.8s ease-out;
}

.banner-content h2,
.banner-content p,
.banner-content .banner-btn {
    opacity: 0;
    animation: floatUp 0.8s ease-out forwards;
}

.banner-content h2 {
    animation-delay: 0.2s;
}

.banner-content p {
    animation-delay: 0.4s;
}

.banner-content .banner-btn {
    animation-delay: 0.6s;
}

.feature-item {
    opacity: 0;
    animation: floatUp 0.6s ease-out forwards;
}

.feature-item:nth-child(1) {
    animation-delay: 0.1s;
}

.feature-item:nth-child(2) {
    animation-delay: 0.2s;
}

.feature-item:nth-child(3) {
    animation-delay: 0.3s;
}

.feature-item:nth-child(4) {
    animation-delay: 0.4s;
}

.product-card {
    opacity: 0;
    animation: scaleIn 0.6s ease-out forwards;
}

.product-grid .product-card:nth-child(1) {
    animation-delay: 0.1s;
}

.product-grid .product-card:nth-child(2) {
    animation-delay: 0.2s;
}

.product-grid .product-card:nth-child(3) {
    animation-delay: 0.3s;
}

.product-grid .product-card:nth-child(4) {
    animation-delay: 0.4s;
}

/* 闪光效果应用到特定元素 */
.product-tag,
.delivery-tag {
    position: relative;
    overflow: hidden;
}

.product-tag::after,
.delivery-tag::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
    background-size: 200% 100%;
    animation: shine 2s infinite linear;
}

/* 悬停动画 */
.nav a,
.category-nav a,
.btn,
.product-card,
.feature-item,
.footer a {
    transition: all 0.3s ease;
}

/* 表单元素动画 */
.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    transition: all 0.3s ease;
}

/* 发布页面动画 */
.publish-container {
    animation: scaleIn 0.6s ease-out;
}

.publish-form .form-group {
    opacity: 0;
    animation: floatUp 0.5s ease-out forwards;
}

.publish-form .form-group:nth-child(1) { animation-delay: 0.1s; }
.publish-form .form-group:nth-child(2) { animation-delay: 0.15s; }
.publish-form .form-group:nth-child(3) { animation-delay: 0.2s; }
.publish-form .form-group:nth-child(4) { animation-delay: 0.25s; }
.publish-form .form-group:nth-child(5) { animation-delay: 0.3s; }
.publish-form .form-group:nth-child(6) { animation-delay: 0.35s; }
.publish-form .form-group:nth-child(7) { animation-delay: 0.4s; }
.publish-form .form-group:nth-child(8) { animation-delay: 0.45s; }
.publish-form .form-group:nth-child(9) { animation-delay: 0.5s; }
.publish-form .form-group:nth-child(10) { animation-delay: 0.55s; }
.publish-form .form-group:nth-child(11) { animation-delay: 0.6s; }
.publish-form .form-group:nth-child(12) { animation-delay: 0.65s; }

.form-actions {
    opacity: 0;
    animation: floatUp 0.5s ease-out 0.7s forwards;
}

/* 响应式调整 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}