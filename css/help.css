/* 帮助中心样式 - 数字鱼虚拟商品交易平台 */

.help-page {
    background-color: #f8f9fa;
    min-height: calc(100vh - 80px);
    padding: 40px 0;
}

.help-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 页面头部 */
.page-header {
    text-align: center;
    margin-bottom: 40px;
}

.page-title {
    font-size: 36px;
    font-weight: 700;
    color: #212529;
    margin: 0 0 12px 0;
}

.page-subtitle {
    font-size: 18px;
    color: #6c757d;
    margin: 0;
}

/* 搜索框 */
.help-search {
    margin-bottom: 40px;
}

.search-box {
    position: relative;
    max-width: 500px;
    margin: 0 auto;
}

.search-box i {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 18px;
}

.search-box input {
    width: 100%;
    padding: 16px 16px 16px 50px;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    font-size: 16px;
    background: white;
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #ff6f06;
    box-shadow: 0 0 0 3px rgba(255, 111, 6, 0.1);
}

/* 快速导航 */
.quick-nav {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 24px;
    margin-bottom: 50px;
}

.nav-item {
    background: white;
    border-radius: 16px;
    padding: 30px 24px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
}

.nav-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.nav-icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, #ff6f06, #ff8533);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 28px;
}

.nav-item h3 {
    font-size: 18px;
    font-weight: 600;
    color: #212529;
    margin: 0 0 8px 0;
}

.nav-item p {
    font-size: 14px;
    color: #6c757d;
    margin: 0;
}

/* 帮助内容 */
.help-content {
    background: white;
    border-radius: 16px;
    padding: 40px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 40px;
}

.help-section {
    margin-bottom: 40px;
}

.help-section:last-child {
    margin-bottom: 0;
}

.help-section h2 {
    font-size: 24px;
    font-weight: 700;
    color: #212529;
    margin: 0 0 24px 0;
    padding-bottom: 12px;
    border-bottom: 2px solid #f1f3f4;
}

/* FAQ 列表 */
.faq-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.faq-item {
    border: 1px solid #e9ecef;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item:hover {
    border-color: #ff6f06;
    box-shadow: 0 2px 8px rgba(255, 111, 6, 0.1);
}

.faq-question {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    background: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s ease;
}

.faq-question:hover {
    background: #fff2e8;
}

.faq-question h3 {
    font-size: 16px;
    font-weight: 600;
    color: #212529;
    margin: 0;
    flex: 1;
}

.faq-question i {
    font-size: 14px;
    color: #6c757d;
    transition: transform 0.3s ease;
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.faq-answer p {
    padding: 20px 24px;
    margin: 0;
    font-size: 14px;
    line-height: 1.6;
    color: #495057;
}

.faq-item.open .faq-answer {
    max-height: 200px;
}

/* 联系客服 */
.contact-section {
    margin-top: 40px;
}

.contact-card {
    background: linear-gradient(135deg, #ff6f06, #ff8533);
    border-radius: 16px;
    padding: 40px;
    color: white;
    text-align: center;
}

.contact-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    font-size: 36px;
}

.contact-content h3 {
    font-size: 24px;
    font-weight: 700;
    margin: 0 0 12px 0;
}

.contact-content p {
    font-size: 16px;
    margin: 0 0 24px 0;
    opacity: 0.9;
}

.contact-methods {
    display: flex;
    justify-content: center;
    gap: 16px;
    flex-wrap: wrap;
}

.contact-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 25px;
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.contact-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    color: white;
}

.contact-btn i {
    font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .help-container {
        padding: 0 15px;
    }
    
    .page-title {
        font-size: 28px;
    }
    
    .page-subtitle {
        font-size: 16px;
    }
    
    .quick-nav {
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
    }
    
    .nav-item {
        padding: 24px 16px;
    }
    
    .nav-icon {
        width: 56px;
        height: 56px;
        font-size: 24px;
        margin-bottom: 16px;
    }
    
    .nav-item h3 {
        font-size: 16px;
    }
    
    .nav-item p {
        font-size: 13px;
    }
    
    .help-content {
        padding: 24px 20px;
    }
    
    .help-section h2 {
        font-size: 20px;
    }
    
    .faq-question {
        padding: 16px 20px;
    }
    
    .faq-question h3 {
        font-size: 15px;
    }
    
    .faq-answer p {
        padding: 16px 20px;
        font-size: 13px;
    }
    
    .contact-card {
        padding: 30px 20px;
    }
    
    .contact-icon {
        width: 64px;
        height: 64px;
        font-size: 28px;
        margin-bottom: 20px;
    }
    
    .contact-content h3 {
        font-size: 20px;
    }
    
    .contact-content p {
        font-size: 14px;
    }
    
    .contact-methods {
        flex-direction: column;
        align-items: center;
    }
    
    .contact-btn {
        width: 200px;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .quick-nav {
        grid-template-columns: 1fr;
    }
    
    .help-content {
        padding: 20px 16px;
    }
    
    .search-box input {
        padding: 14px 14px 14px 46px;
        font-size: 15px;
    }
    
    .search-box i {
        left: 14px;
        font-size: 16px;
    }
}
