/* 商品列表页优化样式 */

/* 页面布局 */
.list-page {
    background-color: #f8f9fa;
    min-height: 100vh;
}

.list-page .container {
    max-width: 1400px;
}

/* 页面头部 */
.page-header {
    background: white;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 20px;
    font-size: 14px;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #666;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-item:hover {
    color: #ff6f06;
}

.breadcrumb-item.current {
    color: #333;
    font-weight: 500;
}

.breadcrumb-separator {
    color: #ccc;
    margin: 0 4px;
}

.page-title-section {
    text-align: center;
}

.page-title {
    font-size: 32px;
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
}

.page-subtitle {
    font-size: 16px;
    color: #666;
    margin: 0;
}

.page-subtitle strong {
    color: #ff6f06;
    font-weight: 600;
}

/* 筛选和排序区域 */
.filter-sort-section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* 快速筛选 */
.quick-filters {
    margin-bottom: 20px;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-label {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    min-width: 60px;
}

.filter-tags {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-tag {
    display: inline-block;
    padding: 8px 16px;
    background: #f8f9fa;
    color: #666;
    border-radius: 20px;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.filter-tag:hover {
    background: #fff2e8;
    color: #ff6f06;
    border-color: #ff6f06;
}

.filter-tag.active {
    background: linear-gradient(135deg, #ff6f06, #ff8533);
    color: white;
    border-color: #ff6f06;
}

/* 高级筛选 */
.advanced-filters {
    display: none;
    border-top: 1px solid #eee;
    padding-top: 20px;
    margin-top: 20px;
}

.filter-form {
    width: 100%;
}

.filter-row {
    display: flex;
    gap: 30px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-group .filter-label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.price-range {
    display: flex;
    align-items: center;
    gap: 10px;
}

.price-input {
    flex: 1;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.price-input:focus {
    outline: none;
    border-color: #ff6f06;
    box-shadow: 0 0 0 3px rgba(255, 111, 6, 0.1);
}

.price-separator {
    color: #666;
    font-weight: 500;
}

.checkbox-group {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 14px;
    color: #666;
}

.checkbox-item input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #ddd;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-item input[type="checkbox"]:checked + .checkmark {
    background: #ff6f06;
    border-color: #ff6f06;
}

.checkbox-item input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 5px;
    top: 2px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.filter-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

/* 排序和视图控制 */
.sort-view-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
    border-top: 1px solid #eee;
    padding-top: 20px;
}

.sort-options {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.sort-label {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.sort-item {
    padding: 8px 16px;
    color: #666;
    text-decoration: none;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.sort-item:hover {
    background: #f8f9fa;
    color: #ff6f06;
}

.sort-item.active {
    background: #ff6f06;
    color: white;
    border-color: #ff6f06;
}

.view-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.filter-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 6px;
    color: #666;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.filter-toggle:hover {
    background: #fff2e8;
    color: #ff6f06;
    border-color: #ff6f06;
}

.view-switch {
    display: flex;
    border: 1px solid #ddd;
    border-radius: 6px;
    overflow: hidden;
}

.view-btn {
    padding: 8px 12px;
    background: white;
    border: none;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
}

.view-btn:hover {
    background: #f8f9fa;
    color: #ff6f06;
}

.view-btn.active {
    background: #ff6f06;
    color: white;
}

/* 商品容器 */
.products-container {
    margin-bottom: 40px;
}

/* 商品网格布局 */
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 25px;
}

/* 商品列表布局 */
.product-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.product-list .enhanced-product-card {
    display: flex;
    flex-direction: row;
    height: 200px;
}

.product-list .product-image {
    width: 200px;
    flex-shrink: 0;
}

.product-list .product-content {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* 增强商品卡片 */
.enhanced-product-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
}

.enhanced-product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border-color: #ff6f06;
}

.product-link {
    display: block;
    text-decoration: none;
    color: inherit;
    height: 100%;
}

/* 商品图片 */
.product-image {
    position: relative;
    height: 220px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.enhanced-product-card:hover .product-image img {
    transform: scale(1.1);
}

/* 商品标签 */
.product-badges {
    position: absolute;
    top: 12px;
    left: 12px;
    z-index: 2;
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    color: white;
}

.virtual-badge {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.featured-badge {
    background: linear-gradient(135deg, #ffc107, #ff8c00);
}

/* 悬停操作 */
.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.enhanced-product-card:hover .product-overlay {
    opacity: 1;
}

.overlay-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.action-btn:hover {
    background: #ff6f06;
    color: white;
    transform: scale(1.1);
}

.action-btn.liked {
    background: #dc3545;
    color: white;
}

/* 商品内容 */
.product-content {
    padding: 20px;
    height: calc(100% - 220px);
    display: flex;
    flex-direction: column;
}

.product-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
    line-height: 1.4;
    height: 44px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

/* 商品价格 */
.product-price {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 12px;
    flex-wrap: wrap;
}

.current-price {
    font-size: 20px;
    font-weight: 700;
    color: #ff6f06;
}

.original-price {
    font-size: 14px;
    color: #999;
    text-decoration: line-through;
}

.discount-tag {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: 600;
}

/* 商品特性 */
.product-features {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
    flex-wrap: wrap;
}

.feature-tag {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background: #f8f9fa;
    color: #666;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.feature-tag i {
    font-size: 12px;
    color: #ff6f06;
}

/* 商品元信息 */
.product-meta {
    margin-top: auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.seller-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.seller-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    object-fit: cover;
}

.seller-name {
    font-size: 13px;
    color: #666;
    font-weight: 500;
}

.seller-rating {
    display: flex;
    gap: 2px;
}

.seller-rating i {
    font-size: 12px;
    color: #ffc107;
}

.product-stats {
    display: flex;
    gap: 12px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #999;
}

.stat-item i {
    font-size: 14px;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 80px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.empty-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.empty-icon i {
    font-size: 32px;
    color: #999;
}

.empty-title {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.empty-description {
    font-size: 16px;
    color: #666;
    margin-bottom: 30px;
    line-height: 1.5;
}

.empty-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

/* 分页 */
.pagination-wrapper {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.pagination-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    color: #666;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.pagination-btn:hover {
    background: #ff6f06;
    color: white;
    border-color: #ff6f06;
}

.pagination-numbers {
    display: flex;
    gap: 5px;
    align-items: center;
}

.pagination-number {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    color: #666;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.pagination-number:hover {
    background: #fff2e8;
    color: #ff6f06;
    border-color: #ff6f06;
}

.pagination-number.active {
    background: #ff6f06;
    color: white;
    border-color: #ff6f06;
}

.pagination-ellipsis {
    padding: 0 8px;
    color: #999;
    font-size: 14px;
}

.pagination-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
    border-top: 1px solid #eee;
    padding-top: 20px;
}

.page-info {
    font-size: 14px;
    color: #666;
}

.page-jump {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    color: #666;
}

.jump-input {
    width: 60px;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
    font-size: 14px;
}

.jump-input:focus {
    outline: none;
    border-color: #ff6f06;
}

.jump-btn {
    padding: 6px 12px;
    background: #ff6f06;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.jump-btn:hover {
    background: #e56200;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }
}

@media (max-width: 768px) {
    .page-header {
        padding: 20px;
    }

    .page-title {
        font-size: 24px;
    }

    .filter-sort-section {
        padding: 20px;
    }

    .filter-row {
        flex-direction: column;
        gap: 20px;
        align-items: stretch;
    }

    .sort-view-section {
        flex-direction: column;
        gap: 15px;
    }

    .sort-options {
        justify-content: center;
    }

    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 15px;
    }

    .product-list .enhanced-product-card {
        flex-direction: column;
        height: auto;
    }

    .product-list .product-image {
        width: 100%;
        height: 200px;
    }

    .pagination {
        gap: 5px;
    }

    .pagination-btn {
        padding: 8px 12px;
        font-size: 13px;
    }

    .pagination-number {
        width: 36px;
        height: 36px;
        font-size: 13px;
    }

    .pagination-info {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 15px;
    }

    .page-title {
        font-size: 20px;
    }

    .filter-sort-section {
        padding: 15px;
    }

    .filter-tags {
        gap: 8px;
    }

    .filter-tag {
        padding: 6px 12px;
        font-size: 13px;
    }

    .checkbox-group {
        flex-direction: column;
        gap: 10px;
    }

    .product-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .enhanced-product-card .product-content {
        padding: 15px;
    }

    .product-title {
        font-size: 15px;
        height: auto;
        -webkit-line-clamp: 3;
    }

    .current-price {
        font-size: 18px;
    }

    .pagination-numbers {
        gap: 3px;
    }

    .pagination-number {
        width: 32px;
        height: 32px;
        font-size: 12px;
    }

    .empty-state {
        padding: 60px 15px;
    }

    .empty-title {
        font-size: 20px;
    }

    .empty-description {
        font-size: 14px;
    }

    .empty-actions {
        flex-direction: column;
        align-items: center;
    }
}

/* 模态框样式 */
.quick-view-modal,
.share-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    position: relative;
    background: white;
    border-radius: 12px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #999;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: #f5f5f5;
    color: #333;
}

.modal-body {
    padding: 20px;
}

/* 分享选项 */
.share-options {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.share-btn {
    padding: 15px;
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 8px;
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
}

.share-btn:hover {
    background: #ff6f06;
    color: white;
    border-color: #ff6f06;
    transform: translateY(-2px);
}

/* 加载指示器 */
.loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40px;
    color: #666;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #ff6f06;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.no-more-message {
    text-align: center;
    padding: 40px;
    color: #999;
    font-size: 14px;
    border-top: 1px solid #eee;
    margin-top: 20px;
}

/* 筛选器激活状态 */
.filter-toggle.active {
    background: #ff6f06;
    color: white;
    border-color: #ff6f06;
}

/* 提示消息 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #333;
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    z-index: 10000;
    font-size: 14px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 响应式模态框 */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        max-height: 90vh;
    }

    .modal-header,
    .modal-body {
        padding: 15px;
    }

    .share-options {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .share-btn {
        padding: 12px;
    }
}
