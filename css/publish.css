/* 发布页面样式 */
.publish-page {
    padding: 30px 0 50px;
    background-color: #f5f5f5;
}

.publish-container {
    background-color: #fff;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    max-width: 900px;
    margin: 0 auto;
}

.page-title {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

/* 表单样式 */
.publish-form .form-group {
    margin-bottom: 25px;
}

.form-label {
    display: block;
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 10px;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    border-color: #ff6f06;
    box-shadow: 0 0 0 2px rgba(255, 111, 6, 0.1);
    outline: none;
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.input-tips {
    font-size: 12px;
    color: #999;
    margin-top: 5px;
}

/* 商品类型选择器 */
.product-type-selector {
    display: flex;
    gap: 20px;
    margin-bottom: 10px;
}

.type-option {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.type-option input[type="radio"] {
    margin-right: 8px;
    accent-color: #ff6f06;
}

/* 虚拟商品分类选择器 */
.virtual-category-selector {
    max-width: 400px;
}

/* 图片上传 */
.image-uploader {
    margin-bottom: 15px;
}

.upload-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 10px;
}

.upload-item {
    width: 120px;
    height: 120px;
    border-radius: 6px;
    overflow: hidden;
    position: relative;
}

.add-image {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 1px dashed #ddd;
    background-color: #f9f9f9;
    cursor: pointer;
    transition: all 0.3s;
}

.add-image:hover {
    border-color: #ff6f06;
    background-color: #fff2e8;
}

.add-image i {
    font-size: 24px;
    color: #999;
    margin-bottom: 5px;
}

.add-image span {
    font-size: 12px;
    color: #999;
}

.file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.upload-tips p {
    font-size: 12px;
    color: #999;
    margin-bottom: 5px;
}

/* 虚拟商品属性 */
.virtual-attributes {
    background-color: #f9f9f9;
    border-radius: 6px;
    padding: 15px;
}

.attribute-item {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.attribute-item:last-child {
    margin-bottom: 0;
}

.attribute-label {
    width: 100px;
    font-size: 14px;
    color: #666;
}

.attribute-item .form-select,
.attribute-item .form-input {
    flex: 1;
    min-width: 200px;
}

.period-input {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 10px;
    margin-left: 100px;
}

.period-input .form-input {
    width: 100px;
}

.period-input .form-select {
    width: 80px;
}

/* 价格输入 */
.price-input {
    display: flex;
    align-items: center;
    max-width: 300px;
}

.price-symbol {
    font-size: 16px;
    font-weight: 500;
    margin-right: 5px;
    color: #333;
}

/* 交易方式 */
.trade-options,
.service-options {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 10px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    margin-right: 8px;
    accent-color: #ff6f06;
}

/* 内容选项卡 */
.content-tabs {
    display: flex;
    border-bottom: 1px solid #eee;
    margin-bottom: 15px;
}

.content-tab {
    padding: 10px 20px;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    border-bottom: 2px solid transparent;
    transition: all 0.3s;
}

.content-tab.active {
    color: #ff6f06;
    border-bottom-color: #ff6f06;
}

.content-panel {
    display: none;
}

.content-panel.active {
    display: block;
}

/* 文件上传 */
.file-uploader {
    margin-bottom: 15px;
}

.upload-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
    overflow: hidden;
}

.upload-btn:hover {
    background-color: #fff2e8;
    border-color: #ff6f06;
}

.upload-btn i {
    font-size: 18px;
    margin-right: 8px;
    color: #666;
}

.file-list {
    margin-top: 15px;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
    margin-bottom: 10px;
}

.file-icon {
    font-size: 20px;
    color: #666;
    margin-right: 10px;
}

.file-info {
    flex: 1;
}

.file-name {
    font-size: 14px;
    color: #333;
    margin-bottom: 3px;
}

.file-size {
    font-size: 12px;
    color: #999;
}

.file-remove {
    color: #ff6f06;
    cursor: pointer;
    font-size: 16px;
}

/* 表单操作 */
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.btn {
    padding: 12px 25px;
    font-size: 16px;
    font-weight: 500;
}

/* 侧边导航样式 */
.sidebar-nav {
    width: 120px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    padding: 15px 0;
    margin-right: 20px;
}

.sidebar-nav ul {
    list-style: none;
}

.sidebar-nav li {
    padding: 10px 15px;
    border-bottom: 1px solid #f5f5f5;
}

.sidebar-nav li:last-child {
    border-bottom: none;
}

.sidebar-nav a {
    display: block;
    color: #666;
    font-size: 14px;
    text-decoration: none;
    transition: color 0.3s;
}

.sidebar-nav a:hover,
.sidebar-nav a.active {
    color: #ff6f06;
}

/* 修改主内容区布局 */
.publish-page .container {
    display: flex;
    align-items: flex-start;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .publish-page .container {
        flex-direction: column;
    }
    
    .sidebar-nav {
        width: 100%;
        margin-right: 0;
        margin-bottom: 20px;
    }
    
    .sidebar-nav ul {
        display: flex;
        flex-wrap: wrap;
    }
    
    .sidebar-nav li {
        border-bottom: none;
        border-right: 1px solid #f5f5f5;
    }
}
.form-label {
    display: block;
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 10px;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    border-color: #ff6f06;
    box-shadow: 0 0 0 2px rgba(255, 111, 6, 0.1);
    outline: none;
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.input-tips {
    font-size: 12px;
    color: #999;
    margin-top: 5px;
}

/* 商品类型选择器 */
.product-type-selector {
    display: flex;
    gap: 20px;
    margin-bottom: 10px;
}

.type-option {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.type-option input[type="radio"] {
    margin-right: 8px;
    accent-color: #ff6f06;
}

/* 虚拟商品分类选择器 */
.virtual-category-selector {
    max-width: 400px;
}

/* 图片上传 */
.image-uploader {
    margin-bottom: 15px;
}

.upload-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 10px;
}

.upload-item {
    width: 120px;
    height: 120px;
    border-radius: 6px;
    overflow: hidden;
    position: relative;
}

.add-image {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 1px dashed #ddd;
    background-color: #f9f9f9;
    cursor: pointer;
    transition: all 0.3s;
}

.add-image:hover {
    border-color: #ff6f06;
    background-color: #fff2e8;
}

.add-image i {
    font-size: 24px;
    color: #999;
    margin-bottom: 5px;
}

.add-image span {
    font-size: 12px;
    color: #999;
}

.file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.upload-tips p {
    font-size: 12px;
    color: #999;
    margin-bottom: 5px;
}

/* 虚拟商品属性 */
.virtual-attributes {
    background-color: #f9f9f9;
    border-radius: 6px;
    padding: 15px;
}

.attribute-item {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.attribute-item:last-child {
    margin-bottom: 0;
}

.attribute-label {
    width: 100px;
    font-size: 14px;
    color: #666;
}

.attribute-item .form-select,
.attribute-item .form-input {
    flex: 1;
    min-width: 200px;
}

.period-input {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 10px;
    margin-left: 100px;
}

.period-input .form-input {
    width: 100px;
}

.period-input .form-select {
    width: 80px;
}

/* 价格输入 */
.price-input {
    display: flex;
    align-items: center;
    max-width: 300px;
}

.price-symbol {
    font-size: 16px;
    font-weight: 500;
    margin-right: 5px;
    color: #333;
}

/* 交易方式 */
.trade-options,
.service-options {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 10px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    margin-right: 8px;
    accent-color: #ff6f06;
}

/* 内容选项卡 */
.content-tabs {
    display: flex;
    border-bottom: 1px solid #eee;
    margin-bottom: 15px;
}

.content-tab {
    padding: 10px 20px;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    border-bottom: 2px solid transparent;
    transition: all 0.3s;
}

.content-tab.active {
    color: #ff6f06;
    border-bottom-color: #ff6f06;
}

.content-panel {
    display: none;
}

.content-panel.active {
    display: block;
}

/* 文件上传 */
.file-uploader {
    margin-bottom: 15px;
}

.upload-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
    overflow: hidden;
}

.upload-btn:hover {
    background-color: #fff2e8;
    border-color: #ff6f06;
}

.upload-btn i {
    font-size: 18px;
    margin-right: 8px;
    color: #666;
}

.file-list {
    margin-top: 15px;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
    margin-bottom: 10px;
}

.file-icon {
    font-size: 20px;
    color: #666;
    margin-right: 10px;
}

.file-info {
    flex: 1;
}

.file-name {
    font-size: 14px;
    color: #333;
    margin-bottom: 3px;
}

.file-size {
    font-size: 12px;
    color: #999;
}

.file-remove {
    color: #ff6f06;
    cursor: pointer;
    font-size: 16px;
}

/* 表单操作 */
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.btn {
    padding: 12px 25px;
    font-size: 16px;
    font-weight: 500;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .publish-container {
        padding: 20px 15px;
    }
    
    .attribute-label {
        width: 100%;
        margin-bottom: 5px;
    }
    
    .period-input {
        margin-left: 0;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
}