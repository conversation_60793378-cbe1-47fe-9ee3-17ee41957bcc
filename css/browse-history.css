/* 浏览历史页面样式 - 数字鱼虚拟商品交易平台 */

/* 浏览统计 */
.browse-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.browse-stats .stat-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 16px;
    transition: all 0.3s ease;
}

.browse-stats .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.browse-stats .stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, #6c757d, #495057);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.browse-stats .stat-content {
    flex: 1;
}

.browse-stats .stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #212529;
    margin-bottom: 4px;
}

.browse-stats .stat-label {
    font-size: 14px;
    color: #6c757d;
}

/* 历史记录列表 */
.history-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.history-item {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
}

.history-item:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

/* 商品图片 */
.history-item .product-image {
    position: relative;
    width: 100px;
    height: 100px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
}

.history-item .product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.history-item .product-tag {
    position: absolute;
    top: 6px;
    left: 6px;
    background: linear-gradient(135deg, #ff6f06, #ff8533);
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 4px;
}

/* 商品信息 */
.history-item .product-info {
    flex: 1;
    min-width: 0;
}

.history-item .product-title {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
}

.history-item .product-title a {
    color: #212529;
    text-decoration: none;
}

.history-item .product-title a:hover {
    color: #ff6f06;
}

.history-item .product-meta {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
}

.history-item .product-category {
    font-size: 12px;
    color: #6c757d;
    background-color: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
}

.history-item .product-price {
    font-size: 16px;
    font-weight: 700;
    color: #ff6f06;
}

.history-item .seller-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.history-item .seller-avatar {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    object-fit: cover;
}

.history-item .seller-name {
    font-size: 12px;
    color: #6c757d;
}

.history-item .product-stats {
    display: flex;
    gap: 12px;
}

.history-item .stat-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #6c757d;
}

.history-item .stat-item i {
    font-size: 13px;
}

/* 浏览信息 */
.browse-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 12px;
    flex-shrink: 0;
}

.browse-time {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    color: #6c757d;
}

.browse-time i {
    font-size: 14px;
}

.item-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    border: 1px solid #dee2e6;
    background: white;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    color: #495057;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
}

.view-btn:hover {
    background-color: #d1ecf1;
    border-color: #17a2b8;
    color: #17a2b8;
}

.remove-btn:hover {
    background-color: #f8d7da;
    border-color: #dc3545;
    color: #dc3545;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .browse-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    .history-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
        padding: 16px;
    }
    
    .history-item .product-image {
        width: 80px;
        height: 80px;
        align-self: center;
    }
    
    .history-item .product-info {
        width: 100%;
        text-align: center;
    }
    
    .browse-info {
        width: 100%;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }
    
    .item-actions {
        flex-wrap: wrap;
    }
}

@media (max-width: 480px) {
    .browse-stats {
        grid-template-columns: 1fr;
    }
    
    .history-item {
        padding: 12px;
    }
    
    .history-item .product-image {
        width: 60px;
        height: 60px;
    }
    
    .browse-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .item-actions {
        width: 100%;
        justify-content: center;
    }
    
    .action-btn {
        flex: 1;
        justify-content: center;
    }
}
