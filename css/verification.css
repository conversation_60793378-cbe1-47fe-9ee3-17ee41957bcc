/* 实名认证样式 - 数字鱼虚拟商品交易平台 */

/* 认证状态卡片 */
.verification-status {
    margin-bottom: 32px;
}

.status-card {
    background: white;
    border-radius: 16px;
    padding: 32px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #f1f3f4;
}

.status-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 24px;
}

.status-icon {
    font-size: 48px;
}

.status-info h3 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
    color: #333;
}

.status-info p {
    margin: 0;
    font-size: 16px;
    color: #6c757d;
    line-height: 1.5;
}

.verification-details {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #e9ecef;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-item label {
    font-weight: 500;
    color: #333;
    margin: 0;
}

.detail-item span {
    color: #6c757d;
}

.reject-reason {
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 24px;
}

.reject-reason h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: #e53e3e;
}

.reject-reason p {
    margin: 0;
    color: #742a2a;
    line-height: 1.5;
}

.status-actions {
    text-align: center;
}

/* 认证表单 */
.verification-form-container {
    margin-bottom: 32px;
}

.form-card {
    background: white;
    border-radius: 16px;
    padding: 32px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #f1f3f4;
}

.form-header {
    text-align: center;
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid #f1f3f4;
}

.form-header h3 {
    margin: 0 0 12px 0;
    font-size: 24px;
    font-weight: 600;
    color: #333;
}

.form-header p {
    margin: 0;
    font-size: 16px;
    color: #6c757d;
    line-height: 1.5;
}

.verification-form {
    max-width: 600px;
    margin: 0 auto;
}

.form-label.required::after {
    content: ' *';
    color: #e53e3e;
}

/* 文件上传区域 */
.upload-area {
    border: 2px dashed #e9ecef;
    border-radius: 12px;
    padding: 32px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    background: #fafbfc;
}

.upload-area:hover {
    border-color: #ff6f06;
    background: #fff2e8;
}

.upload-area.dragover {
    border-color: #ff6f06;
    background: #fff2e8;
    transform: scale(1.02);
}

.upload-area input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.upload-placeholder i {
    font-size: 48px;
    color: #adb5bd;
    margin-bottom: 16px;
    display: block;
}

.upload-placeholder p {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 500;
    color: #495057;
}

.upload-placeholder small {
    color: #6c757d;
    font-size: 14px;
}

/* 文件预览 */
.upload-preview {
    position: relative;
    display: inline-block;
}

.upload-preview img {
    max-width: 200px;
    max-height: 150px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.preview-overlay {
    position: absolute;
    top: 8px;
    right: 8px;
}

.btn-remove {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(220, 53, 69, 0.9);
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.btn-remove:hover {
    background: #dc3545;
    transform: scale(1.1);
}

/* 认证须知 */
.verification-tips {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 24px;
    margin: 24px 0;
    border-left: 4px solid #ff6f06;
}

.verification-tips h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.verification-tips ul {
    margin: 0;
    padding-left: 20px;
}

.verification-tips li {
    margin-bottom: 8px;
    color: #6c757d;
    line-height: 1.5;
}

.verification-tips li:last-child {
    margin-bottom: 0;
}

.form-actions {
    text-align: center;
    margin-top: 32px;
}

.btn-large {
    padding: 16px 48px;
    font-size: 16px;
    font-weight: 600;
}

/* 认证优势 */
.verification-benefits {
    background: white;
    border-radius: 16px;
    padding: 32px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #f1f3f4;
}

.verification-benefits h3 {
    margin: 0 0 32px 0;
    font-size: 24px;
    font-weight: 600;
    color: #333;
    text-align: center;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
}

.benefit-item {
    text-align: center;
    padding: 24px;
    background: #fafbfc;
    border-radius: 12px;
    border: 1px solid #f1f3f4;
    transition: all 0.3s ease;
}

.benefit-item:hover {
    background: #fff2e8;
    border-color: #ff6f06;
    transform: translateY(-4px);
}

.benefit-icon {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    background: linear-gradient(135deg, #ff6f06, #ff8533);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px auto;
    font-size: 24px;
}

.benefit-item h4 {
    margin: 0 0 12px 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.benefit-item p {
    margin: 0;
    font-size: 14px;
    color: #6c757d;
    line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .status-card,
    .form-card,
    .verification-benefits {
        padding: 24px;
    }

    .status-header {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }

    .status-icon {
        font-size: 40px;
    }

    .status-info h3 {
        font-size: 20px;
    }

    .verification-details {
        padding: 20px;
    }

    .detail-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .upload-area {
        padding: 24px 16px;
    }

    .upload-placeholder i {
        font-size: 40px;
        margin-bottom: 12px;
    }

    .upload-placeholder p {
        font-size: 15px;
    }

    .verification-tips {
        padding: 20px;
    }

    .benefits-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .benefit-item {
        padding: 20px;
    }

    .benefit-icon {
        width: 56px;
        height: 56px;
        font-size: 20px;
    }

    .btn-large {
        padding: 14px 32px;
        font-size: 15px;
    }
}

@media (max-width: 480px) {
    .status-card,
    .form-card,
    .verification-benefits {
        padding: 20px;
    }

    .form-header h3 {
        font-size: 20px;
    }

    .verification-benefits h3 {
        font-size: 20px;
        margin-bottom: 24px;
    }

    .upload-area {
        padding: 20px 12px;
    }

    .upload-placeholder i {
        font-size: 36px;
    }

    .upload-placeholder p {
        font-size: 14px;
    }

    .verification-tips {
        padding: 16px;
    }

    .verification-tips ul {
        padding-left: 16px;
    }

    .btn-large {
        width: 100%;
        padding: 12px 24px;
    }
}
