/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans SC', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    font-size: 14px;
    color: #333;
    background-color: #f5f5f5;
    line-height: 1.5;
}

a {
    text-decoration: none;
    color: #333;
    transition: color 0.3s;
}

a:hover {
    color: #ff6f06;
}

ul, ol {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* 按钮样式 */
button {
    cursor: pointer;
    border: none;
    outline: none;
    background: none;
}

.btn {
    display: inline-block;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    transition: all 0.3s;
}

.btn-primary {
    background-color: #ff6f06;
    color: #fff;
}

.btn-primary:hover {
    background-color: #e56200;
}

.btn-outline {
    border: 1px solid #ddd;
    background-color: #fff;
}

.btn-outline:hover {
    border-color: #ff6f06;
    color: #ff6f06;
}

/* 头部导航 */
.header {
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header .container {
    display: flex;
    align-items: center;
    height: 60px;
}

.logo {
    margin-right: 20px;
}

.logo img {
    height: 36px;
}

.search-box {
    flex: 1;
    max-width: 500px;
    position: relative;
    margin-right: 20px;
}

.search-box input {
    width: 100%;
    height: 40px;
    padding: 0 40px 0 15px;
    border: 1px solid #e5e5e5;
    border-radius: 20px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.search-box input:focus {
    border-color: #ff6f06;
    outline: none;
}

.search-btn {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    font-size: 16px;
}

.search-btn:hover {
    color: #ff6f06;
}

.nav ul {
    display: flex;
}

.nav li {
    margin: 0 15px;
}

.nav a {
    display: block;
    font-size: 16px;
    font-weight: 500;
    padding: 5px 0;
    position: relative;
}

.nav a.active {
    color: #ff6f06;
}

.nav a.active:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #ff6f06;
}

.publish-btn {
    color: #ff6f06 !important;
    font-weight: 600 !important;
}

.user-info {
    display: flex;
    align-items: center;
    margin-left: 20px;
}

.login-btn {
    margin-right: 15px;
}

.user-avatar img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

/* 主要内容区 */
.main {
    padding: 20px 0 40px;
}

/* 分类导航 */
.category-nav {
    background-color: #fff;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.category-nav ul {
    display: flex;
    flex-wrap: wrap;
}

.category-nav li {
    margin-right: 20px;
    margin-bottom: 10px;
}

.category-nav a {
    display: block;
    padding: 5px 10px;
    border-radius: 15px;
    transition: all 0.3s;
}

.category-nav a:hover {
    background-color: #fff2e8;
    color: #ff6f06;
}

/* 轮播广告 */
.banner-slider {
    position: relative;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 20px;
    height: 300px;
}

.slider-item {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s;
}

.slider-item.active {
    opacity: 1;
}

.slider-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.slider-dots {
    position: absolute;
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
}

.dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    margin: 0 5px;
    cursor: pointer;
    transition: all 0.3s;
}

.dot.active {
    background-color: #fff;
    transform: scale(1.2);
}

/* 特色服务 */
.feature-services {
    display: flex;
    justify-content: space-between;
    background-color: #fff;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.service-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px 0;
}

.service-item i {
    font-size: 24px;
    color: #ff6f06;
    margin-bottom: 5px;
}

/* 标题栏 */
.section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.section-title h2 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.more-link {
    font-size: 14px;
    color: #999;
}

.more-link:hover {
    color: #ff6f06;
}

/* 商品网格 */
.product-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

/* 商品卡片 */
.product-card {
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.product-img {
    position: relative;
    height: 0;
    padding-bottom: 100%;
    overflow: hidden;
}

.product-img img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s;
}

.product-card:hover .product-img img {
    transform: scale(1.05);
}

.product-tag {
    position: absolute;
    top: 10px;
    left: 10px;
    padding: 3px 8px;
    background-color: #ff6f06;
    color: #fff;
    border-radius: 2px;
    font-size: 12px;
}

.product-info {
    padding: 12px;
}

.product-title {
    font-size: 14px;
    font-weight: 400;
    margin-bottom: 8px;
    line-height: 1.4;
    height: 40px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.product-meta {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.price {
    font-size: 16px;
    font-weight: 600;
    color: #ff6f06;
    margin-right: 8px;
}

.original-price {
    font-size: 12px;
    color: #999;
    text-decoration: line-through;
}

.seller-info {
    display: flex;
    align-items: center;
}

.seller-avatar {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 5px;
}

.seller-name {
    font-size: 12px;
    color: #666;
    margin-right: 5px;
}

.seller-level {
    font-size: 12px;
    color: #ff6f06;
}

.product-actions {
    display: flex;
    justify-content: space-between;
    padding: 8px 12px 12px;
    border-top: 1px solid #f5f5f5;
}

.like-btn, .comment-btn {
    font-size: 12px;
    color: #999;
    cursor: pointer;
}

.like-btn:hover, .comment-btn:hover {
    color: #ff6f06;
}

/* 鱼塘卡片 */
.fishpond-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.fishpond-card {
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s, box-shadow 0.3s;
}

.fishpond-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.fishpond-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 15px;
}

.fishpond-img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-bottom: 10px;
}

.fishpond-header h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
}

.fishpond-header p {
    font-size: 12px;
    color: #999;
}

.fishpond-products {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.fishpond-products img {
    width: 30%;
    border-radius: 4px;
}

.join-btn {
    width: 100%;
    padding: 8px 0;
    background-color: #fff;
    border: 1px solid #ff6f06;
    color: #ff6f06;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s;
}

.join-btn:hover {
    background-color: #ff6f06;
    color: #fff;
}

/* 底部信息 */
.footer {
    background-color: #fff;
    padding: 40px 0 20px;
    border-top: 1px solid #e5e5e5;
}

.footer-links {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
}

.link-group h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
}

.link-group ul li {
    margin-bottom: 10px;
}

.link-group ul li a {
    color: #666;
    font-size: 14px;
}

.link-group ul li a:hover {
    color: #ff6f06;
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 20px;
    border-top: 1px solid #e5e5e5;
}

.copyright, .footer-info {
    font-size: 12px;
    color: #999;
}

/* 列表页样式 */
.list-page .container {
    max-width: 1200px;
}

.breadcrumb {
    margin-bottom: 15px;
    font-size: 14px;
    color: #666;
}

.breadcrumb a {
    color: #666;
}

.breadcrumb a:hover {
    color: #ff6f06;
}

.current-category {
    color: #333;
    font-weight: 500;
}

.filter-section {
    background-color: #fff;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.filter-group {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px dashed #e5e5e5;
}

.filter-group:last-child {
    border-bottom: none;
}

.filter-label {
    width: 80px;
    font-size: 14px;
    color: #666;
    flex-shrink: 0;
}

.filter-options {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}

.filter-options a {
    margin-right: 15px;
    margin-bottom: 5px;
    font-size: 14px;
    color: #666;
    padding: 3px 8px;
    border-radius: 2px;
}

.filter-options a.active {
    background-color: #ff6f06;
    color: #fff;
}

.filter-options a:hover:not(.active) {
    color: #ff6f06;
}

.price-input {
    display: flex;
    align-items: center;
}

.price-input input {
    width: 80px;
    height: 30px;
    padding: 0 10px;
    border: 1px solid #e5e5e5;
    border-radius: 2px;
    margin: 0 5px;
}

.price-input button {
    padding: 0 10px;
    height: 30px;
    background-color: #f5f5f5;
    border: 1px solid #e5e5e5;
    border-radius: 2px;
    margin-left: 5px;
    color: #666;
}

.price-input button:hover {
    color: #ff6f06;
    border-color: #ff6f06;
}

.checkbox-label {
    display: flex;
    align-items: center;
    margin-right: 15px;
    cursor: pointer;
}

.checkbox-label input {
    margin-right: 5px;
}

.sort-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    border-radius: 4px;
    padding: 10px 15px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.sort-options {
    display: flex;
}

.sort-options a {
    margin-right: 20px;
    font-size: 14px;
    color: #666;
    display: flex;
    align-items: center;
}

.sort-options a.active {
    color: #ff6f06;
    font-weight: 500;
}

.sort-options a i {
    margin-left: 3px;
}

.view-switch {
    display: flex;
    align-items: center;
}

.view-count {
    margin-right: 15px;
    font-size: 14px;
    color: #999;
}

.grid-view, .list-view {
    font-size: 18px;
    color: #999;
    margin-left: 10px;
}

.grid-view.active, .list-view.active {
    color: #ff6f06;
}

.list-grid {
    margin-bottom: 30px;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 30px;
}

.pagination a {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 36px;
    height: 36px;
    margin: 0 5px;
    border-radius: 4px;
    font-size: 14px;
    color: #666;
    background-color: #fff;
    border: 1px solid #e5e5e5;
    transition: all 0.3s;
}

.pagination a.active {
    background-color: #ff6f06;
    color: #fff;
    border-color: #ff6f06;
}

.pagination a:hover:not(.active) {
    border-color: #ff6f06;
    color: #ff6f06;
}

.prev-page, .next-page {
    font-size: 16px;
}

.ellipsis {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 36px;
    height: 36px;
    margin: 0 5px;
    font-size: 14px;
    color: #666;
}

.page-jump {
    display: flex;
    align-items: center;
    margin-left: 15px;
    font-size: 14px;
    color: #666;
}

.page-jump input {
    width: 40px;
    height: 36px;
    margin: 0 5px;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    text-align: center;
}

.page-jump button {
    padding: 0 10px;
    height: 36px;
    background-color: #f5f5f5;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    margin-left: 5px;
    color: #666;
}

.page-jump button:hover {
    color: #ff6f06;
    border-color: #ff6f06;
}

/* 详情页样式 */
.detail-page .container {
    max-width: 1200px;
}

.product-detail {
    display: flex;
    background-color: #fff;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.product-gallery {
    width: 400px;
    margin-right: 30px;
}

.main-image {
    width: 100%;
    height: 400px;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.main-image img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    cursor: zoom-in;
}

.thumbnails {
    display: flex;
    justify-content: flex-start;
}

.thumbnails img {
    width: 70px;
    height: 70px;
    border-radius: 4px;
    margin-right: 10px;
    cursor: pointer;
    border: 2px solid transparent;
    object-fit: cover;
}

.thumbnails img.active {
    border-color: #ff6f06;
}

.product-info-detail {
    flex: 1;
}

.product-title-detail {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 15px;
    line-height: 1.4;
}

.product-price-detail {
    display: flex;
    align-items: baseline;
    margin-bottom: 20px;
}

.current-price {
    font-size: 24px;
    font-weight: 600;
    color: #ff6f06;
    margin-right: 10px;
}

.original-price-detail {
    font-size: 14px;
    color: #999;
    text-decoration: line-through;
}

.product-meta-detail {
    margin-bottom: 20px;
}

.meta-item {
    display: flex;
    margin-bottom: 10px;
    font-size: 14px;
    color: #666;
}

.meta-label {
    width: 70px;
    color: #999;
}

.product-services {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e5e5e5;
}

.service-tag {
    display: flex;
    align-items: center;
    margin-right: 15px;
    margin-bottom: 10px;
    font-size: 14px;
    color: #666;
}

.service-tag i {
    color: #ff6f06;
    margin-right: 5px;
}

.product-actions-detail {
    display: flex;
    margin-bottom: 20px;
}

.buy-now-btn, .add-cart-btn {
    padding: 10px 30px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 4px;
    margin-right: 15px;
}

.buy-now-btn {
    background-color: #ff6f06;
    color: #fff;
}

.buy-now-btn:hover {
    background-color: #e56200;
}

.add-cart-btn {
    background-color: #fff2e8;
    color: #ff6f06;
    border: 1px solid #ff6f06;
}

.add-cart-btn:hover {
    background-color: #ffe0cc;
}

.action-icons {
    display: flex;
    align-items: center;
}

.action-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-left: 20px;
    font-size: 14px;
    color: #666;
    cursor: pointer;
}

.action-icon i {
    font-size: 20px;
    margin-bottom: 5px;
}

.action-icon:hover {
    color: #ff6f06;
}

.seller-card {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
}

.seller-avatar-detail {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-right: 15px;
}

.seller-info-detail {
    flex: 1;
}

.seller-name-detail {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 5px;
}

.seller-meta {
    display: flex;
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
}

.seller-meta-item {
    margin-right: 15px;
}

.contact-seller-btn {
    padding: 8px 20px;
    font-size: 14px;
    border-radius: 4px;
    background-color: #fff;
    border: 1px solid #ff6f06;
    color: #ff6f06;
}

.contact-seller-btn:hover {
    background-color: #ff6f06;
    color: #fff;
}

.product-detail-tabs {
    background-color: #fff;
    border-radius: 4px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.detail-tabs-header {
    display: flex;
    border-bottom: 1px solid #e5e5e5;
}

.detail-tab {
    padding: 15px 30px;
    font-size: 16px;
    font-weight: 500;
    color: #666;
    cursor: pointer;
    position: relative;
}

.detail-tab.active {
    color: #ff6f06;
}

.detail-tab.active:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #ff6f06;
}

.detail-tab-content {
    padding: 20px;
}

.product-description {
    font-size: 14px;
    color: #333;
    line-height: 1.6;
}

.product-description img {
    max-width: 100%;
    margin: 15px 0;
    border-radius: 4px;
}

.product-attributes {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.attribute-item {
    display: flex;
    font-size: 14px;
    color: #666;
}

.attribute-label {
    width: 100px;
    color: #999;
}

.similar-products {
    margin-top: 30px;
}

.similar-products h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
}

.similar-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 15px;
}

/* 搜索页样式 */
.search-page .container {
    max-width: 1200px;
}

.search-header {
    margin-bottom: 20px;
}

.search-result-info {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
}

.search-tabs {
    display: flex;
    background-color: #fff;
    border-radius: 4px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.search-tab {
    padding: 15px 30px;
    font-size: 16px;
    font-weight: 500;
    color: #666;
    cursor: pointer;
    position: relative;
    display: flex;
    align-items: center;
}

.search-tab.active {
    color: #ff6f06;
    background-color: #fff2e8;
}

.search-tab-count {
    font-size: 14px;
    color: #999;
    margin-left: 5px;
}

.search-tab.active .search-tab-count {
    color: #ff6f06;
}

/* 图片预览 */
.image-preview-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.preview-image {
    max-width: 90%;
    max-height: 90%;
    object-fit: contain;
}

.preview-close {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 30px;
    color: #fff;
    background: none;
    border: none;
    cursor: pointer;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .product-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .fishpond-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .similar-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 992px) {
    .product-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .product-detail {
        flex-direction: column;
    }
    
    .product-gallery {
        width: 100%;
        margin-right: 0;
        margin-bottom: 20px;
    }
    
    .similar-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .header .container {
        flex-wrap: wrap;
    }
    
    .logo {
        margin-right: 10px;
    }
    
    .search-box {
        order: 3;
        width: 100%;
        max-width: 100%;
        margin: 10px 0;
    }
    
    .nav ul {
        justify-content: space-between;
    }
    
    .nav li {
        margin: 0 5px;
    }
    
    .feature-services {
        flex-wrap: wrap;
    }
    
    .service-item {
        width: 50%;
        margin-bottom: 10px;
    }
    
    .fishpond-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-links {
        flex-wrap: wrap;
    }
    
    .link-group {
        width: 50%;
        margin-bottom: 20px;
    }
    
    .product-attributes {
        grid-template-columns: 1fr;
    }
    
    .similar-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .product-grid {
        grid-template-columns: 1fr;
    }
    
    .link-group {
        width: 100%;
    }
    
    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }
    
    .copyright {
        margin-bottom: 10px;
    }
    
    .similar-grid {
        grid-template-columns: 1fr;
    }
}

/* 注册和登录页面样式 */
.register-page, .login-page {
    background-color: #f5f5f5;
    min-height: calc(100vh - 60px - 300px);
    display: flex;
    align-items: center;
    padding: 40px 0;
}

.auth-container {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 480px;
    margin: 0 auto;
    padding: 30px;
}

.auth-header {
    text-align: center;
    margin-bottom: 30px;
}

.auth-header h2 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.auth-header p {
    font-size: 14px;
    color: #666;
}

.auth-header p a {
    color: #ff6f06;
    font-weight: 500;
}

.login-tabs {
    display: flex;
    border-bottom: 1px solid #e5e5e5;
    margin-bottom: 20px;
}

.login-tab {
    flex: 1;
    text-align: center;
    padding: 10px 0;
    font-size: 16px;
    font-weight: 500;
    color: #666;
    cursor: pointer;
    position: relative;
}

.login-tab.active {
    color: #ff6f06;
}

.login-tab.active:after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #ff6f06;
}

.login-form {
    display: none;
}

.login-form.active {
    display: block;
}

.auth-form {
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
}

.form-group input {
    width: 100%;
    height: 44px;
    padding: 0 15px;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.form-group input:focus {
    border-color: #ff6f06;
    outline: none;
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-prefix {
    position: absolute;
    left: 15px;
    color: #999;
    font-size: 14px;
}

.input-group input {
    padding-left: 45px;
}

.toggle-password {
    position: absolute;
    right: 15px;
    color: #999;
    cursor: pointer;
}

.verify-code-btn {
    position: absolute;
    right: 1px;
    top: 1px;
    height: 42px;
    padding: 0 15px;
    background-color: #f5f5f5;
    border-radius: 0 4px 4px 0;
    font-size: 14px;
    color: #ff6f06;
    white-space: nowrap;
}

.verify-code-btn:hover {
    background-color: #ffe0cc;
}

.password-strength {
    margin-top: 8px;
}

.strength-bar {
    height: 4px;
    background-color: #e5e5e5;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 5px;
}

.strength-level {
    height: 100%;
    width: 0;
    transition: width 0.3s, background-color 0.3s;
}

.strength-level[data-level="0"] {
    width: 33%;
    background-color: #f56c6c;
}

.strength-level[data-level="1"] {
    width: 66%;
    background-color: #e6a23c;
}

.strength-level[data-level="2"] {
    width: 100%;
    background-color: #67c23a;
}

.strength-text {
    font-size: 12px;
    color: #999;
}

.form-tip {
    font-size: 12px;
    color: #999;
    margin-top: 5px;
}

.checkbox-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.checkbox-label input {
    width: auto;
    height: auto;
    margin-right: 5px;
}

.agreement-link, .forgot-link {
    font-size: 14px;
    color: #ff6f06;
    margin-left: 5px;
}

.auth-submit-btn {
    width: 100%;
    height: 44px;
    background-color: #ff6f06;
    color: #fff;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 500;
    transition: background-color 0.3s;
}

.auth-submit-btn:hover {
    background-color: #e56200;
}

.divider {
    position: relative;
    text-align: center;
    margin: 20px 0;
}

.divider:before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: #e5e5e5;
}

.divider span {
    position: relative;
    background-color: #fff;
    padding: 0 15px;
    font-size: 14px;
    color: #999;
}

.social-login {
    display: flex;
    justify-content: center;
}

.social-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background-color: #f5f5f5;
    margin: 0 10px;
    font-size: 20px;
    color: #666;
    transition: all 0.3s;
}

.social-icon:hover {
    background-color: #ffe0cc;
    color: #ff6f06;
}

/* 会员中心页面样式 */
.member-page {
    background-color: #f5f5f5;
    padding: 20px 0 40px;
}

.member-container {
    display: flex;
    gap: 20px;
}

.member-sidebar {
    width: 240px;
    flex-shrink: 0;
}

.member-content {
    flex: 1;
}

.user-profile {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.user-avatar {
    position: relative;
    width: 80px;
    height: 80px;
    margin: 0 auto 15px;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.avatar-edit {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 24px;
    height: 24px;
    background-color: #fff;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    cursor: pointer;
}

.avatar-edit i {
    font-size: 14px;
    color: #666;
}

.user-name {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 5px;
}

.user-level {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 15px;
}

.level-icon {
    color: #ff6f06;
    margin-right: 5px;
}

.level-text {
    font-size: 14px;
    color: #ff6f06;
}

.user-stats {
    display: flex;
    justify-content: space-around;
    border-top: 1px solid #e5e5e5;
    padding-top: 15px;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    color: #999;
}

.sidebar-menu {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.menu-group {
    margin-bottom: 10px;
}

.menu-title {
    padding: 15px;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    background-color: #f9f9f9;
    border-bottom: 1px solid #e5e5e5;
}

.menu-list {
    padding: 5px 0;
}

.menu-list li {
    padding: 0 15px;
}

.menu-list li a {
    display: flex;
    align-items: center;
    padding: 12px 0;
    font-size: 14px;
    color: #666;
    border-bottom: 1px solid #f5f5f5;
}

.menu-list li:last-child a {
    border-bottom: none;
}

.menu-list li.active a {
    color: #ff6f06;
}

.menu-list li a i {
    margin-right: 10px;
    font-size: 16px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.section-header h2 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.view-all {
    font-size: 14px;
    color: #999;
    display: flex;
    align-items: center;
}

.view-all i {
    margin-left: 5px;
}

.order-overview, .my-listings, .my-favorites, .browse-history {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.order-status-tabs {
    display: flex;
    justify-content: space-between;
    padding: 15px 0;
}

.status-tab {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}

.status-icon {
    font-size: 24px;
    color: #666;
    margin-bottom: 8px;
}

.status-text {
    font-size: 14px;
    color: #666;
}

.status-count {
    position: absolute;
    top: -5px;
    right: -10px;
    min-width: 18px;
    height: 18px;
    background-color: #ff6f06;
    color: #fff;
    border-radius: 9px;
    font-size: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 5px;
}

.listing-tabs {
    display: flex;
    border-bottom: 1px solid #e5e5e5;
    margin-bottom: 15px;
}

.listing-tab {
    padding: 10px 20px;
    font-size: 14px;
    color: #666;
    cursor: pointer;
    position: relative;
}

.listing-tab.active {
    color: #ff6f06;
}

.listing-tab.active:after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #ff6f06;
}

.listing-grid, .favorites-grid, .history-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.listing-stats {
    display: flex;
    margin-top: 8px;
    font-size: 12px;
    color: #999;
}

.view-count, .like-count, .comment-count {
    margin-right: 15px;
    display: flex;
    align-items: center;
}

.view-count i, .like-count i, .comment-count i {
    margin-right: 3px;
}

.listing-actions {
    display: flex;
    justify-content: space-between;
    padding: 10px 12px;
    border-top: 1px solid #f5f5f5;
}

.action-btn {
    padding: 5px 10px;
    font-size: 12px;
    border-radius: 4px;
    background-color: #f5f5f5;
    color: #666;
}

.edit-btn:hover {
    background-color: #e5e5e5;
    color: #333;
}

.promote-btn {
    background-color: #fff2e8;
    color: #ff6f06;
}

.promote-btn:hover {
    background-color: #ffe0cc;
}

.more-btn {
    padding: 5px 8px;
}

.add-listing-card {
    background-color: #f9f9f9;
    border-radius: 4px;
    border: 1px dashed #ddd;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    min-height: 200px;
    transition: all 0.3s;
}

.add-listing-card:hover {
    border-color: #ff6f06;
    background-color: #fff2e8;
}

.add-listing-card a {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    padding: 20px;
}

.add-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 10px;
    font-size: 24px;
    color: #ff6f06;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.add-text {
    font-size: 14px;
    color: #666;
}

/* 响应式设计 - 会员中心 */
@media (max-width: 992px) {
    .member-container {
        flex-direction: column;
    }
    
    .member-sidebar {
        width: 100%;
    }
    
    .listing-grid, .favorites-grid, .history-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .listing-grid, .favorites-grid, .history-grid {
        grid-template-columns: 1fr;
    }
    
    .order-status-tabs {
        flex-wrap: wrap;
    }
    
    .status-tab {
        width: 33.33%;
        margin-bottom: 15px;
    }
}