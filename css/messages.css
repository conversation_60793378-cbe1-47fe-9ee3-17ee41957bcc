/* 消息中心样式 - 数字鱼虚拟商品交易平台 */

/* 未读数量样式 */
.unread-count {
    color: #dc3545;
    font-weight: 600;
}

/* 消息筛选 */
.message-filters {
    display: flex;
    gap: 12px;
    margin-bottom: 30px;
    background: white;
    padding: 16px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    flex-wrap: wrap;
}

.filter-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border: 1px solid #dee2e6;
    background: white;
    border-radius: 8px;
    color: #6c757d;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.filter-btn:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
    color: #495057;
}

.filter-btn.active {
    background: linear-gradient(135deg, #ff6f06, #ff8533);
    border-color: #ff6f06;
    color: white;
    box-shadow: 0 2px 8px rgba(255, 111, 6, 0.3);
}

.filter-btn i {
    font-size: 16px;
}

.filter-btn .badge {
    background: #dc3545;
    color: white;
    font-size: 11px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 4px;
}

.filter-btn.active .badge {
    background: rgba(255, 255, 255, 0.3);
}

/* 消息列表 */
.messages-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.message-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.message-item:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.message-item.unread {
    border-left-color: #ff6f06;
    background: linear-gradient(135deg, #fff, #fff8f5);
}

.message-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    color: #6c757d;
}

.message-item.unread .message-icon {
    background: linear-gradient(135deg, #ff6f06, #ff8533);
    color: white;
}

.message-content {
    flex: 1;
    min-width: 0;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
    gap: 16px;
}

.message-title {
    font-size: 16px;
    font-weight: 600;
    color: #212529;
    margin: 0;
    line-height: 1.4;
}

.message-item.unread .message-title {
    color: #ff6f06;
}

.message-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
}

.message-time {
    font-size: 13px;
    color: #6c757d;
    white-space: nowrap;
}

.unread-dot {
    width: 8px;
    height: 8px;
    background: #dc3545;
    border-radius: 50%;
    flex-shrink: 0;
}

.message-text {
    font-size: 14px;
    color: #495057;
    line-height: 1.5;
    margin: 0;
}

.message-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex-shrink: 0;
}

.message-actions .action-btn {
    width: 36px;
    height: 36px;
    border: 1px solid #dee2e6;
    background: white;
    border-radius: 8px;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.message-actions .action-btn:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
}

.mark-read-btn:hover {
    background-color: #d4edda;
    border-color: #28a745;
    color: #28a745;
}

.delete-btn:hover {
    background-color: #f8d7da;
    border-color: #dc3545;
    color: #dc3545;
}

/* 动画效果 */
@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100px);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .message-filters {
        padding: 12px;
        gap: 8px;
    }
    
    .filter-btn {
        padding: 8px 12px;
        font-size: 13px;
    }
    
    .filter-btn span {
        display: none;
    }
    
    .filter-btn i {
        font-size: 18px;
    }
    
    .message-item {
        padding: 16px;
        gap: 12px;
    }
    
    .message-icon {
        width: 40px;
        height: 40px;
        font-size: 18px;
    }
    
    .message-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .message-meta {
        align-self: flex-end;
    }
    
    .message-actions {
        flex-direction: row;
    }
    
    .message-actions .action-btn {
        width: 32px;
        height: 32px;
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
    
    .message-filters {
        justify-content: center;
    }
    
    .message-item {
        padding: 12px;
        gap: 10px;
    }
    
    .message-icon {
        width: 36px;
        height: 36px;
        font-size: 16px;
    }
    
    .message-title {
        font-size: 15px;
    }
    
    .message-text {
        font-size: 13px;
    }
}
