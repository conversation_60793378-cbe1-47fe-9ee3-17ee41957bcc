/* 订单页面样式 - 数字鱼虚拟商品交易平台 */

/* 订单统计 */
.order-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.order-stats .stat-item {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 16px;
    transition: all 0.3s ease;
}

.order-stats .stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.order-stats .stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.order-stats .stat-icon.pending {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.order-stats .stat-icon.paid {
    background: linear-gradient(135deg, #17a2b8, #20c997);
}

.order-stats .stat-icon.completed {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.order-stats .stat-icon.refunded {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
}

/* 订单列表 */
.orders-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.order-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.order-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* 订单头部 */
.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.order-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.order-number {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
}

.order-time {
    font-size: 12px;
    color: #6c757d;
}

.order-status {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    color: white;
}

.status-pending {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.status-paid {
    background: linear-gradient(135deg, #17a2b8, #20c997);
}

.status-completed {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.status-cancelled {
    background: linear-gradient(135deg, #6c757d, #495057);
}

.status-refunded {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
}

/* 订单内容 */
.order-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
}

.product-info {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
}

.product-image {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-details {
    flex: 1;
}

.product-title {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
}

.product-title a {
    color: #212529;
    text-decoration: none;
}

.product-title a:hover {
    color: #ff6f06;
}

.seller-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.seller-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    object-fit: cover;
}

.seller-name {
    font-size: 13px;
    color: #6c757d;
}

.order-quantity {
    font-size: 13px;
    color: #6c757d;
}

.order-price {
    text-align: right;
    flex-shrink: 0;
}

.total-price {
    font-size: 18px;
    font-weight: 700;
    color: #ff6f06;
    margin-bottom: 4px;
}

.unit-price {
    font-size: 12px;
    color: #6c757d;
}

/* 订单操作 */
.order-actions {
    display: flex;
    gap: 12px;
    padding: 16px 20px;
    border-top: 1px solid #e9ecef;
    background-color: #f8f9fa;
    justify-content: flex-end;
}

.order-actions .action-btn {
    padding: 8px 16px;
    border: 1px solid #dee2e6;
    background: white;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    color: #495057;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.order-actions .action-btn:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

.order-actions .action-btn.primary {
    background: linear-gradient(135deg, #ff6f06, #ff8533);
    border-color: #ff6f06;
    color: white;
}

.order-actions .action-btn.primary:hover {
    background: linear-gradient(135deg, #e55a00, #ff6f06);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 111, 6, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .order-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    .order-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
        padding: 16px;
    }
    
    .order-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
        padding: 16px;
    }
    
    .product-info {
        width: 100%;
    }
    
    .order-price {
        text-align: left;
        width: 100%;
    }
    
    .order-actions {
        flex-wrap: wrap;
        padding: 12px 16px;
    }
    
    .order-actions .action-btn {
        flex: 1;
        min-width: 100px;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .order-stats {
        grid-template-columns: 1fr;
    }
    
    .product-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .product-image {
        width: 60px;
        height: 60px;
    }
    
    .order-actions {
        flex-direction: column;
    }
    
    .order-actions .action-btn {
        width: 100%;
    }
}
