<?php
// 先处理所有逻辑，再包含header
require_once 'includes/functions.php';

// 检查登录状态
if (!isLoggedIn()) {
    $_SESSION['error_message'] = '请先登录';
    redirect('login.php');
}

$currentUser = getCurrentUser();

// 处理清空历史记录
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'clear_all') {
        try {
            $stmt = $pdo->prepare("DELETE FROM browse_history WHERE user_id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            $_SESSION['success_message'] = '浏览历史已清空';
        } catch (Exception $e) {
            $_SESSION['error_message'] = '清空失败：' . $e->getMessage();
        }
        redirect('browse-history.php');
    } elseif ($_POST['action'] === 'remove_item') {
        $productId = intval($_POST['product_id'] ?? 0);
        if ($productId > 0) {
            try {
                $stmt = $pdo->prepare("DELETE FROM browse_history WHERE user_id = ? AND product_id = ?");
                $stmt->execute([$_SESSION['user_id'], $productId]);
                $_SESSION['success_message'] = '已从浏览历史中移除';
            } catch (Exception $e) {
                $_SESSION['error_message'] = '移除失败：' . $e->getMessage();
            }
        }
        redirect('browse-history.php');
    }
}

// 获取分页参数
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// 获取浏览历史（同一商品只显示最近的一次浏览记录）
try {
    $stmt = $pdo->prepare("
        SELECT p.*, u.nickname as seller_name, u.avatar as seller_avatar, 
               c.name as category_name, bh.created_at as browse_time
        FROM (
            SELECT product_id, MAX(created_at) as created_at, user_id
            FROM browse_history 
            WHERE user_id = ?
            GROUP BY product_id
        ) bh_latest
        JOIN browse_history bh ON bh.product_id = bh_latest.product_id 
                                AND bh.created_at = bh_latest.created_at 
                                AND bh.user_id = bh_latest.user_id
        JOIN products p ON bh.product_id = p.id 
        JOIN users u ON p.user_id = u.id 
        JOIN categories c ON p.category_id = c.id 
        WHERE p.status = 'active'
        ORDER BY bh.created_at DESC 
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([$_SESSION['user_id'], $limit, $offset]);
    $browseHistory = $stmt->fetchAll();
    
    // 获取总数（去重后的商品数量）
    $countStmt = $pdo->prepare("
        SELECT COUNT(DISTINCT product_id) 
        FROM browse_history bh
        JOIN products p ON bh.product_id = p.id
        WHERE bh.user_id = ? AND p.status = 'active'
    ");
    $countStmt->execute([$_SESSION['user_id']]);
    $totalHistory = $countStmt->fetchColumn();
    $totalPages = ceil($totalHistory / $limit);
    
} catch (Exception $e) {
    $browseHistory = [];
    $totalHistory = 0;
    $totalPages = 0;
}

// 获取浏览统计
try {
    $statsStmt = $pdo->prepare("
        SELECT 
            COUNT(DISTINCT product_id) as unique_products,
            COUNT(*) as total_views,
            DATE(MAX(created_at)) as last_browse_date
        FROM browse_history 
        WHERE user_id = ?
    ");
    $statsStmt->execute([$_SESSION['user_id']]);
    $stats = $statsStmt->fetch();
} catch (Exception $e) {
    $stats = [
        'unique_products' => 0,
        'total_views' => 0,
        'last_browse_date' => null
    ];
}

// 现在包含header，因为所有逻辑处理已完成
$pageTitle = '浏览历史';
$additionalCSS = ['css/member.css', 'css/browse-history.css'];
require_once 'includes/header.php';
?>

<!-- 主要内容区 -->
<main class="main member-page">
    <div class="container">
        <div class="member-container">
            <!-- 侧边栏 -->
            <?php include 'includes/member-sidebar.php'; ?>
            
            <!-- 主内容区 -->
            <div class="member-content">
                <!-- 页面标题 -->
                <div class="page-header">
                    <div class="page-title-section">
                        <h1 class="page-title">浏览历史</h1>
                        <p class="page-subtitle">
                            共浏览了 <?php echo $stats['unique_products']; ?> 个不同商品，
                            总计 <?php echo $stats['total_views']; ?> 次浏览
                        </p>
                    </div>
                    <div class="page-actions">
                        <?php if ($totalHistory > 0): ?>
                            <button class="btn btn-outline" onclick="clearAllHistory()">
                                <i class="bi bi-trash"></i>
                                <span>清空历史</span>
                            </button>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- 浏览统计 -->
                <div class="browse-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="bi bi-eye"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value"><?php echo $stats['unique_products']; ?></div>
                            <div class="stat-label">浏览商品数</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="bi bi-clock-history"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value"><?php echo $stats['total_views']; ?></div>
                            <div class="stat-label">总浏览次数</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="bi bi-calendar-event"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value">
                                <?php echo $stats['last_browse_date'] ? date('m-d', strtotime($stats['last_browse_date'])) : '--'; ?>
                            </div>
                            <div class="stat-label">最近浏览</div>
                        </div>
                    </div>
                </div>

                <!-- 浏览历史列表 -->
                <div class="history-section">
                    <?php if (empty($browseHistory)): ?>
                        <div class="empty-state">
                            <div class="empty-icon">
                                <i class="bi bi-clock-history"></i>
                            </div>
                            <h3>还没有浏览记录</h3>
                            <p>去逛逛，发现更多好商品吧！</p>
                            <a href="index.php" class="btn btn-primary">去逛逛</a>
                        </div>
                    <?php else: ?>
                        <div class="history-list">
                            <?php foreach ($browseHistory as $product): ?>
                                <div class="history-item">
                                    <div class="product-image">
                                        <?php 
                                        $images = json_decode($product['images'], true);
                                        $firstImage = $images ? (UPLOAD_PATH . $images[0]) : 'images/product-default.svg';
                                        ?>
                                        <img src="<?php echo $firstImage; ?>" alt="<?php echo htmlspecialchars($product['title']); ?>">
                                        <?php if ($product['is_virtual']): ?>
                                            <span class="product-tag">虚拟商品</span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="product-info">
                                        <h3 class="product-title">
                                            <a href="detail.php?id=<?php echo $product['id']; ?>">
                                                <?php echo htmlspecialchars($product['title']); ?>
                                            </a>
                                        </h3>
                                        <div class="product-meta">
                                            <span class="product-category"><?php echo htmlspecialchars($product['category_name']); ?></span>
                                            <span class="product-price"><?php echo formatPrice($product['price']); ?></span>
                                        </div>
                                        <div class="seller-info">
                                            <img src="<?php echo $product['seller_avatar'] ?: 'images/avatar-default.svg'; ?>" alt="卖家头像" class="seller-avatar">
                                            <span class="seller-name"><?php echo htmlspecialchars($product['seller_name']); ?></span>
                                        </div>
                                        <div class="product-stats">
                                            <span class="stat-item">
                                                <i class="bi bi-eye"></i>
                                                <?php echo $product['views']; ?>
                                            </span>
                                            <span class="stat-item">
                                                <i class="bi bi-heart"></i>
                                                <?php echo $product['likes']; ?>
                                            </span>
                                        </div>
                                    </div>
                                    
                                    <div class="browse-info">
                                        <div class="browse-time">
                                            <i class="bi bi-clock"></i>
                                            <span><?php echo timeAgo($product['browse_time']); ?></span>
                                        </div>
                                        <div class="item-actions">
                                            <a href="detail.php?id=<?php echo $product['id']; ?>" class="action-btn view-btn">
                                                <i class="bi bi-eye"></i>
                                                查看
                                            </a>
                                            <button class="action-btn remove-btn" onclick="removeHistoryItem(<?php echo $product['id']; ?>)">
                                                <i class="bi bi-x"></i>
                                                移除
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <!-- 分页 -->
                        <?php if ($totalPages > 1): ?>
                            <div class="pagination">
                                <?php if ($page > 1): ?>
                                    <a href="?page=<?php echo $page - 1; ?>" class="page-btn">
                                        <i class="bi bi-chevron-left"></i>
                                    </a>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                    <a href="?page=<?php echo $i; ?>" 
                                       class="page-btn <?php echo $i === $page ? 'active' : ''; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                <?php endfor; ?>
                                
                                <?php if ($page < $totalPages): ?>
                                    <a href="?page=<?php echo $page + 1; ?>" class="page-btn">
                                        <i class="bi bi-chevron-right"></i>
                                    </a>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
function clearAllHistory() {
    if (confirm('确定要清空所有浏览历史吗？此操作不可恢复。')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type="hidden" name="action" value="clear_all">';
        document.body.appendChild(form);
        form.submit();
    }
}

function removeHistoryItem(productId) {
    if (confirm('确定要从浏览历史中移除这个商品吗？')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="remove_item">
            <input type="hidden" name="product_id" value="${productId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php require_once 'includes/footer.php'; ?>
